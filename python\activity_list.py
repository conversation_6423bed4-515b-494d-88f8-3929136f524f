import requests
import json
from datetime import datetime

def fetch_activity_list(cookie, activity_type=1, activity_status=3, page_size=10, page=1):
    """
    爬取快手小店活动列表数据的函数
    
    参数:
    cookie: str - 请求的cookie字符串
    activity_type: int - 活动类型
    activity_status: int - 活动状态
    page_size: int - 每页数据条数
    page: int - 当前页码
    
    返回:
    dict - 处理后的活动列表数据
    """
    url = "https://cps.kwaixiaodian.com/distribute/pc/investment/activity/list"
    
    # 构建请求参数
    payload = {
        "activityType": activity_type,
        "activityStatus": activity_status,
        "limit": page_size,
        "offset": (page - 1) * page_size
    }
    
    headers = {
        "authority": "cps.kwaixiaodian.com",
        "accept": "application/json",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "cookie": cookie,
        "kpf": "PC_WEB",
        "kpn": "KWAIXIAODIAN",
        "referer": "https://cps.kwaixiaodian.com/pc/leader/base/order-manage",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"
    }
    
    try:
        response = requests.get(url, params=payload, headers=headers)
        response.raise_for_status()
        
        # 解析响应数据
        result = response.json()
        
        # 检查响应是否成功
        if result.get("result") != 1:
            return {"error": result.get("error_msg", "未知错误")}
        
        # 提取活动列表
        activities = result.get("data", [])
        
        # 处理数据，添加可读的时间格式
        for activity in activities:
            if "createTime" in activity and activity["createTime"] > 0:
                timestamp = activity["createTime"]
                activity["createTimeFormatted"] = datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')
            
            if "startTime" in activity and activity["startTime"] > 0:
                timestamp = activity["startTime"]
                activity["startTimeFormatted"] = datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')
            
            if "endTime" in activity and activity["endTime"] > 0:
                timestamp = activity["endTime"]
                activity["endTimeFormatted"] = datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')
        
        return {
            "activities": activities,
            "total": result.get("total", 0),
            "page": page,
            "page_size": page_size
        }
        
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}

def get_all_activities(cookie, activity_type=1, activity_status=3, page_size=10, max_pages=None):
    """
    获取所有活动列表数据（分页获取）
    
    参数:
    cookie: str - 请求的cookie字符串
    activity_type: int - 活动类型
    activity_status: int - 活动状态
    page_size: int - 每页数据条数
    max_pages: int - 最大获取页数，None表示获取所有页
    
    返回:
    list - 所有活动列表数据
    """
    all_activities = []
    current_page = 1
    total = None
    
    while True:
        result = fetch_activity_list(
            cookie=cookie,
            activity_type=activity_type,
            activity_status=activity_status,
            page_size=page_size,
            page=current_page
        )
        
        if "error" in result:
            print(f"获取第{current_page}页数据时出错: {result['error']}")
            break
        
        activities = result.get("activities", [])
        all_activities.extend(activities)
        
        if total is None:
            total = result.get("total", 0)
            print(f"总活动数: {total}")
        
        print(f"已获取第{current_page}页数据, 当前共{len(all_activities)}条活动")
        
        # 判断是否继续获取下一页
        if max_pages and current_page >= max_pages:
            print(f"已达到最大页数限制: {max_pages}页")
            break
        
        if len(all_activities) >= total:
            print("已获取所有活动数据")
            break
        
        current_page += 1
        # 添加延迟，避免请求过于频繁
        import time
        time.sleep(1)
    
    return all_activities

def save_activities_to_file(activities, filename="activities.json"):
    """
    将活动列表数据保存到文件
    
    参数:
    activities: list - 活动列表数据
    filename: str - 保存的文件名
    """
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(activities, f, ensure_ascii=False, indent=2)
    print(f"活动列表数据已保存至 {filename}")

# 使用示例
if __name__ == "__main__":
    # 示例cookie，实际使用时需要替换
    cookie = "_did=web_6109536701C516C8; did=web_m5p31mdccrdqpmzfrlb5k0pb18ya7tn1; sid=kuaishou.shop.b; bUserId=1000040627146; userId=2885180614; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAE3tBozHFVZOn8pljtRICePPkO5r0KudHnwAKYs6cKFeR4iZ2kuwe3aY_uTEPJciQgEY7R3cPMo-Zs_Z83AQerRWidPl8vDub4WQdp_YD1EFwv1BmSppwUKKxrfyFD1MAMI0oFHlLPVdpSQK-m5yfSBSPE5AvYJkQ1QKZyRMXgmVs1RE4_wHZLmfACJibZ6mUJZC8jixMJ0KY1-1xjH83blGhISqzP1A3OvUjEirVmZwcinEd4iIJfDUuUOMJQnmTnc3NWgIAQd3LEVHp0oaIZSZ-gNVIyEKAUwAQ; kuaishou.shop.b_ph=70bed43b4a2ba56eb32fc3df6bcdff0a7ba5"
    
    # 获取单页数据示例
    result = fetch_activity_list(cookie)
    if "error" not in result:
        print(f"获取到 {len(result.get('activities', []))} 条活动数据")
    
    # 获取所有数据示例
    all_activities = get_all_activities(cookie)
    save_activities_to_file(all_activities) 