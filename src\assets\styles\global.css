/* 全局样式 */
.el-container {
  width: 100%;
  height: 100%;
}

.el-main {
  width: 100%;
  padding: 0;
}

.el-card {
  width: 100%;
}

.el-table {
  width: 100% !important;
}

.el-row {
  width: 100%;
}

/* 确保页面内容铺满 */
.page-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  overflow: auto;
}

/* 卡片样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 搜索区域 */
.search-area {
  margin-bottom: 20px;
  width: 100%;
}

/* 分页容器 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
