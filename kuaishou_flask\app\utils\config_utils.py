"""
系统配置工具函数
"""
from app.utils.db_utils import get_connection

def get_system_config(config_key, default_value=''):
    """
    获取系统配置值
    
    Args:
        config_key: 配置键名
        default_value: 默认值
        
    Returns:
        str: 配置值
    """
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        query = f"SELECT config_value FROM system_config WHERE config_key = '{config_key}'"
        cursor.execute(query)
        result = cursor.fetchone()
        
        if result:
            return result['config_value']
        else:
            return default_value
            
    except Exception as e:
        print(f"获取系统配置失败: {str(e)}")
        return default_value
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def get_kuaishou_cookie():
    """
    获取快手小店Cookie
    
    Returns:
        str: Cookie值
    """
    return get_system_config('kuaishou_cookie', '')

def set_system_config(config_key, config_value, config_desc=''):
    """
    设置系统配置值
    
    Args:
        config_key: 配置键名
        config_value: 配置值
        config_desc: 配置描述
        
    Returns:
        bool: 是否成功
    """
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查配置是否存在
        check_query = f"SELECT id FROM system_config WHERE config_key = '{config_key}'"
        cursor.execute(check_query)
        existing_config = cursor.fetchone()
        
        safe_key = config_key.replace("'", "''")
        safe_value = config_value.replace("'", "''")
        safe_desc = config_desc.replace("'", "''")
        
        if existing_config:
            # 更新现有配置
            update_query = f"""
            UPDATE system_config 
            SET config_value = '{safe_value}', 
                config_desc = '{safe_desc}',
                update_time = NOW()
            WHERE config_key = '{safe_key}'
            """
            cursor.execute(update_query)
        else:
            # 创建新配置
            insert_query = f"""
            INSERT INTO system_config (config_key, config_value, config_desc, create_time, update_time)
            VALUES ('{safe_key}', '{safe_value}', '{safe_desc}', NOW(), NOW())
            """
            cursor.execute(insert_query)
        
        connection.commit()
        return True
        
    except Exception as e:
        print(f"设置系统配置失败: {str(e)}")
        if connection:
            connection.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
