from flask import Blueprint, request, jsonify
from app.utils.db_utils import get_connection
from app.utils.config_utils import get_kuaishou_cookie
import sys
import os
import datetime

# 添加python目录到系统路径，以便导入团长爬虫模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../python')))
from team_leader_crawler import TeamLeaderCrawler

team_leader_bp = Blueprint('team_leader', __name__, url_prefix='/api/team-leader')

# Cookie配置现在从数据库获取

@team_leader_bp.route('/search_kuaishou', methods=['GET'])
def search_kuaishou_team_leaders():
    """搜索快手团长"""
    try:
        leader_name = request.args.get('leader_name', '')
        leader_id = request.args.get('leader_id', '')
        limit = int(request.args.get('limit', 10))
        
        # 创建团长爬虫实例，从数据库获取cookie
        cookie = get_kuaishou_cookie()
        crawler = TeamLeaderCrawler(cookie)
        
        # 使用关键词搜索
        keyword = leader_id if leader_id else leader_name
        if not keyword:
            return jsonify({
                'code': 400,
                'message': '请提供团长名称或ID',
                'data': None
            }), 400
        
        # 调用API搜索方法
        team_leaders = crawler.search_team_leaders_for_api(keyword, limit)
        
        return jsonify({
            'code': 0,
            'message': '搜索成功',
            'data': team_leaders
        })
    except Exception as e:
        print(f"搜索快手团长失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'搜索快手团长失败: {str(e)}',
            'data': None
        }), 500

@team_leader_bp.route('/list', methods=['GET'])
def get_team_leader_list():
    """获取团长列表"""
    connection = None
    cursor = None
    try:
        # 获取查询参数
        leader_name = request.args.get('leader_name', '')
        leader_id = request.args.get('leader_id', '')
        business_contact = request.args.get('business_contact', '')
        leader_type = request.args.get('leader_type', '')  # public, shared, special
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        offset = (page - 1) * page_size
        
        # 连接数据库
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 构建查询条件
        conditions = []
        params = []
        
        if leader_name:
            conditions.append("leader_name LIKE %s")
            params.append(f"%{leader_name}%")
        
        if leader_id:
            conditions.append("leader_id LIKE %s")
            params.append(f"%{leader_id}%")
        
        # 根据团长类型筛选
        if leader_type == 'public':
            # 公海团长没有商务所属人且不是其他类型
            conditions.append("(business_contact IS NULL OR business_contact = '')")
            conditions.append("leader_type NOT IN ('special', 'shared', 'exclusive')")
        elif leader_type == 'shared':
            # 共享团长，只根据leader_type字段筛选
            conditions.append("leader_type = 'shared'")
            # 如果指定了商务，则查询该商务参与共享的团长
            if business_contact:
                conditions.append("shared_businesses LIKE %s")
                params.append(f"%{business_contact}%")
        elif leader_type == 'special':
            # 专享团长，只显示当前商务对接的
            conditions.append("leader_type = 'special'")
            if business_contact:
                conditions.append("business_contact = %s")
                params.append(business_contact)
        elif leader_type == 'exclusive':
            # 专属团长，只显示当前商务对接的
            conditions.append("leader_type = 'exclusive'")
            if business_contact:
                conditions.append("business_contact = %s")
                params.append(business_contact)
        
        # 构建WHERE子句
        where_clause = "1=1"
        if conditions:
            where_clause = " AND ".join(conditions)
        
        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM team_leader WHERE {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 查询数据
        query = f"SELECT * FROM team_leader WHERE {where_clause} ORDER BY update_time DESC LIMIT %s OFFSET %s"
        params.extend([page_size, offset])
        cursor.execute(query, params)
        leaders = cursor.fetchall()
        
        # 格式化返回数据
        result = {
            'code': 0,
            'message': '成功',
            'data': {
                'list': leaders,
                'total': total,
                'page': page,
                'page_size': page_size
            }
        }
        
        return jsonify(result)
    except Exception as e:
        print(f"获取团长列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取团长列表失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@team_leader_bp.route('/detail/<string:leader_id>', methods=['GET'])
def get_team_leader_detail(leader_id):
    """获取团长详情"""
    connection = None
    cursor = None
    try:
        # 连接数据库
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询团长详情
        query = """
        SELECT 
            tl.*,
            tlb.business_contact,
            tlb.business_assign_time
        FROM team_leader tl
        LEFT JOIN team_leader_business tlb ON tl.leader_id = tlb.leader_id
        WHERE tl.leader_id = %s
        """
        cursor.execute(query, [leader_id])
        team_leader = cursor.fetchone()
        
        if not team_leader:
            return jsonify({
                'code': 404,
                'message': '团长不存在',
                'data': None
            }), 404
        
        return jsonify({
            'code': 0,
            'message': '获取团长详情成功',
            'data': team_leader
        })
    except Exception as e:
        print(f"获取团长详情失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取团长详情失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@team_leader_bp.route('/add_to_business', methods=['POST'])
def add_team_leader_to_business():
    """只负责添加团长到商务，所有判断已前置到前端，逻辑与达人一致"""
    connection = None
    cursor = None
    try:
        data = request.json
        if not data or 'leader_id' not in data or 'business_contact' not in data:
            return jsonify({'code': 400, 'message': '请求数据不能为空或缺少必要参数', 'data': None}), 400
        leader_id = data['leader_id']
        business_contact = data['business_contact']
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        # 获取商务信息
        business_query = "SELECT * FROM business_user WHERE name = %s"
        cursor.execute(business_query, [business_contact])
        business = cursor.fetchone()
        if not business:
            return jsonify({'code': 404, 'message': f'商务"{business_contact}"不存在', 'data': None}), 404
        business_name = business['name']
        # 检查团长是否存在
        check_query = "SELECT * FROM team_leader WHERE leader_id = %s"
        cursor.execute(check_query, [leader_id])
        leader = cursor.fetchone()
        if not leader:
            # 非入库团长，必须有完整数据
            if 'leader_data' not in data:
                return jsonify({'code': 400, 'message': '团长不存在，需提供团长数据', 'data': None}), 400
            leader_data = data['leader_data']
            if not leader_data.get('leader_name'):
                return jsonify({'code': 400, 'message': '团长名称不能为空', 'data': None}), 400
            leader_data['leader_type'] = 'special'
            leader_data['business_contact'] = business_name
            leader_data['shared_businesses'] = ''
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            leader_data['update_time'] = current_time
            fields = [field for field in leader_data.keys() if leader_data[field] is not None]
            placeholders = ', '.join(['%s'] * len(fields))
            field_str = ', '.join(fields)
            query = f"INSERT INTO team_leader ({field_str}) VALUES ({placeholders})"
            params = [leader_data[field] for field in fields]
            cursor.execute(query, params)
            # 添加商务与团长的关联记录
            relation_query = """
            INSERT INTO team_leader_business 
            (business_contact, leader_id, business_assign_time) 
            VALUES (%s, %s, NOW())
            ON DUPLICATE KEY UPDATE update_time = NOW()
            """
            cursor.execute(relation_query, [business_name, leader_id])
            connection.commit()
            return jsonify({'code': 0, 'message': '添加团长成功', 'data': None})
        else:
            # 已入库团长
            if leader['leader_type'] == 'public':
                # 公海变共享
                shared_businesses = business_name
                update_query = """
                    UPDATE team_leader SET leader_type='shared', business_contact=%s, shared_businesses=%s, update_time=NOW()
                    WHERE leader_id=%s
                """
                cursor.execute(update_query, [business_name, shared_businesses, leader_id])
            elif leader['leader_type'] == 'shared':
                # 共享追加商务
                shared_businesses = leader.get('shared_businesses', '')
                business_list = [b for b in shared_businesses.split(',') if b]
                if business_name not in business_list:
                    business_list.append(business_name)
                shared_businesses = ','.join(business_list)
                update_query = """
                    UPDATE team_leader SET shared_businesses=%s, update_time=NOW()
                    WHERE leader_id=%s
                """
                cursor.execute(update_query, [shared_businesses, leader_id])
            # 添加商务与团长的关联记录
            relation_query = """
            INSERT INTO team_leader_business 
            (business_contact, leader_id, business_assign_time) 
            VALUES (%s, %s, NOW())
            ON DUPLICATE KEY UPDATE update_time = NOW()
            """
            cursor.execute(relation_query, [business_name, leader_id])
            connection.commit()
            return jsonify({'code': 0, 'message': '添加团长成功', 'data': None})
    except Exception as e:
        print(f"添加团长到商务失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({'code': 500, 'message': f'添加团长到商务失败: {str(e)}', 'data': None}), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@team_leader_bp.route('/update', methods=['POST'])
def update_team_leader():
    """更新团长信息"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data or 'leader_id' not in data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空或缺少leader_id',
                'data': None
            }), 400

        leader_id = data['leader_id']

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 检查团长是否存在
        check_query = "SELECT COUNT(*) as count FROM team_leader WHERE leader_id = %s"
        cursor.execute(check_query, [leader_id])
        result = cursor.fetchone()
        if not result or result['count'] == 0:
            return jsonify({
                'code': 404,
                'message': '团长不存在',
                'data': None
            }), 404

        # 准备更新数据
        update_fields = [
            'leader_name', 'contact_name', 'contact_phone', 'wechat', 'shipping_address',
            'tags', 'business_contact', 'shared_businesses', 'remarks', 'leader_type'
        ]

        # 构建SQL语句
        update_parts = []
        params = []

        for field in update_fields:
            if field in data and data[field] is not None:
                update_parts.append(f"{field} = %s")
                params.append(data[field])

        if not update_parts:
            return jsonify({
                'code': 400,
                'message': '没有提供要更新的字段',
                'data': None
            }), 400

        # 添加更新时间
        update_parts.append("update_time = NOW()")

        # 添加leader_id作为WHERE条件
        params.append(leader_id)

        # 执行更新
        query = f"UPDATE team_leader SET {', '.join(update_parts)} WHERE leader_id = %s"
        cursor.execute(query, params)
        connection.commit()

        return jsonify({
            'code': 0,
            'message': '更新团长信息成功',
            'data': None
        })
    except Exception as e:
        print(f"更新团长信息失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新团长信息失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@team_leader_bp.route('/update-business', methods=['POST'])
def update_team_leader_business():
    """更新团长商务信息"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data or 'leader_id' not in data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空或缺少leader_id',
                'data': None
            }), 400

        leader_id = data['leader_id']

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 检查团长是否存在
        check_query = "SELECT leader_type FROM team_leader WHERE leader_id = %s"
        cursor.execute(check_query, [leader_id])
        result = cursor.fetchone()
        if not result:
            return jsonify({
                'code': 404,
                'message': '团长不存在',
                'data': None
            }), 404

        leader_type = result['leader_type']

        # 准备更新数据
        update_parts = []
        params = []

        # 根据团长类型更新不同字段
        if leader_type in ['exclusive', 'special']:
            # 专属和特殊团长只能更新business_contact
            if 'business_contact' in data:
                update_parts.append("business_contact = %s")
                params.append(data['business_contact'])
        elif leader_type == 'shared':
            # 共享团长只能更新shared_businesses
            if 'shared_businesses' in data:
                update_parts.append("shared_businesses = %s")
                params.append(data['shared_businesses'])

        # 支持更新团长类型
        if 'leader_type' in data:
            update_parts.append("leader_type = %s")
            params.append(data['leader_type'])
            # 如果类型改变，需要清理相应字段
            if data['leader_type'] == 'shared':
                update_parts.append("business_contact = NULL")
            elif data['leader_type'] in ['exclusive', 'special']:
                update_parts.append("shared_businesses = NULL")

        if not update_parts:
            return jsonify({
                'code': 400,
                'message': '没有提供要更新的字段',
                'data': None
            }), 400

        # 添加更新时间
        update_parts.append("update_time = NOW()")

        # 添加leader_id作为WHERE条件
        params.append(leader_id)

        # 执行更新
        query = f"UPDATE team_leader SET {', '.join(update_parts)} WHERE leader_id = %s"
        cursor.execute(query, params)
        connection.commit()

        return jsonify({
            'code': 0,
            'message': '更新团长商务信息成功',
            'data': None
        })
    except Exception as e:
        print(f"更新团长商务信息失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新团长商务信息失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@team_leader_bp.route('/remove_from_business', methods=['POST'])
def remove_team_leader_from_business():
    """从商务中移除团长"""
    connection = None
    cursor = None
    try:
        data = request.json
        if not data or 'leader_id' not in data or 'business_contact' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供团长ID和商务名称',
                'data': None
            }), 400
        
        leader_id = data['leader_id']
        business_contact = data['business_contact']
        
        # 连接数据库
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 获取商务信息
        business_query = "SELECT * FROM business_user WHERE name = %s"
        cursor.execute(business_query, [business_contact])
        business = cursor.fetchone()
        
        if not business:
            return jsonify({
                'code': 404,
                'message': f'商务"{business_contact}"不存在',
                'data': None
            }), 404
        
        # 使用商务的name作为business_contact
        business_name = business['name']
        
        # 检查团长是否存在
        check_query = "SELECT * FROM team_leader WHERE leader_id = %s"
        cursor.execute(check_query, [leader_id])
        team_leader = cursor.fetchone()
        
        if not team_leader:
            return jsonify({
                'code': 404,
                'message': '团长不存在',
                'data': None
            }), 404
        
        # 判断团长类型
        leader_type = team_leader['leader_type']
        
        # 如果是专享或专属团长，直接移至公海
        if leader_type in ['special', 'exclusive']:
            if team_leader['business_contact'] != business_name:
                return jsonify({
                    'code': 400,
                    'message': '您不是该团长的对接商务，无法移除',
                    'data': None
                }), 400
            
            # 更新为公海团长
            update_query = """
            UPDATE team_leader 
            SET leader_type = 'public',
                business_contact = NULL,
                shared_businesses = NULL,
                update_time = NOW()
            WHERE leader_id = %s
            """
            cursor.execute(update_query, [leader_id])
            
            # 删除关联记录
            delete_relation_query = """
            DELETE FROM team_leader_business
            WHERE leader_id = %s AND business_contact = %s
            """
            cursor.execute(delete_relation_query, [leader_id, business_name])
            
            connection.commit()
            
            return jsonify({
                'code': 0,
                'message': '成功将团长移除至公海',
                'data': None
            })
        
        # 如果是共享团长，只删除当前商务的关联
        if leader_type == 'shared':
            # 检查是否有关联
            check_relation_query = """
            SELECT COUNT(*) as count FROM team_leader_business 
            WHERE leader_id = %s AND business_contact = %s
            """
            cursor.execute(check_relation_query, [leader_id, business_name])
            relation_exists = cursor.fetchone()['count'] > 0
            
            # 检查shared_businesses中是否包含该商务
            shared_businesses_contains = False
            if team_leader.get('shared_businesses'):
                shared_businesses_list = team_leader['shared_businesses'].split(',')
                shared_businesses_contains = business_name in shared_businesses_list
            
            if not relation_exists and not shared_businesses_contains:
                return jsonify({
                    'code': 400,
                    'message': '您不是该团长的对接商务，无法移除',
                    'data': None
                }), 400
            
            # 删除关联记录
            delete_relation_query = """
            DELETE FROM team_leader_business
            WHERE leader_id = %s AND business_contact = %s
            """
            cursor.execute(delete_relation_query, [leader_id, business_name])
            
            # 检查是否还有其他商务关联
            count_query = """
            SELECT COUNT(*) as count FROM team_leader_business 
            WHERE leader_id = %s
            """
            cursor.execute(count_query, [leader_id])
            remaining_count = cursor.fetchone()['count']
            
            # 更新shared_businesses字段，移除当前商务
            shared_businesses = team_leader.get('shared_businesses', '')
            if shared_businesses:
                # 将商务列表拆分为数组
                business_list = shared_businesses.split(',')
                # 移除当前商务
                if business_name in business_list:
                    business_list.remove(business_name)
                # 重新组合商务列表
                shared_businesses = ','.join(business_list)
            
            # 如果没有其他商务关联，则将团长移至公海
            if remaining_count == 0 and not shared_businesses:
                update_query = """
                UPDATE team_leader 
                SET leader_type = 'public',
                    business_contact = NULL,
                    shared_businesses = NULL,
                    update_time = NOW()
                WHERE leader_id = %s
                """
                cursor.execute(update_query, [leader_id])
            else:
                # 更新shared_businesses字段
                update_query = """
                UPDATE team_leader 
                SET shared_businesses = %s,
                    update_time = NOW()
                WHERE leader_id = %s
                """
                cursor.execute(update_query, [shared_businesses, leader_id])
            
            connection.commit()
            
            return jsonify({
                'code': 0,
                'message': '成功移除商务关联',
                'data': None
            })
        
        return jsonify({
            'code': 400,
            'message': '无法移除该团长',
            'data': None
        }), 400
        
    except Exception as e:
        if connection:
            connection.rollback()
        print(f"从商务中移除团长失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'从商务中移除团长失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@team_leader_bp.route('/business_relations/<leader_id>', methods=['GET'])
def get_team_leader_business_relations(leader_id):
    """获取团长与商务的关联信息"""
    connection = None
    cursor = None
    try:
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询团长信息
        leader_query = "SELECT * FROM team_leader WHERE leader_id = %s"
        cursor.execute(leader_query, [leader_id])
        leader = cursor.fetchone()
        
        if not leader:
            return jsonify({
                'code': 404,
                'message': '团长不存在',
                'data': None
            }), 404
        
        # 初始化关联信息列表
        relations = []
        
        # 查询商务关联信息
        try:
            relations_query = """
            SELECT tlb.business_contact, bu.name as business_name, tlb.business_assign_time, tlb.update_time
            FROM team_leader_business tlb
            LEFT JOIN business_user bu ON tlb.business_contact = bu.name
            WHERE tlb.leader_id = %s
            ORDER BY tlb.business_assign_time DESC
            """
            cursor.execute(relations_query, [leader_id])
            relations = cursor.fetchall() or []
        except Exception as e:
            print(f"查询商务关联信息失败: {str(e)}")
            # 继续执行，尝试从shared_businesses字段获取信息
        
        # 如果没有找到关联记录，但是有shared_businesses字段，则从该字段获取商务信息
        if not relations and leader.get('shared_businesses'):
            try:
                shared_businesses = leader['shared_businesses'].split(',')
                for business_contact in shared_businesses:
                    if business_contact:
                        # 查询商务信息
                        try:
                            business_query = """
                            SELECT name as business_name FROM business_user WHERE name = %s
                            """
                            cursor.execute(business_query, [business_contact])
                            business = cursor.fetchone()
                            
                            relations.append({
                                'business_contact': business_contact,
                                'business_name': business['business_name'] if business else business_contact,
                                'business_assign_time': leader['update_time'],
                                'update_time': leader['update_time']
                            })
                        except Exception as e:
                            print(f"查询商务信息失败: {str(e)}")
                            # 添加没有详细信息的商务
                            relations.append({
                                'business_contact': business_contact,
                                'business_name': business_contact,
                                'business_assign_time': leader['update_time'],
                                'update_time': leader['update_time']
                            })
            except Exception as e:
                print(f"处理shared_businesses字段失败: {str(e)}")
        
        # 确保返回的是列表，即使为空
        relations = relations or []
        
        return jsonify({
            'code': 0,
            'message': '成功',
            'data': {
                'leader': leader,
                'relations': relations
            }
        })
    except Exception as e:
        print(f"获取团长商务关联信息失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取团长商务关联信息失败: {str(e)}',
            'data': {
                'leader': {},
                'relations': []
            }
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@team_leader_bp.route('/check_team_leader_status', methods=['GET'])
def check_team_leader_status():
    """检查团长状态并进行分类转换（定时任务调用）"""
    connection = None
    cursor = None
    try:
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 1. 专享团长15天累计GMV大于1万升级为专属，小于则降至公海
        check_special_query = """
        SELECT t.*, 
               DATEDIFF(NOW(), t.update_time) as days_assigned,
               COALESCE(SUM(o.gmv), 0) as total_gmv
        FROM team_leader t
        LEFT JOIN orders o ON t.leader_id = o.leader_id AND o.create_time >= t.update_time
        WHERE t.leader_type = 'special'
        GROUP BY t.leader_id
        HAVING days_assigned >= 15
        """
        cursor.execute(check_special_query)
        special_leaders = cursor.fetchall()
        
        for leader in special_leaders:
            if leader['total_gmv'] >= 10000:  # 15天GMV大于1万
                # 升级为专属团长
                update_query = """
                UPDATE team_leader 
                SET leader_type = 'exclusive',
                    update_time = NOW()
                WHERE leader_id = %s
                """
                cursor.execute(update_query, [leader['leader_id']])
            else:  # 15天GMV小于1万
                # 降至公海
                update_query = """
                UPDATE team_leader 
                SET leader_type = 'public',
                    business_contact = NULL,
                    shared_businesses = NULL,
                    update_time = NOW()
                WHERE leader_id = %s
                """
                cursor.execute(update_query, [leader['leader_id']])
                
                # 删除关联记录
                delete_relation_query = """
                DELETE FROM team_leader_business
                WHERE leader_id = %s
                """
                cursor.execute(delete_relation_query, [leader['leader_id']])
        
        # 2. 专属团长30天最少10万，否则降至公海
        check_exclusive_query = """
        SELECT t.*, 
               DATEDIFF(NOW(), t.update_time) as days_assigned,
               COALESCE(SUM(o.gmv), 0) as total_gmv
        FROM team_leader t
        LEFT JOIN orders o ON t.leader_id = o.leader_id AND o.create_time >= t.update_time
        WHERE t.leader_type = 'exclusive'
        GROUP BY t.leader_id
        HAVING days_assigned >= 30
        """
        cursor.execute(check_exclusive_query)
        exclusive_leaders = cursor.fetchall()
        
        for leader in exclusive_leaders:
            if leader['total_gmv'] < 100000:  # 30天GMV小于10万
                # 降至公海
                update_query = """
                UPDATE team_leader 
                SET leader_type = 'public',
                    business_contact = NULL,
                    shared_businesses = NULL,
                    update_time = NOW()
                WHERE leader_id = %s
                """
                cursor.execute(update_query, [leader['leader_id']])
                
                # 删除关联记录
                delete_relation_query = """
                DELETE FROM team_leader_business
                WHERE leader_id = %s
                """
                cursor.execute(delete_relation_query, [leader['leader_id']])
        
        # 3. 共享团长30天销售额达到5万的商务获得专享权
        check_shared_query = """
        SELECT t.leader_id, t.shared_businesses,
               b.business_contact,
               DATEDIFF(NOW(), b.business_assign_time) as days_assigned,
               COALESCE(SUM(o.gmv), 0) as total_gmv
        FROM team_leader t
        JOIN team_leader_business b ON t.leader_id = b.leader_id
        LEFT JOIN orders o ON t.leader_id = o.leader_id AND o.business_contact = b.business_contact AND o.create_time >= b.business_assign_time
        WHERE t.leader_type = 'shared'
        GROUP BY t.leader_id, b.business_contact
        HAVING days_assigned >= 30
        """
        cursor.execute(check_shared_query)
        shared_leader_relations = cursor.fetchall()
        
        # 按团长ID分组处理
        leader_groups = {}
        for relation in shared_leader_relations:
            leader_id = relation['leader_id']
            if leader_id not in leader_groups:
                leader_groups[leader_id] = []
            leader_groups[leader_id].append(relation)
        
        for leader_id, relations in leader_groups.items():
            # 找出销售额最高的商务
            max_gmv_relation = max(relations, key=lambda x: x['total_gmv'])
            
            if max_gmv_relation['total_gmv'] >= 50000:  # 有商务30天GMV达到5万
                # 该商务获得专享权
                update_query = """
                UPDATE team_leader 
                SET leader_type = 'special',
                    business_contact = %s,
                    shared_businesses = NULL,
                    update_time = NOW()
                WHERE leader_id = %s
                """
                cursor.execute(update_query, [max_gmv_relation['business_contact'], leader_id])
                
                # 清除其他商务关联
                delete_other_relation_query = """
                DELETE FROM team_leader_business
                WHERE leader_id = %s AND business_contact != %s
                """
                cursor.execute(delete_other_relation_query, [leader_id, max_gmv_relation['business_contact']])
            else:
                # 所有商务都未达到5万销售额
                # 清除所有商务关联，降至公海
                update_query = """
                UPDATE team_leader 
                SET leader_type = 'public',
                    business_contact = NULL,
                    shared_businesses = NULL,
                    update_time = NOW()
                WHERE leader_id = %s
                """
                cursor.execute(update_query, [leader_id])
                
                # 清除关联表中的记录
                delete_relation_query = """
                DELETE FROM team_leader_business
                WHERE leader_id = %s
                """
                cursor.execute(delete_relation_query, [leader_id])
        
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '团长状态检查完成',
            'data': None
        })
    except Exception as e:
        print(f"检查团长状态失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'检查团长状态失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@team_leader_bp.route('/batch_status', methods=['POST'])
def batch_team_leader_status():
    """批量查询团长本地状态和共享人数和归属商务"""
    data = request.json
    ids = data.get('ids', [])
    if not ids:
        return jsonify({'code': 400, 'message': 'ids不能为空', 'data': None}), 400

    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        format_strings = ','.join(['%s'] * len(ids))
        query = f"""
            SELECT 
                leader_id, 
                leader_type, 
                shared_businesses,
                business_contact
            FROM team_leader 
            WHERE leader_id IN ({format_strings})
        """
        cursor.execute(query, ids)
        rows = cursor.fetchall()
        result = {}
        for row in rows:
            shared_count = len(row['shared_businesses'].split(',')) if row['shared_businesses'] else 0
            # 只有专享/专属团长返回business_contact，其他类型为空
            if row['leader_type'] in ['special', 'exclusive']:
                business_contact = row.get('business_contact', '')
            else:
                business_contact = ''
            result[row['leader_id']] = {
                'type': row['leader_type'],
                'sharedCount': shared_count,
                'business_contact': business_contact,
                'shared_businesses': row.get('shared_businesses', '') or ''
            }
        return jsonify({'code': 0, 'message': '成功', 'data': result})
    except Exception as e:
        return jsonify({'code': 500, 'message': str(e), 'data': None}), 500
    finally:
        if cursor: cursor.close()
        if connection: connection.close()

@team_leader_bp.route('/all-list', methods=['GET'])
def get_all_team_leader_list():
    """获取所有团长列表（管理员和运营用）- 显示对接商务名称"""
    connection = None
    cursor = None
    try:
        # 获取查询参数
        leader_name = request.args.get('leader_name', '')
        leader_id = request.args.get('leader_id', '')
        business_contact = request.args.get('business_contact', '')
        leader_type = request.args.get('leader_type', '')  # public, shared, special, exclusive
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        offset = (page - 1) * page_size

        # 连接数据库
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建查询条件
        conditions = []
        params = []

        if leader_name:
            conditions.append("leader_name LIKE %s")
            params.append(f"%{leader_name}%")

        if leader_id:
            conditions.append("leader_id LIKE %s")
            params.append(f"%{leader_id}%")

        if business_contact:
            conditions.append("business_contact LIKE %s")
            params.append(f"%{business_contact}%")

        # 根据团长类型筛选
        if leader_type == 'public':
            # 公海团长没有商务所属人且不是其他类型
            conditions.append("(business_contact IS NULL OR business_contact = '')")
            conditions.append("leader_type NOT IN ('special', 'shared', 'exclusive')")
        elif leader_type == 'shared':
            # 共享团长
            conditions.append("leader_type = 'shared'")
        elif leader_type == 'special':
            # 专享团长
            conditions.append("leader_type = 'special'")
        elif leader_type == 'exclusive':
            # 专属团长
            conditions.append("leader_type = 'exclusive'")

        # 构建WHERE子句
        where_clause = "1=1"
        if conditions:
            where_clause = " AND ".join(conditions)

        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM team_leader WHERE {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']

        # 查询数据
        query = f"SELECT * FROM team_leader WHERE {where_clause} ORDER BY update_time DESC LIMIT %s OFFSET %s"
        params.extend([page_size, offset])
        cursor.execute(query, params)
        leaders = cursor.fetchall()

        # 格式化返回数据
        result = {
            'code': 0,
            'message': '成功',
            'data': {
                'list': leaders,
                'total': total,
                'page': page,
                'page_size': page_size
            }
        }

        return jsonify(result)
    except Exception as e:
        print(f"获取所有团长列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取所有团长列表失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()