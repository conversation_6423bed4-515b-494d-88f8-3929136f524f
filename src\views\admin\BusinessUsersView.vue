<template>
  <div class="business-users-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>用户权限管理</h3>
        </div>
      </template>

      <!-- 选项卡 -->
      <el-tabs v-model="activeTab" class="management-tabs">
        <!-- 用户管理选项卡 -->
        <el-tab-pane label="用户管理" name="users">
          <div class="users-management">
            <!-- 搜索栏 -->
            <div class="search-header">
              <div class="search-filters">
                <el-input
                  v-model="searchForm.keyword"
                  placeholder="搜索用户名或姓名"
                  style="width: 200px"
                  clearable
                  @keyup.enter="handleSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-select
                  v-model="searchForm.role"
                  placeholder="筛选角色"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="全部" value="" />
                  <el-option label="运营" value="operation" />
                  <el-option label="商务" value="business" />
                </el-select>
                <el-button type="primary" @click="handleSearch">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="resetSearch">
                  <el-icon><RefreshLeft /></el-icon>
                  重置
                </el-button>
              </div>
              <el-button type="primary" @click="openAddUserDialog">
                <el-icon><Plus /></el-icon>
                添加用户
              </el-button>
            </div>

            <!-- 用户表格 -->
            <el-table v-loading="loading" :data="userList" style="width: 100%" border>
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="username" label="账号" width="150" />
              <el-table-column prop="name" label="姓名" width="150" />
              <el-table-column prop="role" label="角色" width="120">
                <template #default="scope">
                  <el-tag :type="getRoleTagType(scope.row.role)">{{
                    getRoleName(scope.row.role)
                  }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="role_template_name" label="角色模板" width="150" />
              <el-table-column prop="create_time" label="创建时间" width="180" />
              <el-table-column prop="update_time" label="更新时间" width="180" />
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="scope">
                  <el-button
                    v-if="scope.row.username !== 'manager'"
                    size="small"
                    type="primary"
                    @click="handleEdit(scope.row)"
                    >编辑</el-button
                  >
                  <el-button
                    v-if="scope.row.username !== 'manager'"
                    size="small"
                    type="danger"
                    @click="handleDelete(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-tab-pane>

        <!-- 岗位设置选项卡 -->
        <el-tab-pane label="岗位设置" name="roles">
          <div class="roles-management">
            <div class="roles-header">
              <h4>角色权限模板管理</h4>
              <el-button type="primary" @click="openAddRoleDialog">
                <el-icon><Plus /></el-icon>
                创建角色
              </el-button>
            </div>

            <!-- 角色列表 -->
            <div class="roles-grid">
              <el-card
                v-for="role in roleTemplates"
                :key="role.id"
                class="role-card"
                shadow="hover"
              >
                <template #header>
                  <div class="role-card-header">
                    <span class="role-name">{{ role.name }}</span>
                    <div class="role-actions">
                      <el-button size="small" type="primary" @click="editRoleTemplate(role)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button
                        v-if="!role.is_system"
                        size="small"
                        type="danger"
                        @click="deleteRoleTemplate(role)"
                      >
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </template>
                <div class="role-description">
                  <p>{{ role.description || '暂无描述' }}</p>
                  <div class="role-permissions-summary">
                    <el-tag size="small" v-for="module in getEnabledModules(role)" :key="module">
                      {{ getModuleName(module) }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 角色模板编辑对话框 -->
    <el-dialog
      v-model="roleDialogVisible"
      :title="roleDialogType === 'add' ? '创建角色' : '编辑角色'"
      width="800px"
    >
      <el-form ref="roleFormRef" :model="roleForm" :rules="roleRules" label-width="100px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色类型" prop="role">
          <el-input
            v-model="roleForm.role"
            placeholder="请输入角色类型（如：senior_business、regional_manager等）"
          />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>

        <!-- 权限模板配置 -->
        <el-form-item label="权限配置">
          <div class="permissions-compact-panel">
            <!-- 达人管理权限 -->
            <div class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="roleForm.permissions.perm_talent"
                  :active-value="1"
                  :inactive-value="0"
                />
                <span class="module-name">达人管理</span>
              </div>
              <div v-if="roleForm.permissions.perm_talent" class="permission-options">
                <!-- 数据权限 -->
                <div class="permission-group">
                  <div class="group-header">
                    <span class="group-label">数据权限:</span>
                    <el-button
                      size="small"
                      type="primary"
                      link
                      @click="openPermissionEditor('talent', 'data')"
                    >
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                  </div>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.talent.data"
                    size="small"
                  >
                    <el-checkbox
                      v-for="option in getPermissionOptions('talent', 'data')"
                      :key="option.value"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
                <!-- 操作权限 -->
                <div class="permission-group">
                  <div class="group-header">
                    <span class="group-label">操作权限:</span>
                    <el-button
                      size="small"
                      type="primary"
                      link
                      @click="openPermissionEditor('talent', 'operations')"
                    >
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                  </div>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.talent.operations"
                    size="small"
                  >
                    <el-checkbox
                      v-for="option in getPermissionOptions('talent', 'operations')"
                      :key="option.value"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 团长管理权限 -->
            <div class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="roleForm.permissions.perm_team_leader"
                  :active-value="1"
                  :inactive-value="0"
                />
                <span class="module-name">团长管理</span>
              </div>
              <div v-if="roleForm.permissions.perm_team_leader" class="permission-options">
                <!-- 数据权限 -->
                <div class="permission-group">
                  <div class="group-header">
                    <span class="group-label">数据权限:</span>
                    <el-button
                      size="small"
                      type="primary"
                      link
                      @click="openPermissionEditor('team_leader', 'data')"
                    >
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                  </div>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.team_leader.data"
                    size="small"
                  >
                    <el-checkbox
                      v-for="option in getPermissionOptions('team_leader', 'data')"
                      :key="option.value"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
                <!-- 操作权限 -->
                <div class="permission-group">
                  <div class="group-header">
                    <span class="group-label">操作权限:</span>
                    <el-button
                      size="small"
                      type="primary"
                      link
                      @click="openPermissionEditor('team_leader', 'operations')"
                    >
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                  </div>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.team_leader.operations"
                    size="small"
                  >
                    <el-checkbox
                      v-for="option in getPermissionOptions('team_leader', 'operations')"
                      :key="option.value"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 商品管理权限 -->
            <div class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="roleForm.permissions.perm_product"
                  :active-value="1"
                  :inactive-value="0"
                />
                <span class="module-name">商品管理</span>
              </div>
              <div v-if="roleForm.permissions.perm_product" class="permission-options">
                <!-- 操作权限 -->
                <div class="permission-group">
                  <div class="group-header">
                    <span class="group-label">操作权限:</span>
                    <el-button
                      size="small"
                      type="primary"
                      link
                      @click="openPermissionEditor('product', 'operations')"
                    >
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                  </div>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.product.operations"
                    size="small"
                  >
                    <el-checkbox
                      v-for="option in getPermissionOptions('product', 'operations')"
                      :key="option.value"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 订单管理权限 -->
            <div class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="roleForm.permissions.perm_order"
                  :active-value="1"
                  :inactive-value="0"
                />
                <span class="module-name">订单管理</span>
              </div>
              <div v-if="roleForm.permissions.perm_order" class="permission-options">
                <div class="permission-group">
                  <span class="group-label">子模块权限:</span>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.order.modules"
                    size="small"
                  >
                    <el-checkbox label="merchant_commission_orders">商家佣金</el-checkbox>
                    <el-checkbox label="reserved_service_fee_orders">预留服务费</el-checkbox>
                    <el-checkbox label="team_leader_commission_orders">团长佣金</el-checkbox>
                  </el-checkbox-group>
                </div>
                <div class="permission-group">
                  <span class="group-label">数据权限:</span>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.order.data"
                    size="small"
                  >
                    <el-checkbox label="own_only">仅本人</el-checkbox>
                    <el-checkbox label="all_data">全部</el-checkbox>
                  </el-checkbox-group>
                </div>
                <div class="permission-group">
                  <span class="group-label">操作权限:</span>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.order.operations"
                    size="small"
                  >
                    <el-checkbox label="edit_business">编辑商务</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 助播管理权限 -->
            <div class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="roleForm.permissions.perm_boost"
                  :active-value="1"
                  :inactive-value="0"
                />
                <span class="module-name">助播管理</span>
              </div>
              <div v-if="roleForm.permissions.perm_boost" class="permission-options">
                <div class="permission-group">
                  <span class="group-label">操作权限:</span>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.boost.operations"
                    size="small"
                  >
                    <el-checkbox label="view">查看</el-checkbox>
                    <el-checkbox label="add">添加</el-checkbox>
                    <el-checkbox label="edit">编辑</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 业绩管理权限 -->
            <div class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="roleForm.permissions.perm_performance_overview"
                  :active-value="1"
                  :inactive-value="0"
                />
                <span class="module-name">业绩管理</span>
              </div>
              <div
                v-if="
                  roleForm.permissions.perm_performance_overview ||
                  roleForm.permissions.perm_performance_statistics
                "
                class="permission-options"
              >
                <div class="permission-group">
                  <span class="group-label">子模块权限:</span>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.performance.modules"
                    size="small"
                  >
                    <el-checkbox label="performance_overview">业绩概览</el-checkbox>
                    <el-checkbox label="performance_statistics">业绩统计</el-checkbox>
                  </el-checkbox-group>
                </div>
                <div class="permission-group">
                  <span class="group-label">数据权限:</span>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.performance.data"
                    size="small"
                  >
                    <el-checkbox label="own_only">仅本人</el-checkbox>
                    <el-checkbox label="all_data">全部</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 寄样管理权限 -->
            <div class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="roleForm.permissions.perm_sample_management"
                  :active-value="1"
                  :inactive-value="0"
                />
                <span class="module-name">寄样管理</span>
              </div>
              <div v-if="roleForm.permissions.perm_sample_management" class="permission-options">
                <div class="permission-group">
                  <span class="group-label">操作权限:</span>
                  <el-checkbox-group
                    v-model="roleForm.permissions.permissions.sample.operations"
                    size="small"
                  >
                    <el-checkbox label="view">查看</el-checkbox>
                    <el-checkbox label="add">添加</el-checkbox>
                    <el-checkbox label="edit">编辑</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="roleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRoleForm" :loading="roleSubmitting"
            >确定</el-button
          >
        </span>
      </template>
    </el-dialog>

    <!-- 权限选项编辑器对话框 -->
    <el-dialog
      v-model="permissionEditorVisible"
      :title="`编辑${currentEditModule}模块的${currentEditType}权限选项`"
      width="600px"
    >
      <div class="permission-editor">
        <div class="editor-header">
          <el-button type="primary" @click="addPermissionOption">
            <el-icon><Plus /></el-icon>
            添加权限选项
          </el-button>
        </div>

        <div class="permission-options-list">
          <div
            v-for="(option, index) in currentPermissionOptions"
            :key="index"
            class="permission-option-item"
          >
            <el-input
              v-model="option.label"
              placeholder="权限名称（如：仅本人）"
              style="width: 200px; margin-right: 10px"
            />
            <el-input
              v-model="option.value"
              placeholder="权限值（如：own_only）"
              style="width: 200px; margin-right: 10px"
            />
            <el-button
              type="danger"
              size="small"
              @click="removePermissionOption(index)"
              :disabled="currentPermissionOptions.length <= 1"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>

        <div class="editor-tips">
          <el-alert title="提示" type="info" :closable="false" show-icon>
            <template #default>
              <p>• 权限名称：用户界面显示的文字，如"仅本人"、"全部数据"</p>
              <p>• 权限值：系统内部使用的标识，如"own_only"、"all_data"</p>
              <p>• 权限值应使用英文和下划线，避免特殊字符</p>
            </template>
          </el-alert>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionEditorVisible = false">取消</el-button>
          <el-button type="primary" @click="savePermissionOptions">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加/编辑用户对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
      width="600px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
        style="max-width: 400px"
      >
        <el-form-item label="账号" prop="username" v-if="dialogType === 'add'">
          <el-input v-model="userForm.username" placeholder="请输入账号" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="dialogType === 'add'">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>

        <!-- 编辑用户时的密码管理 -->
        <div v-if="dialogType === 'edit'">
          <el-form-item label="密码" prop="newPassword">
            <el-input
              v-model="userForm.newPassword"
              type="password"
              placeholder="直接输入新密码即可修改，留空则不修改"
              show-password
              clearable
            />
            <div style="margin-top: 5px; color: #999; font-size: 12px">
              当前密码：
              <span style="font-family: monospace">
                <el-input
                  v-model="currentPassword"
                  :type="showCurrentPassword ? 'text' : 'password'"
                  size="small"
                  readonly
                  style="
                    width: 200px;
                    display: inline-block;
                    background-color: transparent;
                    border: none;
                  "
                >
                  <template #suffix>
                    <el-icon
                      @click="showCurrentPassword = !showCurrentPassword"
                      style="cursor: pointer"
                    >
                      <View v-if="!showCurrentPassword" />
                      <Hide v-else />
                    </el-icon>
                  </template>
                </el-input>
              </span>
            </div>
          </el-form-item>
        </div>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin" />
            <el-option
              v-for="template in roleTemplates"
              :key="template.role"
              :label="template.name"
              :value="template.role"
            />
          </el-select>
        </el-form-item>

        <!-- 权限设置 - 除了管理员外的所有角色都显示 -->
        <div v-if="userForm.role !== 'admin' && userForm.role !== ''">
          <el-divider content-position="left">权限设置</el-divider>

          <!-- 全选权限按钮 -->
          <div class="permission-actions">
            <el-button type="primary" size="small" @click="selectAllPermissions">
              <el-icon><Select /></el-icon>
              全选权限
            </el-button>
            <el-button size="small" @click="clearAllPermissions">
              <el-icon><CloseBold /></el-icon>
              清空权限
            </el-button>
          </div>

          <!-- 权限配置面板 - 紧凑布局 -->
          <div class="permissions-compact-panel">
            <!-- 达人管理权限 -->
            <div v-if="shouldShowPermissionModule('talent')" class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="userForm.perm_talent"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleModulePermissionChange('talent', $event)"
                />
                <span class="module-name">达人管理</span>
              </div>
              <div v-if="userForm.perm_talent" class="permission-options">
                <div v-if="shouldShowDetailPermission('talent', 'data')" class="permission-group">
                  <span class="group-label">数据权限:</span>
                  <el-checkbox-group v-model="userForm.permissions.talent.data" size="small">
                    <el-checkbox
                      label="own_only"
                      v-if="shouldShowSpecificPermission('talent', 'data', 'own_only')"
                      >仅本人</el-checkbox
                    >
                    <el-checkbox
                      label="all_data"
                      v-if="shouldShowSpecificPermission('talent', 'data', 'all_data')"
                      >全部</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
                <div
                  v-if="shouldShowDetailPermission('talent', 'operations')"
                  class="permission-group"
                >
                  <span class="group-label">操作权限:</span>
                  <el-checkbox-group v-model="userForm.permissions.talent.operations" size="small">
                    <el-checkbox
                      label="view"
                      v-if="shouldShowSpecificPermission('talent', 'operations', 'view')"
                      >查看</el-checkbox
                    >
                    <el-checkbox
                      label="add"
                      v-if="shouldShowSpecificPermission('talent', 'operations', 'add')"
                      >添加</el-checkbox
                    >
                    <el-checkbox
                      label="edit"
                      v-if="shouldShowSpecificPermission('talent', 'operations', 'edit')"
                      >编辑</el-checkbox
                    >
                    <el-checkbox
                      label="assign"
                      v-if="shouldShowSpecificPermission('talent', 'operations', 'assign')"
                      >分配</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 团长管理权限 -->
            <div v-if="shouldShowPermissionModule('team_leader')" class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="userForm.perm_team_leader"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleModulePermissionChange('team_leader', $event)"
                />
                <span class="module-name">团长管理</span>
              </div>
              <div v-if="userForm.perm_team_leader" class="permission-options">
                <div
                  v-if="shouldShowDetailPermission('team_leader', 'data')"
                  class="permission-group"
                >
                  <span class="group-label">数据权限:</span>
                  <el-checkbox-group v-model="userForm.permissions.team_leader.data" size="small">
                    <el-checkbox
                      label="own_only"
                      v-if="shouldShowSpecificPermission('team_leader', 'data', 'own_only')"
                      >仅本人</el-checkbox
                    >
                    <el-checkbox
                      label="all_data"
                      v-if="shouldShowSpecificPermission('team_leader', 'data', 'all_data')"
                      >全部</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
                <div
                  v-if="shouldShowDetailPermission('team_leader', 'operations')"
                  class="permission-group"
                >
                  <span class="group-label">操作权限:</span>
                  <el-checkbox-group
                    v-model="userForm.permissions.team_leader.operations"
                    size="small"
                  >
                    <el-checkbox
                      label="view"
                      v-if="shouldShowSpecificPermission('team_leader', 'operations', 'view')"
                      >查看</el-checkbox
                    >
                    <el-checkbox
                      label="add"
                      v-if="shouldShowSpecificPermission('team_leader', 'operations', 'add')"
                      >添加</el-checkbox
                    >
                    <el-checkbox
                      label="edit"
                      v-if="shouldShowSpecificPermission('team_leader', 'operations', 'edit')"
                      >编辑</el-checkbox
                    >
                    <el-checkbox
                      label="assign"
                      v-if="shouldShowSpecificPermission('team_leader', 'operations', 'assign')"
                      >分配</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 商品管理权限 -->
            <div v-if="shouldShowPermissionModule('product')" class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="userForm.perm_product"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleModulePermissionChange('product', $event)"
                />
                <span class="module-name">商品管理</span>
              </div>
              <div v-if="userForm.perm_product" class="permission-options">
                <div
                  v-if="shouldShowDetailPermission('product', 'operations')"
                  class="permission-group"
                >
                  <span class="group-label">操作权限:</span>
                  <el-checkbox-group v-model="userForm.permissions.product.operations" size="small">
                    <el-checkbox
                      label="view"
                      v-if="shouldShowSpecificPermission('product', 'operations', 'view')"
                      >查看</el-checkbox
                    >
                    <el-checkbox
                      label="add"
                      v-if="shouldShowSpecificPermission('product', 'operations', 'add')"
                      >添加</el-checkbox
                    >
                    <el-checkbox
                      label="edit"
                      v-if="shouldShowSpecificPermission('product', 'operations', 'edit')"
                      >编辑</el-checkbox
                    >
                    <el-checkbox
                      label="promote"
                      v-if="shouldShowSpecificPermission('product', 'operations', 'promote')"
                      >推广</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 订单管理权限 -->
            <div v-if="shouldShowPermissionModule('order')" class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="userForm.perm_order"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleModulePermissionChange('order', $event)"
                />
                <span class="module-name">订单管理</span>
              </div>
              <div v-if="userForm.perm_order" class="permission-options">
                <div v-if="shouldShowDetailPermission('order', 'modules')" class="permission-group">
                  <span class="group-label">子模块权限:</span>
                  <el-checkbox-group v-model="userForm.permissions.order.modules" size="small">
                    <el-checkbox
                      label="merchant_commission_orders"
                      v-if="
                        shouldShowSpecificPermission(
                          'order',
                          'modules',
                          'merchant_commission_orders',
                        )
                      "
                      >商家佣金</el-checkbox
                    >
                    <el-checkbox
                      label="reserved_service_fee_orders"
                      v-if="
                        shouldShowSpecificPermission(
                          'order',
                          'modules',
                          'reserved_service_fee_orders',
                        )
                      "
                      >预留服务费</el-checkbox
                    >
                    <el-checkbox
                      label="team_leader_commission_orders"
                      v-if="
                        shouldShowSpecificPermission(
                          'order',
                          'modules',
                          'team_leader_commission_orders',
                        )
                      "
                      >团长佣金</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
                <div v-if="shouldShowDetailPermission('order', 'data')" class="permission-group">
                  <span class="group-label">数据权限:</span>
                  <el-checkbox-group v-model="userForm.permissions.order.data" size="small">
                    <el-checkbox
                      label="own_only"
                      v-if="shouldShowSpecificPermission('order', 'data', 'own_only')"
                      >仅本人</el-checkbox
                    >
                    <el-checkbox
                      label="all_data"
                      v-if="shouldShowSpecificPermission('order', 'data', 'all_data')"
                      >全部</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
                <div
                  v-if="shouldShowDetailPermission('order', 'operations')"
                  class="permission-group"
                >
                  <span class="group-label">操作权限:</span>
                  <el-checkbox-group v-model="userForm.permissions.order.operations" size="small">
                    <el-checkbox
                      label="edit_business"
                      v-if="shouldShowSpecificPermission('order', 'operations', 'edit_business')"
                      >编辑商务</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 助播管理权限 -->
            <div v-if="shouldShowPermissionModule('boost')" class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="userForm.perm_boost"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleModulePermissionChange('boost', $event)"
                />
                <span class="module-name">助播管理</span>
              </div>
              <div v-if="userForm.perm_boost" class="permission-options">
                <div
                  v-if="shouldShowDetailPermission('boost', 'operations')"
                  class="permission-group"
                >
                  <span class="group-label">操作权限:</span>
                  <el-checkbox-group v-model="userForm.permissions.boost.operations" size="small">
                    <el-checkbox
                      label="view"
                      v-if="shouldShowSpecificPermission('boost', 'operations', 'view')"
                      >查看</el-checkbox
                    >
                    <el-checkbox
                      label="add"
                      v-if="shouldShowSpecificPermission('boost', 'operations', 'add')"
                      >添加</el-checkbox
                    >
                    <el-checkbox
                      label="edit"
                      v-if="shouldShowSpecificPermission('boost', 'operations', 'edit')"
                      >编辑</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 业绩管理权限 -->
            <div v-if="shouldShowPermissionModule('performance')" class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="userForm.perm_performance_overview"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleModulePermissionChange('performance', $event)"
                />
                <span class="module-name">业绩管理</span>
              </div>
              <div
                v-if="userForm.perm_performance_overview || userForm.perm_performance_statistics"
                class="permission-options"
              >
                <div
                  v-if="shouldShowDetailPermission('performance', 'modules')"
                  class="permission-group"
                >
                  <span class="group-label">子模块权限:</span>
                  <el-checkbox-group
                    v-model="userForm.permissions.performance.modules"
                    size="small"
                  >
                    <el-checkbox
                      label="performance_overview"
                      v-if="
                        shouldShowSpecificPermission(
                          'performance',
                          'modules',
                          'performance_overview',
                        )
                      "
                      >业绩概览</el-checkbox
                    >
                    <el-checkbox
                      label="performance_statistics"
                      v-if="
                        shouldShowSpecificPermission(
                          'performance',
                          'modules',
                          'performance_statistics',
                        )
                      "
                      >业绩统计</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
                <div
                  v-if="shouldShowDetailPermission('performance', 'data')"
                  class="permission-group"
                >
                  <span class="group-label">数据权限:</span>
                  <el-checkbox-group v-model="userForm.permissions.performance.data" size="small">
                    <el-checkbox
                      label="own_only"
                      v-if="shouldShowSpecificPermission('performance', 'data', 'own_only')"
                      >仅本人</el-checkbox
                    >
                    <el-checkbox
                      label="all_data"
                      v-if="shouldShowSpecificPermission('performance', 'data', 'all_data')"
                      >全部</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
              </div>
            </div>

            <!-- 寄样管理权限 -->
            <div v-if="shouldShowPermissionModule('sample')" class="permission-row">
              <div class="permission-module">
                <el-switch
                  v-model="userForm.perm_sample_management"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleModulePermissionChange('sample', $event)"
                />
                <span class="module-name">寄样管理</span>
              </div>
              <div v-if="userForm.perm_sample_management" class="permission-options">
                <div
                  v-if="shouldShowDetailPermission('sample', 'operations')"
                  class="permission-group"
                >
                  <span class="group-label">操作权限:</span>
                  <el-checkbox-group v-model="userForm.permissions.sample.operations" size="small">
                    <el-checkbox
                      label="view"
                      v-if="shouldShowSpecificPermission('sample', 'operations', 'view')"
                      >查看</el-checkbox
                    >
                    <el-checkbox
                      label="add"
                      v-if="shouldShowSpecificPermission('sample', 'operations', 'add')"
                      >添加</el-checkbox
                    >
                    <el-checkbox
                      label="edit"
                      v-if="shouldShowSpecificPermission('sample', 'operations', 'edit')"
                      >编辑</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUserForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import {
  View,
  Hide,
  ArrowDown,
  ArrowUp,
  Search,
  RefreshLeft,
  Plus,
  Edit,
  Delete,
  Select,
  CloseBold,
} from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'

// 权限数据结构
interface ModulePermission {
  data: string[]
  operations: string[]
}

interface OrderModulePermission {
  data: string[]
  operations: string[]
  modules: string[]
}

interface PerformanceModulePermission {
  data: string[]
  operations: string[]
  modules: string[]
}

interface UserPermissions {
  talent: ModulePermission
  team_leader: ModulePermission
  product: ModulePermission
  order: OrderModulePermission
  boost: ModulePermission
  performance: PerformanceModulePermission
  sample: ModulePermission
}

interface User {
  id: number
  username: string
  name: string
  role: string
  create_time: string
  update_time: string
  // 权限字段
  perm_talent?: number
  perm_team_leader?: number
  perm_product?: number
  perm_order?: number
  perm_boost?: number
  perm_performance_overview?: number
  perm_performance_statistics?: number
  perm_sample_management?: number
  perm_hide_product?: number
  perm_manage_tags?: number
  perm_merchant_commission_orders?: number
  perm_reserved_service_fee_orders?: number
  perm_team_leader_commission_orders?: number
  permissions?: UserPermissions
}

const route = useRoute()
const router = useRouter()

// 选项卡
const activeTab = ref('users')

// 用户列表
const userList = ref<User[]>([])
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  role: '',
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 对话框相关
const userDialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const userFormRef = ref<FormInstance>()
const submitting = ref(false)

// 角色模板相关
const roleTemplates = ref<any[]>([])
const roleDialogVisible = ref(false)
const roleDialogType = ref<'add' | 'edit'>('add')
const roleFormRef = ref<FormInstance>()
const roleSubmitting = ref(false)

// 权限编辑器相关
const permissionEditorVisible = ref(false)
const currentEditModule = ref('')
const currentEditType = ref('')
const currentPermissionOptions = ref<Array<{ label: string; value: string }>>([])

// 默认权限选项配置
const defaultPermissionOptions = {
  talent: {
    data: [
      { label: '仅本人', value: 'own_only' },
      { label: '全部', value: 'all_data' },
    ],
    operations: [
      { label: '查看', value: 'view' },
      { label: '添加', value: 'add' },
      { label: '编辑', value: 'edit' },
      { label: '分配', value: 'assign' },
    ],
  },
  team_leader: {
    data: [
      { label: '仅本人', value: 'own_only' },
      { label: '全部', value: 'all_data' },
    ],
    operations: [
      { label: '查看', value: 'view' },
      { label: '添加', value: 'add' },
      { label: '编辑', value: 'edit' },
      { label: '分配', value: 'assign' },
    ],
  },
  product: {
    operations: [
      { label: '查看', value: 'view' },
      { label: '添加', value: 'add' },
      { label: '编辑', value: 'edit' },
      { label: '推广', value: 'promote' },
    ],
  },
  order: {
    modules: [
      { label: '商家佣金', value: 'merchant_commission_orders' },
      { label: '预留服务费', value: 'reserved_service_fee_orders' },
      { label: '团长佣金', value: 'team_leader_commission_orders' },
    ],
    data: [
      { label: '仅本人', value: 'own_only' },
      { label: '全部', value: 'all_data' },
    ],
    operations: [{ label: '编辑商务', value: 'edit_business' }],
  },
  boost: {
    operations: [
      { label: '查看', value: 'view' },
      { label: '添加', value: 'add' },
      { label: '编辑', value: 'edit' },
    ],
  },
  performance: {
    modules: [
      { label: '业绩概览', value: 'performance_overview' },
      { label: '业绩统计', value: 'performance_statistics' },
    ],
    data: [
      { label: '仅本人', value: 'own_only' },
      { label: '全部', value: 'all_data' },
    ],
  },
  sample: {
    operations: [
      { label: '查看', value: 'view' },
      { label: '添加', value: 'add' },
      { label: '编辑', value: 'edit' },
    ],
  },
}

// 角色表单
const roleForm = reactive({
  id: 0,
  name: '',
  role: '',
  description: '',
  permissions: {
    perm_talent: 0,
    perm_team_leader: 0,
    perm_product: 0,
    perm_order: 0,
    perm_boost: 0,
    perm_performance_overview: 0,
    perm_performance_statistics: 0,
    perm_sample_management: 0,
    perm_hide_product: 0,
    perm_manage_tags: 0,
    perm_merchant_commission_orders: 0,
    perm_reserved_service_fee_orders: 0,
    perm_team_leader_commission_orders: 0,
    permissions: {
      talent: { data: [], operations: [] },
      team_leader: { data: [], operations: [] },
      product: { data: [], operations: [] },
      order: { data: [], operations: [], modules: [] },
      boost: { data: [], operations: [] },
      performance: { data: [], operations: [], modules: [] },
      sample: { data: [], operations: [] },
    },
    permissionOptions: {} as any,
  },
})

// 当前编辑用户的角色模板信息
const currentUserRoleTemplate = ref<any>(null)

// 角色表单验证规则
const roleRules = reactive({
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
})

// 页面标题
const getPageTitle = computed(() => {
  return '用户权限管理'
})

// 用户表单
const userForm = reactive({
  id: 0,
  username: '',
  name: '',
  password: '',
  newPassword: '',
  role: 'business',
  // 权限字段
  perm_talent: 1,
  perm_team_leader: 1,
  perm_product: 1,
  perm_order: 1,
  perm_boost: 1,
  perm_performance_overview: 1,
  perm_performance_statistics: 1,
  perm_sample_management: 1,
  perm_hide_product: 0, // 隐藏商品权限默认为0
  perm_manage_tags: 0, // 标签管理权限默认为0
  perm_merchant_commission_orders: 1, // 商家返佣订单权限默认为1
  perm_reserved_service_fee_orders: 1, // 预留服务费订单权限默认为1
  perm_team_leader_commission_orders: 1, // 团长返佣订单权限默认为1
  // 细粒度权限配置
  permissions: {
    talent: { data: [], operations: ['view', 'add', 'edit', 'assign'] },
    team_leader: { data: [], operations: ['view', 'add', 'edit', 'assign'] },
    product: { data: [], operations: ['view', 'add', 'edit', 'promote'] },
    order: { data: [], operations: [], modules: [] },
    boost: { data: [], operations: ['view', 'add', 'edit'] },
    performance: { data: [], operations: [], modules: [] },
    sample: { data: [], operations: ['view', 'add', 'edit'] },
    system: { data: [], operations: [] },
  } as UserPermissions,
})

// 权限展开状态
const expandedPermissions = reactive({
  talent: false,
  team_leader: false,
  product: false,
  order: false,
  boost: false,
  performance: false,
  sample: false,
})

// 密码相关状态
const currentPassword = ref('')
const showCurrentPassword = ref(false)

// 表单验证规则
const userRules = reactive({
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' },
  ],
  newPassword: [{ min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
})

// 获取角色名称
const getRoleName = (role: string): string => {
  switch (role) {
    case 'admin':
      return '管理员'
    case 'business':
      return '商务'
    case 'operation':
      return '运营'
    default:
      return '未知'
  }
}

// 获取角色标签类型
const getRoleTagType = (role: string): string => {
  switch (role) {
    case 'admin':
      return 'danger'
    case 'business':
      return 'primary'
    case 'operation':
      return 'success'
    default:
      return 'info'
  }
}

// 获取用户列表
const getUserList = async () => {
  loading.value = true
  try {
    const params: Record<string, any> = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
    }

    if (searchForm.keyword) {
      params['keyword'] = searchForm.keyword
    }

    if (searchForm.role) {
      params['role'] = searchForm.role
    }

    const response = await axios.get('/api/auth/business-users', { params })
    if (response.data.code === 0) {
      userList.value = response.data.data.list
      pagination.total = response.data.data.total
    } else {
      ElMessage.error(response.data.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1
  getUserList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.role = ''
  pagination.currentPage = 1
  getUserList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  getUserList()
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  getUserList()
}

// 打开添加用户对话框
const openAddUserDialog = () => {
  dialogType.value = 'add'
  userForm.id = 0
  userForm.username = ''
  userForm.name = ''
  userForm.password = ''
  userForm.newPassword = ''

  // 重置密码相关状态
  currentPassword.value = ''
  showCurrentPassword.value = false

  // 默认选择商务角色
  userForm.role = 'business'

  // 先重置权限
  userForm.perm_talent = 0
  userForm.perm_team_leader = 0
  userForm.perm_product = 0
  userForm.perm_order = 0
  userForm.perm_boost = 0
  userForm.perm_performance_overview = 0
  userForm.perm_performance_statistics = 0
  userForm.perm_sample_management = 0
  userForm.perm_hide_product = 0
  userForm.perm_manage_tags = 0
  userForm.perm_merchant_commission_orders = 0
  userForm.perm_reserved_service_fee_orders = 0
  userForm.perm_team_leader_commission_orders = 0

  // 重置细粒度权限
  userForm.permissions = {
    talent: { data: [], operations: [] },
    team_leader: { data: [], operations: [] },
    product: { data: [], operations: [] },
    order: { data: [], operations: [], modules: [] },
    boost: { data: [], operations: [] },
    performance: { data: [], operations: [], modules: [] },
    sample: { data: [], operations: [] },
  }

  // 重置权限展开状态
  Object.keys(expandedPermissions).forEach((key) => {
    expandedPermissions[key as keyof typeof expandedPermissions] = false
  })

  userDialogVisible.value = true

  // 默认全选该角色的权限
  setTimeout(() => {
    selectAllPermissions()
  }, 100)
}

// 处理编辑用户
const handleEdit = async (row: User) => {
  try {
    dialogType.value = 'edit'

    // 获取用户详细信息（包括密码）
    const response = await axios.get(`/api/auth/business-users/${row.id}`)
    if (response.data.code === 0) {
      const userData = response.data.data
      userForm.id = userData.id
      userForm.username = userData.username
      userForm.name = userData.name
      userForm.role = userData.role

      // 设置当前密码
      currentPassword.value = userData.password || ''

      // 重置密码编辑状态
      showCurrentPassword.value = false
      userForm.newPassword = ''

      // 根据用户的role字段获取对应的角色模板
      currentUserRoleTemplate.value =
        roleTemplates.value.find((template: any) => template.role === userData.role) || null

      console.log('用户role:', userData.role, '匹配的角色模板:', currentUserRoleTemplate.value)

      // 设置权限值 - 显示用户真实的权限状态
      userForm.perm_talent = userData.perm_talent ?? 0
      userForm.perm_team_leader = userData.perm_team_leader ?? 0
      userForm.perm_product = userData.perm_product ?? 0
      userForm.perm_order = userData.perm_order ?? 0
      userForm.perm_boost = userData.perm_boost ?? 0
      userForm.perm_performance_overview = userData.perm_performance_overview ?? 0
      userForm.perm_performance_statistics = userData.perm_performance_statistics ?? 0
      userForm.perm_sample_management = userData.perm_sample_management ?? 0
      userForm.perm_hide_product = userData.perm_hide_product ?? 0
      userForm.perm_manage_tags = userData.perm_manage_tags ?? 0
      userForm.perm_merchant_commission_orders = userData.perm_merchant_commission_orders ?? 0
      userForm.perm_reserved_service_fee_orders = userData.perm_reserved_service_fee_orders ?? 0
      userForm.perm_team_leader_commission_orders = userData.perm_team_leader_commission_orders ?? 0

      // 设置细粒度权限
      if (userData.permissions) {
        try {
          userForm.permissions =
            typeof userData.permissions === 'string'
              ? JSON.parse(userData.permissions)
              : userData.permissions
        } catch (error) {
          console.error('解析权限数据失败:', error)
          userForm.permissions = {
            talent: { data: [], operations: [] },
            team_leader: { data: [], operations: [] },
            product: { data: [], operations: [] },
            order: { data: [], operations: [], modules: [] },
            boost: { data: [], operations: [] },
            performance: { data: [], operations: [], modules: [] },
            sample: { data: [], operations: [] },
          }
        }
      } else {
        // 如果没有细粒度权限数据，使用默认值
        userForm.permissions = {
          talent: { data: [], operations: [] },
          team_leader: { data: [], operations: [] },
          product: { data: [], operations: [] },
          order: { data: [], operations: [], modules: [] },
          boost: { data: [], operations: [] },
          performance: { data: [], operations: [], modules: [] },
          sample: { data: [], operations: [] },
        }
      }

      // 重置权限展开状态
      Object.keys(expandedPermissions).forEach((key) => {
        expandedPermissions[key as keyof typeof expandedPermissions] = false
      })

      userDialogVisible.value = true
    } else {
      ElMessage.error(response.data.message || '获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请检查网络连接')
  }
}

// 处理删除用户
const handleDelete = (row: User) => {
  ElMessageBox.confirm(`确定要删除用户 ${row.name} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const response = await axios.delete(`/api/auth/business-users/${row.id}`)
        if (response.data.code === 0) {
          ElMessage.success('删除用户成功')
          getUserList()
        } else {
          ElMessage.error(response.data.message || '删除用户失败')
        }
      } catch (error: any) {
        if (error.response && error.response.data) {
          ElMessage.error(error.response.data.message || '删除用户失败')
        } else {
          ElMessage.error('删除用户失败，请检查网络连接')
        }
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 提交用户表单
const submitUserForm = async () => {
  if (!userFormRef.value) return

  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (dialogType.value === 'add') {
          // 添加用户
          const response = await axios.post('/api/auth/business-users', {
            username: userForm.username,
            name: userForm.name,
            password: userForm.password,
            role: userForm.role,
            // 权限字段
            perm_talent: userForm.perm_talent,
            perm_team_leader: userForm.perm_team_leader,
            perm_product: userForm.perm_product,
            perm_order: userForm.perm_order,
            perm_boost: userForm.perm_boost,
            perm_performance_overview: userForm.perm_performance_overview,
            perm_performance_statistics: userForm.perm_performance_statistics,
            perm_sample_management: userForm.perm_sample_management,
            perm_hide_product: userForm.perm_hide_product,
            perm_manage_tags: userForm.perm_manage_tags,
            perm_merchant_commission_orders: userForm.perm_merchant_commission_orders,
            perm_reserved_service_fee_orders: userForm.perm_reserved_service_fee_orders,
            perm_team_leader_commission_orders: userForm.perm_team_leader_commission_orders,
            permissions: userForm.permissions,
          })

          if (response.data.code === 0) {
            ElMessage.success('添加用户成功')
            userDialogVisible.value = false
            getUserList()
          } else {
            ElMessage.error(response.data.message || '添加用户失败')
          }
        } else {
          // 编辑用户
          const updateData: any = {
            name: userForm.name,
            role: userForm.role,
            // 权限字段
            perm_talent: userForm.perm_talent,
            perm_team_leader: userForm.perm_team_leader,
            perm_product: userForm.perm_product,
            perm_order: userForm.perm_order,
            perm_boost: userForm.perm_boost,
            perm_performance_overview: userForm.perm_performance_overview,
            perm_performance_statistics: userForm.perm_performance_statistics,
            perm_sample_management: userForm.perm_sample_management,
            perm_hide_product: userForm.perm_hide_product,
            perm_manage_tags: userForm.perm_manage_tags,
            perm_merchant_commission_orders: userForm.perm_merchant_commission_orders,
            perm_reserved_service_fee_orders: userForm.perm_reserved_service_fee_orders,
            perm_team_leader_commission_orders: userForm.perm_team_leader_commission_orders,
            permissions: userForm.permissions,
          }

          // 如果输入了新密码，则添加到更新数据中
          if (userForm.newPassword && userForm.newPassword.trim()) {
            updateData.password = userForm.newPassword
          }

          const response = await axios.put(`/api/auth/business-users/${userForm.id}`, updateData)

          if (response.data.code === 0) {
            ElMessage.success('更新用户成功')
            userDialogVisible.value = false
            getUserList()
          } else {
            ElMessage.error(response.data.message || '更新用户失败')
          }
        }
      } catch (error: any) {
        if (error.response && error.response.data) {
          ElMessage.error(error.response.data.message || '操作失败')
        } else {
          ElMessage.error('操作失败，请检查网络连接')
        }
      } finally {
        submitting.value = false
      }
    }
  })
}

// 权限相关方法
const togglePermissionDetail = (module: keyof typeof expandedPermissions) => {
  expandedPermissions[module] = !expandedPermissions[module]
}

// 监听角色变化，添加用户时自动全选权限
watch(
  () => userForm.role,
  (newRole) => {
    if (newRole && dialogType.value === 'add') {
      // 添加用户时，选择角色后自动全选该角色模板的权限
      setTimeout(() => {
        selectAllPermissions()
      }, 100)
    }
  },
)

// 判断权限模块是否应该显示 - 根据角色模板配置
const shouldShowPermissionModule = (moduleName: string) => {
  // 管理员显示所有权限
  if (userForm.role === 'admin') {
    return true
  }

  // 如果还没有选择角色，不显示权限模块
  if (!userForm.role) {
    return false
  }

  // 根据用户选择的角色查找对应的角色模板
  const template = roleTemplates.value.find((t: any) => t.role === userForm.role)

  // 如果没有找到角色模板，显示所有权限（兼容旧数据或基础角色）
  if (!template || !template.permissions) {
    return true
  }

  // 根据角色模板的权限配置决定是否显示
  const permissions = template.permissions

  switch (moduleName) {
    case 'talent':
      return permissions.perm_talent === 1
    case 'team_leader':
      return permissions.perm_team_leader === 1
    case 'product':
      return permissions.perm_product === 1
    case 'order':
      return permissions.perm_order === 1
    case 'boost':
      return permissions.perm_boost === 1
    case 'performance':
      return (
        permissions.perm_performance_overview === 1 || permissions.perm_performance_statistics === 1
      )
    case 'sample':
      return permissions.perm_sample_management === 1
    default:
      return true
  }
}

// 判断是否显示数据权限选项
const shouldShowDataPermission = (type: 'own_only' | 'all_data') => {
  // 管理员显示所有权限
  if (userForm.role === 'admin') {
    return true
  }

  // 根据角色类型显示不同的数据权限
  // operation 类型的角色显示"全部数据"
  // business 类型的角色显示"仅本人数据"
  // 自定义角色根据角色名称判断
  if (type === 'all_data') {
    return userForm.role === 'operation' || userForm.role.includes('operation')
  } else if (type === 'own_only') {
    return (
      userForm.role === 'business' ||
      userForm.role.includes('business') ||
      (!userForm.role.includes('operation') && userForm.role !== 'admin')
    )
  }

  return false
}

// 判断细粒度权限是否应该显示
const shouldShowDetailPermission = (
  moduleName: string,
  permissionType: 'data' | 'operations' | 'modules',
) => {
  // 管理员显示所有权限
  if (userForm.role === 'admin') {
    return true
  }

  // 如果还没有选择角色，不显示
  if (!userForm.role) {
    return false
  }

  // 根据用户选择的角色查找对应的角色模板
  const template = roleTemplates.value.find((t: any) => t.role === userForm.role)

  // 如果没有找到角色模板，显示所有权限（兼容旧数据）
  if (!template || !template.permissions || !template.permissions.permissions) {
    return true
  }

  const modulePermissions = template.permissions.permissions[moduleName]
  if (!modulePermissions) {
    return true
  }

  // 根据权限类型判断是否显示
  if (permissionType === 'data') {
    // 数据权限：检查模板中是否配置了数据权限
    return modulePermissions.data && modulePermissions.data.length > 0
  } else if (permissionType === 'operations') {
    // 操作权限：检查模板中是否配置了操作权限
    return modulePermissions.operations && modulePermissions.operations.length > 0
  } else if (permissionType === 'modules') {
    // 子模块权限：检查模板中是否配置了子模块权限
    return modulePermissions.modules && modulePermissions.modules.length > 0
  }

  return true
}

// 判断具体的权限选项是否应该显示
const shouldShowSpecificPermission = (
  moduleName: string,
  permissionType: 'data' | 'operations' | 'modules',
  permissionValue: string,
) => {
  // 管理员显示所有权限
  if (userForm.role === 'admin') {
    return true
  }

  // 如果还没有选择角色，不显示
  if (!userForm.role) {
    return false
  }

  // 根据用户选择的角色查找对应的角色模板
  const template = roleTemplates.value.find((t: any) => t.role === userForm.role)

  // 如果没有找到角色模板，显示所有权限（兼容旧数据）
  if (!template || !template.permissions || !template.permissions.permissions) {
    return true
  }

  const modulePermissions = template.permissions.permissions[moduleName]
  if (!modulePermissions) {
    return true
  }

  // 检查具体的权限值是否在模板配置中
  if (permissionType === 'data') {
    return modulePermissions.data && modulePermissions.data.includes(permissionValue)
  } else if (permissionType === 'operations') {
    return modulePermissions.operations && modulePermissions.operations.includes(permissionValue)
  } else if (permissionType === 'modules') {
    return modulePermissions.modules && modulePermissions.modules.includes(permissionValue)
  }

  return true
}

const handleModulePermissionChange = (module: keyof UserPermissions, enabled: number) => {
  if (!enabled) {
    // 如果禁用模块权限，清空细粒度权限
    userForm.permissions[module].data = []
    userForm.permissions[module].operations = []
    expandedPermissions[module] = false
  } else {
    // 如果启用模块权限，设置默认权限
    setDefaultPermissions(module)
  }
}

const setDefaultPermissions = (module: keyof UserPermissions) => {
  const defaultPermissions: UserPermissions = {
    talent: {
      data: [],
      operations: ['view', 'add', 'edit', 'assign'],
    },
    team_leader: {
      data: [],
      operations: ['view', 'add', 'edit', 'assign'],
    },
    product: {
      data: [],
      operations: ['view', 'add', 'edit', 'promote'],
    },
    order: {
      data: [],
      operations: [],
      modules: [],
    },
    boost: {
      data: [],
      operations: ['view', 'add', 'edit'],
    },
    performance: {
      data: [],
      operations: [],
      modules: [],
    },
    sample: {
      data: [],
      operations: ['view', 'add', 'edit'],
    },
  }

  userForm.permissions[module] = { ...defaultPermissions[module] } as any
}

// 全选权限
const selectAllPermissions = () => {
  // 根据用户选择的角色查找对应的角色模板
  const template = roleTemplates.value.find((t: any) => t.role === userForm.role)

  if (template && template.permissions) {
    const templatePerms = template.permissions

    // 根据角色模板设置权限
    userForm.perm_talent = templatePerms.perm_talent || 0
    userForm.perm_team_leader = templatePerms.perm_team_leader || 0
    userForm.perm_product = templatePerms.perm_product || 0
    userForm.perm_order = templatePerms.perm_order || 0
    userForm.perm_boost = templatePerms.perm_boost || 0
    userForm.perm_performance_overview = templatePerms.perm_performance_overview || 0
    userForm.perm_performance_statistics = templatePerms.perm_performance_statistics || 0
    userForm.perm_sample_management = templatePerms.perm_sample_management || 0
    userForm.perm_hide_product = templatePerms.perm_hide_product || 0
    userForm.perm_manage_tags = templatePerms.perm_manage_tags || 0
    userForm.perm_merchant_commission_orders = templatePerms.perm_merchant_commission_orders || 0
    userForm.perm_reserved_service_fee_orders = templatePerms.perm_reserved_service_fee_orders || 0
    userForm.perm_team_leader_commission_orders =
      templatePerms.perm_team_leader_commission_orders || 0

    // 设置细粒度权限
    if (templatePerms.permissions) {
      userForm.permissions = JSON.parse(JSON.stringify(templatePerms.permissions))
    }
  }

  // 不显示提示消息，静默设置
}

// 清空权限
const clearAllPermissions = () => {
  userForm.perm_talent = 0
  userForm.perm_team_leader = 0
  userForm.perm_product = 0
  userForm.perm_order = 0
  userForm.perm_boost = 0
  userForm.perm_performance_overview = 0
  userForm.perm_performance_statistics = 0
  userForm.perm_sample_management = 0
  userForm.perm_hide_product = 0
  userForm.perm_manage_tags = 0
  userForm.perm_merchant_commission_orders = 0
  userForm.perm_reserved_service_fee_orders = 0
  userForm.perm_team_leader_commission_orders = 0

  // 清空细粒度权限
  userForm.permissions = {
    talent: { data: [], operations: [] },
    team_leader: { data: [], operations: [] },
    product: { data: [], operations: [] },
    order: { data: [], operations: [], modules: [] },
    boost: { data: [], operations: [] },
    performance: { data: [], operations: [], modules: [] },
    sample: { data: [], operations: [] },
  }

  // 收起所有权限详情
  Object.keys(expandedPermissions).forEach((key) => {
    expandedPermissions[key as keyof typeof expandedPermissions] = false
  })

  ElMessage.success('已清空所有权限')
}

// 角色模板相关方法
const getRoleTemplates = async () => {
  try {
    const response = await axios.get('/api/role-templates')
    if (response.data.code === 0) {
      roleTemplates.value = response.data.data
    } else {
      ElMessage.error(response.data.message || '获取角色模板失败')
    }
  } catch (error) {
    console.error('获取角色模板失败:', error)
    ElMessage.error('获取角色模板失败')
  }
}

const openAddRoleDialog = () => {
  roleDialogType.value = 'add'
  resetRoleForm()
  roleDialogVisible.value = true
}

const editRoleTemplate = (role: any) => {
  roleDialogType.value = 'edit'
  roleForm.id = role.id
  roleForm.name = role.name
  roleForm.role = role.role // 设置角色类型值
  roleForm.description = role.description
  roleForm.permissions = { ...role.permissions }
  roleDialogVisible.value = true
}

const resetRoleForm = () => {
  roleForm.id = 0
  roleForm.name = ''
  roleForm.role = '' // 重置角色类型字段
  roleForm.description = ''
  roleForm.permissions = {
    perm_talent: 0,
    perm_team_leader: 0,
    perm_product: 0,
    perm_order: 0,
    perm_boost: 0,
    perm_performance_overview: 0,
    perm_performance_statistics: 0,
    perm_sample_management: 0,
    perm_hide_product: 0,
    perm_manage_tags: 0,
    perm_merchant_commission_orders: 0,
    perm_reserved_service_fee_orders: 0,
    perm_team_leader_commission_orders: 0,
    permissions: {
      talent: { data: [], operations: [] },
      team_leader: { data: [], operations: [] },
      product: { data: [], operations: [] },
      order: { data: [], operations: [], modules: [] },
      boost: { data: [], operations: [] },
      performance: { data: [], operations: [], modules: [] },
      sample: { data: [], operations: [] },
    },
    permissionOptions: {},
  }
}

const getEnabledModules = (role: any) => {
  const modules = []
  const permissions = role.permissions || {}

  if (permissions.perm_talent) modules.push('talent')
  if (permissions.perm_team_leader) modules.push('team_leader')
  if (permissions.perm_product) modules.push('product')
  if (permissions.perm_order) modules.push('order')
  if (permissions.perm_boost) modules.push('boost')
  if (permissions.perm_performance_overview || permissions.perm_performance_statistics)
    modules.push('performance')
  if (permissions.perm_sample_management) modules.push('sample')

  return modules
}

const submitRoleForm = async () => {
  if (!roleFormRef.value) return

  try {
    await roleFormRef.value.validate()
    roleSubmitting.value = true

    const url =
      roleDialogType.value === 'add' ? '/api/role-templates' : `/api/role-templates/${roleForm.id}`
    const method = roleDialogType.value === 'add' ? 'post' : 'put'

    const response = await axios[method](url, {
      name: roleForm.name,
      role: roleForm.role,
      description: roleForm.description,
      permissions: roleForm.permissions,
    })

    if (response.data.code === 0) {
      ElMessage.success(roleDialogType.value === 'add' ? '创建角色成功' : '更新角色成功')
      roleDialogVisible.value = false
      getRoleTemplates()
    } else {
      ElMessage.error(response.data.message || '操作失败')
    }
  } catch (error) {
    console.error('提交角色表单失败:', error)
    ElMessage.error('操作失败，请检查网络连接')
  } finally {
    roleSubmitting.value = false
  }
}

const deleteRoleTemplate = async (role: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除角色"${role.name}"吗？`, '确认删除', {
      type: 'warning',
    })

    const response = await axios.delete(`/api/role-templates/${role.id}`)
    if (response.data.code === 0) {
      ElMessage.success('删除角色成功')
      getRoleTemplates()
    } else {
      ElMessage.error(response.data.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  }
}

const getModuleName = (module: string) => {
  const names: Record<string, string> = {
    talent: '达人管理',
    team_leader: '团长管理',
    product: '商品管理',
    order: '订单管理',
    boost: '助播管理',
    performance: '业绩管理',
    sample: '寄样管理',
  }
  return names[module] || module
}

// 权限编辑器相关方法
const getPermissionOptions = (moduleName: string, permissionType: string) => {
  // 首先检查角色表单中是否有自定义的权限选项
  if (
    roleForm.permissions.permissionOptions &&
    roleForm.permissions.permissionOptions[moduleName] &&
    roleForm.permissions.permissionOptions[moduleName][permissionType]
  ) {
    return roleForm.permissions.permissionOptions[moduleName][permissionType]
  }

  // 如果没有自定义选项，使用默认选项
  const moduleOptions = (defaultPermissionOptions as any)[moduleName]
  if (moduleOptions && moduleOptions[permissionType]) {
    return moduleOptions[permissionType]
  }

  return []
}

const openPermissionEditor = (moduleName: string, permissionType: string) => {
  currentEditModule.value = getModuleName(moduleName)
  currentEditType.value =
    permissionType === 'data'
      ? '数据'
      : permissionType === 'operations'
        ? '操作'
        : permissionType === 'modules'
          ? '子模块'
          : permissionType

  // 获取当前的权限选项
  const options = getPermissionOptions(moduleName, permissionType)
  currentPermissionOptions.value = JSON.parse(JSON.stringify(options))

  // 如果没有选项，添加一个默认选项
  if (currentPermissionOptions.value.length === 0) {
    currentPermissionOptions.value.push({ label: '', value: '' })
  }

  permissionEditorVisible.value = true
}

const addPermissionOption = () => {
  currentPermissionOptions.value.push({ label: '', value: '' })
}

const removePermissionOption = (index: number) => {
  if (currentPermissionOptions.value.length > 1) {
    currentPermissionOptions.value.splice(index, 1)
  }
}

const savePermissionOptions = () => {
  // 验证权限选项
  const validOptions = currentPermissionOptions.value.filter(
    (option) => option.label.trim() && option.value.trim(),
  )

  if (validOptions.length === 0) {
    ElMessage.warning('请至少添加一个有效的权限选项')
    return
  }

  // 检查是否有重复的值
  const values = validOptions.map((option) => option.value)
  const uniqueValues = [...new Set(values)]
  if (values.length !== uniqueValues.length) {
    ElMessage.warning('权限值不能重复')
    return
  }

  // 获取模块名和权限类型
  const moduleNames: Record<string, string> = {
    达人管理: 'talent',
    团长管理: 'team_leader',
    商品管理: 'product',
    订单管理: 'order',
    助播管理: 'boost',
    业绩管理: 'performance',
    寄样管理: 'sample',
  }

  const permissionTypes: Record<string, string> = {
    数据: 'data',
    操作: 'operations',
    子模块: 'modules',
  }

  const moduleName = moduleNames[currentEditModule.value]
  const permissionType = permissionTypes[currentEditType.value]

  if (!moduleName || !permissionType) {
    ElMessage.error('模块或权限类型错误')
    return
  }

  // 初始化权限选项结构
  if (!roleForm.permissions.permissionOptions) {
    roleForm.permissions.permissionOptions = {}
  }
  if (!roleForm.permissions.permissionOptions[moduleName]) {
    roleForm.permissions.permissionOptions[moduleName] = {}
  }

  // 保存权限选项
  roleForm.permissions.permissionOptions[moduleName][permissionType] = validOptions

  // 更新当前选中的权限值，移除不存在的选项
  const currentValues = (roleForm.permissions.permissions as any)[moduleName][permissionType] || []
  const validValues = validOptions.map((option) => option.value)
  ;(roleForm.permissions.permissions as any)[moduleName][permissionType] = currentValues.filter(
    (value: string) => validValues.includes(value),
  )

  ElMessage.success('权限选项保存成功')
  permissionEditorVisible.value = false
}

// 页面加载时获取数据
onMounted(() => {
  getUserList()
  getRoleTemplates()
})
</script>

<style scoped>
.business-users-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 编辑用户对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  padding: 20px 24px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

:deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 24px;
  background-color: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

/* 表单样式优化 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

/* 权限配置面板样式 */
.permissions-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.permission-card {
  border: 1px solid #e4e7ed;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: white;
}

.permission-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
}

.permission-card :deep(.el-card__header) {
  padding: 14px 18px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e4e7ed;
}

.permission-card :deep(.el-card__body) {
  padding: 0;
}

.permission-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.permission-title {
  font-weight: 600;
  color: #303133;
  flex: 1;
  font-size: 15px;
}

.permission-header :deep(.el-button) {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 6px;
}

.permission-detail {
  padding: 18px;
  background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
  border-top: 1px solid #e4e7ed;
}

.permission-section {
  margin-bottom: 16px;
}

.permission-section:last-child {
  margin-bottom: 0;
}

.permission-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  padding-bottom: 6px;
  border-bottom: 2px solid #e9ecef;
  display: inline-block;
}

.permission-section :deep(.el-checkbox-group) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 8px 12px;
  margin-top: 4px;
}

.permission-section :deep(.el-checkbox) {
  margin-right: 0;
  padding: 6px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
  background-color: white;
  border: 1px solid #e4e7ed;
}

.permission-section :deep(.el-checkbox:hover) {
  background-color: #f0f8ff;
  border-color: #409eff;
}

.permission-section :deep(.el-checkbox.is-checked) {
  background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
  border-color: #409eff;
  color: white;
}

.permission-section :deep(.el-checkbox.is-checked .el-checkbox__label) {
  color: white;
}

.permission-section :deep(.el-checkbox__label) {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.permission-section :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: white;
  border-color: white;
}

.permission-section :deep(.el-checkbox__input.is-checked .el-checkbox__inner::after) {
  border-color: #409eff;
}

/* 对话框底部按钮样式 */
.dialog-footer :deep(.el-button) {
  padding: 10px 24px;
  border-radius: 8px;
  font-weight: 500;
}

.dialog-footer :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
  border: none;
}

/* 搜索栏样式 */
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px;
}

/* 角色管理样式 */
.roles-management {
  padding: 20px;
}

.roles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.roles-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.role-card {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.role-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.role-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.role-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.role-actions {
  display: flex;
  gap: 8px;
}

.role-description p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.role-permissions-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.role-permissions-summary .el-tag {
  font-size: 12px;
}

/* 权限操作按钮样式 */
.permission-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #d4edda;
}

.permission-actions .el-button {
  font-size: 13px;
}

/* 紧凑权限配置面板样式 */
.permissions-compact-panel {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.permission-row {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafafa;
}

.permission-module {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
  flex-shrink: 0;
}

.module-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.permission-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  flex: 1;
}

.permission-group {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
}

.group-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
  min-width: 70px;
  flex-shrink: 0;
}

.permission-group :deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-group :deep(.el-checkbox) {
  margin-right: 0;
  font-size: 13px;
}

.permission-group :deep(.el-checkbox__label) {
  font-size: 13px;
  padding-left: 6px;
}

/* 选项卡样式 */
.management-tabs {
  margin-top: 20px;
}

.management-tabs :deep(.el-tabs__header) {
  margin-bottom: 20px;
}

.management-tabs :deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .permissions-panel {
    grid-template-columns: 1fr;
  }

  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .permission-section :deep(.el-checkbox-group) {
    grid-template-columns: 1fr;
  }

  .search-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .search-filters {
    flex-wrap: wrap;
  }

  .roles-grid {
    grid-template-columns: 1fr;
  }

  .role-card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

/* 权限编辑器样式 */
.permission-editor {
  .editor-header {
    margin-bottom: 20px;
  }

  .permission-options-list {
    margin-bottom: 20px;
  }

  .permission-option-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .editor-tips {
    .el-alert {
      p {
        margin: 4px 0;
        font-size: 13px;
      }
    }
  }
}

/* 权限组头部样式 */
.group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  .group-label {
    font-weight: 500;
    color: #606266;
  }
}
</style>
