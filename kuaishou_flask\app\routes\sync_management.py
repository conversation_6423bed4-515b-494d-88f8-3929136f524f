"""
同步管理相关路由
"""
import logging
import os
from flask import Blueprint, request, jsonify
import jwt
from app.services.performance_sync_service import performance_sync_service

# 创建蓝图
sync_management_bp = Blueprint('sync_management', __name__)

@sync_management_bp.route('/performance/config/update', methods=['POST'])
def update_performance_config():
    """手动更新业绩同步配置"""
    try:
        # 从请求头获取令牌进行权限验证
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]

        try:
            # 验证令牌
            SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_role = payload.get('role', '')
            
            # 只有管理员和运营可以更新配置
            if user_role not in ['admin', 'operation']:
                return jsonify({
                    'code': 403,
                    'message': '权限不足，只有管理员和运营可以更新配置',
                    'data': None
                }), 403
                
        except Exception as e:
            return jsonify({
                'code': 401,
                'message': f'令牌验证失败: {str(e)}',
                'data': None
            }), 401

        # 强制更新配置
        performance_sync_service.force_config_update()
        
        return jsonify({
            'code': 200,
            'message': '业绩同步配置更新成功',
            'data': None
        })
        
    except Exception as e:
        logging.error(f"更新业绩同步配置失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"更新业绩同步配置失败: {str(e)}",
            'data': None
        }), 500

@sync_management_bp.route('/performance/status', methods=['GET'])
def get_performance_sync_status():
    """获取业绩同步状态"""
    try:
        # 从请求头获取令牌进行权限验证
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]

        try:
            # 验证令牌
            SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_role = payload.get('role', '')
            
            # 只有管理员和运营可以查看同步状态
            if user_role not in ['admin', 'operation']:
                return jsonify({
                    'code': 403,
                    'message': '权限不足，只有管理员和运营可以查看同步状态',
                    'data': None
                }), 403
                
        except Exception as e:
            return jsonify({
                'code': 401,
                'message': f'令牌验证失败: {str(e)}',
                'data': None
            }), 401

        # 获取同步状态
        status = performance_sync_service.get_sync_status()
        
        if status is None:
            return jsonify({
                'code': 500,
                'message': '获取同步状态失败',
                'data': None
            }), 500
        
        return jsonify({
            'code': 200,
            'message': '获取业绩同步状态成功',
            'data': status
        })
        
    except Exception as e:
        logging.error(f"获取业绩同步状态失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取业绩同步状态失败: {str(e)}",
            'data': None
        }), 500

@sync_management_bp.route('/performance/config/list', methods=['GET'])
def get_sync_config_list():
    """获取同步配置列表"""
    try:
        # 从请求头获取令牌进行权限验证
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]

        try:
            # 验证令牌
            SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_role = payload.get('role', '')
            
            # 只有管理员和运营可以查看配置列表
            if user_role not in ['admin', 'operation']:
                return jsonify({
                    'code': 403,
                    'message': '权限不足，只有管理员和运营可以查看配置列表',
                    'data': None
                }), 403
                
        except Exception as e:
            return jsonify({
                'code': 401,
                'message': f'令牌验证失败: {str(e)}',
                'data': None
            }), 401

        from app.utils.db_utils import get_connection
        
        connection = None
        cursor = None
        try:
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # 获取所有同步配置
            config_query = """
                SELECT 
                    id,
                    table_name,
                    key_field,
                    key_value,
                    last_sync_date,
                    create_time,
                    update_time
                FROM sync_config
                ORDER BY table_name, key_value
            """
            cursor.execute(config_query)
            configs = cursor.fetchall()
            
            # 格式化时间字段
            for config in configs:
                if config['create_time']:
                    config['create_time'] = config['create_time'].strftime('%Y-%m-%d %H:%M:%S')
                if config['update_time']:
                    config['update_time'] = config['update_time'].strftime('%Y-%m-%d %H:%M:%S')
                if config['last_sync_date']:
                    config['last_sync_date'] = config['last_sync_date'].strftime('%Y-%m-%d')
            
            return jsonify({
                'code': 200,
                'message': '获取同步配置列表成功',
                'data': {
                    'list': configs,
                    'total': len(configs)
                }
            })
            
        except Exception as e:
            logging.error(f"获取同步配置列表失败: {str(e)}")
            return jsonify({
                'code': 500,
                'message': f"获取同步配置列表失败: {str(e)}",
                'data': None
            }), 500
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        
    except Exception as e:
        logging.error(f"获取同步配置列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取同步配置列表失败: {str(e)}",
            'data': None
        }), 500
