import requests
import time
from datetime import datetime, timedelta
import json
import logging

def fetch_order_data(start_time, end_time, cookie, page_size=200, current_page=1, oid=None, 
                    activity_id=None, seller_id=None, item_id=None, order_status=None, 
                    search_delivered=0, settlement_biz_type=1, second_activity_user_id=None, 
                    time_type=1, fund_type=1):
    """
    爬取快手小店订单数据的函数
    
    参数:
    start_time: str - 开始时间，格式为 'YYYY-MM-DD HH:MM:SS'
    end_time: str - 结束时间，格式为 'YYYY-MM-DD HH:MM:SS'
    cookie: str - 请求的cookie字符串
    page_size: int - 每页数据条数
    current_page: int - 当前页码
    oid: int - 订单ID
    activity_id: int - 活动ID
    seller_id: int - 卖家ID
    item_id: int - 商品ID
    order_status: str - 订单状态
    search_delivered: int - 搜索已发货
    settlement_biz_type: int - 结算业务类型
    second_activity_user_id: int - 二级活动用户ID
    time_type: int - 时间类型
    fund_type: int - 资金类型
    
    返回:
    list - 处理后的订单数据数组
    """
    url = "https://cps.kwaixiaodian.com/distribute/pc/investment/order/list"
    
    # 将时间字符串转换为时间戳（毫秒）
    start_timestamp = int(datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').timestamp() * 1000)
    end_timestamp = int(datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').timestamp() * 1000)
    
    # 构建请求参数
    payload = {
        "pageSize": page_size,
        "current": current_page,
        "limit": page_size,
        "offset": (current_page - 1) * page_size,
        "orderCreateTimeStart": start_timestamp,
        "orderCreateTimeEnd": end_timestamp,
        "timeType": time_type,
        "settlementBizType": settlement_biz_type,
        "searchDelivered": search_delivered,
        "fundType": fund_type
    }
    
    
    # 添加可选参数
    if oid is not None:
        payload["oid"] = oid
    if activity_id is not None:
        payload["activityId"] = activity_id
    if seller_id is not None:
        payload["sellerId"] = seller_id
    if item_id is not None:
        payload["itemId"] = item_id
    if order_status is not None:
        payload["orderStatus"] = order_status
    if second_activity_user_id is not None:
        payload["secondActivityUserId"] = second_activity_user_id
    
    headers = {
        "authority": "cps.kwaixiaodian.com",
        "accept": "application/json",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "cookie": cookie,
        "kpf": "PC_WEB",
        "kpn": "KWAIXIAODIAN",
        "referer": "https://cps.kwaixiaodian.com/pc/leader/base/order-manage",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"
    }
    
    try:
        response = requests.get(url, params=payload, headers=headers)
        response.raise_for_status()
        
        # 解析响应数据
        result = response.json()
        
        # 检查响应是否成功
        if result.get("result") != 1:
            return {"error": result.get("error_msg", "未知错误")}
        
        # 提取订单列表
        orders = result.get("data", {}).get("list", [])
        
        # 处理订单数据，添加可读的时间格式
        for order in orders:
            if "orderCreateTime" in order:
                timestamp = order["orderCreateTime"]
                order["orderCreateTimeFormatted"] = datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')
            
            if "sendTime" in order and order["sendTime"] > 0:
                timestamp = order["sendTime"]
                order["sendTimeFormatted"] = datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')
            
            if "recvTime" in order and order["recvTime"] > 0:
                timestamp = order["recvTime"]
                order["recvTimeFormatted"] = datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')
            
            if "settlementTime" in order and order["settlementTime"] > 0:
                timestamp = order["settlementTime"]
                order["settlementTimeFormatted"] = datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')
        
        # 返回订单数组和总数
        return {
            "orders": orders,
            "total": result.get("data", {}).get("total", 0),
            "current_page": current_page,
            "page_size": page_size
        }
        
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}

def get_all_orders(start_time, end_time, cookie, page_size=200, max_pages=None, **kwargs):
    """
    获取所有订单数据（分页获取）
    
    参数:
    start_time: str - 开始时间，格式为 'YYYY-MM-DD HH:MM:SS'
    end_time: str - 结束时间，格式为 'YYYY-MM-DD HH:MM:SS'
    cookie: str - 请求的cookie字符串
    page_size: int - 每页数据条数
    max_pages: int - 最大获取页数，None表示获取所有页
    **kwargs: 其他传递给fetch_order_data的参数
    
    返回:
    list - 所有订单数据
    """
    all_orders = []
    current_page = 1
    total = None
    
    while True:
        result = fetch_order_data(
            start_time=start_time,
            end_time=end_time,
            cookie=cookie,
            page_size=page_size,
            current_page=current_page,
            **kwargs
        )
        
        if "error" in result:
            print(f"获取第{current_page}页数据时出错: {result['error']}")
            break
        
        orders = result.get("orders", [])
        all_orders.extend(orders)
        
        if total is None:
            total = result.get("total", 0)
            print(f"总订单数: {total}")
        
        print(f"已获取第{current_page}页数据, 当前共{len(all_orders)}条订单")
        
        # 判断是否继续获取下一页
        if max_pages and current_page >= max_pages:
            print(f"已达到最大页数限制: {max_pages}页")
            break
        
        if len(all_orders) >= total:
            print("已获取所有订单数据")
            break
        
        current_page += 1
        # 添加延迟，避免请求过于频繁
        time.sleep(1)
    
    return all_orders

def save_orders_to_file(orders, filename="orders.json"):
    """
    将订单数据保存到文件
    
    参数:
    orders: list - 订单数据列表
    filename: str - 保存的文件名
    """
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(orders, f, ensure_ascii=False, indent=2)
    print(f"订单数据已保存至 {filename}")

# 使用示例
if __name__ == "__main__":
    # 示例cookie，实际使用时需要替换
    cookie = "_did=web_6109536701C516C8; did=web_m5p31mdccrdqpmzfrlb5k0pb18ya7tn1; sid=kuaishou.shop.b; bUserId=1000040627146; userId=2885180614; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAE3r-U2Ms7OrV5g4kNnO5hVo_aWmWtXX4w5R759KFmgRhXPJ7lspBwtKN3L7t7dnwMvDhgqhJoLVnOsUnVfGz9KQL0uNO9GAGugnKX6p9z__PK42e1sTJ3S2wNZiXP_Znr0pp_6xHaOuK-FzuZRlyAAxXy_c3WfibyCsCKKuHFpR8xdEE9YmTI5OIJpF4vjVTi1cwuTJ2UlPXs_EuOQqvlxGhIoDb6opzlycuz0NgNjDyqUN2IiIDg3GMearGLKsf9u_EH_0nxJQGTQmoopSDBC2MU9hxYQKAUwAQ; kuaishou.shop.b_ph=bb711497ef48a08565c4c04834fcc356697d"
    
    # 获取一周内的订单数据
    start_time = "2025-07-03 00:00:00"
    end_time = "2025-07-09 23:59:59"
    
    # 获取单页数据示例
    # result = fetch_order_data(start_time, end_time, cookie)
    # if "error" not in result:
    #     print(f"获取到 {len(result.get('orders', []))} 条订单数据")
    
    # 获取所有数据示例（限制最多获取5页）
    all_orders = get_all_orders(start_time, end_time, cookie, max_pages=2)
    save_orders_to_file(all_orders)

class KuaishouOrderCrawler:
    """快手订单爬虫类，支持获取订单总数和分页获取"""

    def __init__(self, cookie):
        self.cookie = cookie
        self.base_url = "https://cps.kwaixiaodian.com/distribute/pc/investment/order/list"

    def get_total_order_count(self, start_date, end_date=None):
        """
        获取订单总数

        Args:
            start_date: str - 开始日期，格式为 'YYYY-MM-DD'
            end_date: str - 结束日期，格式为 'YYYY-MM-DD'，默认为今天

        Returns:
            int - 订单总数，失败返回None
        """
        try:
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')

            start_time = f"{start_date} 00:00:00"
            end_time = f"{end_date} 23:59:59"

            # 只获取第一页来获取总数
            result = fetch_order_data(start_time, end_time, self.cookie, page_size=1, current_page=1)

            if isinstance(result, dict) and 'total' in result:
                return result['total']
            else:
                logging.error(f"获取订单总数失败: {result}")
                return None

        except Exception as e:
            logging.error(f"获取订单总数异常: {str(e)}")
            return None

    def get_orders_by_page(self, start_date, page, page_size=200, end_date=None):
        """
        按页获取订单数据

        Args:
            start_date: str - 开始日期，格式为 'YYYY-MM-DD'
            page: int - 页码（从1开始）
            page_size: int - 每页数量
            end_date: str - 结束日期，格式为 'YYYY-MM-DD'，默认为今天

        Returns:
            list - 订单数据列表
        """
        try:
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')

            start_time = f"{start_date} 00:00:00"
            end_time = f"{end_date} 23:59:59"

            result = fetch_order_data(start_time, end_time, self.cookie,
                                    page_size=page_size, current_page=page)

            if isinstance(result, dict) and 'orders' in result:
                return result['orders']
            else:
                logging.error(f"获取第{page}页订单数据失败: {result}")
                return []

        except Exception as e:
            logging.error(f"获取第{page}页订单数据异常: {str(e)}")
            return []

    def get_orders_batch(self, start_date, start_page, page_count, page_size=200, end_date=None):
        """
        批量获取多页订单数据

        Args:
            start_date: str - 开始日期
            start_page: int - 起始页码
            page_count: int - 获取页数
            page_size: int - 每页数量
            end_date: str - 结束日期

        Returns:
            list - 所有订单数据列表
        """
        all_orders = []

        for page in range(start_page, start_page + page_count):
            orders = self.get_orders_by_page(start_date, page, page_size, end_date)
            if orders:
                all_orders.extend(orders)
                logging.info(f"获取第{page}页订单数据成功，共{len(orders)}条")
            else:
                logging.warning(f"第{page}页订单数据为空")
                break

            # 避免请求过快
            time.sleep(0.5)

        return all_orders
