<template>
  <div class="database-monitor-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>数据库连接池监控</h3>
          <div class="header-operations">
            <el-button type="primary" @click="refreshStatus" :loading="loading" size="small">
              刷新状态
            </el-button>
            <el-button
              type="warning"
              @click="cleanupConnections"
              :loading="cleanupLoading"
              size="small"
            >
              记录状态
            </el-button>
          </div>
        </div>
      </template>

      <!-- 健康状态概览 -->
      <div class="health-overview">
        <el-alert
          :title="getHealthTitle()"
          :type="getHealthType()"
          :description="getHealthDescription()"
          show-icon
          :closable="false"
        />
      </div>

      <!-- 连接池统计 -->
      <div class="stats-section">
        <h4>连接池统计</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="池大小" :value="dbStats.pool_size || 32" />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="活跃连接"
              :value="dbStats.active_connections || 0"
              :value-style="getConnectionStyle()"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic title="可用连接" :value="dbStats.available_connections || 0" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="总请求数" :value="dbStats.total_requests || 0" />
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="6">
            <el-statistic title="失败请求" :value="dbStats.failed_requests || 0" />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="失败率"
              :value="(dbStats.failure_rate || 0).toFixed(2)"
              suffix="%"
            />
          </el-col>
          <el-col :span="12">
            <div class="connection-progress">
              <span>连接池使用率</span>
              <el-progress
                :percentage="getUsagePercentage()"
                :color="getProgressColor()"
                :stroke-width="20"
                text-inside
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 优化建议 -->
      <div class="recommendations-section" v-if="recommendations.length > 0">
        <h4>优化建议</h4>
        <ul class="recommendations-list">
          <li v-for="(recommendation, index) in recommendations" :key="index">
            {{ recommendation }}
          </li>
        </ul>
      </div>

      <!-- 最后更新时间 -->
      <div class="last-update">
        <el-text type="info" size="small"> 最后更新: {{ lastUpdateTime }} </el-text>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

// 响应式数据
const loading = ref(false)
const cleanupLoading = ref(false)
const healthStatus = ref('healthy')
const dbStats = ref({})
const recommendations = ref([])
const lastUpdateTime = ref('')
const autoRefreshTimer = ref(null)

// 获取数据库状态
const fetchDatabaseStatus = async () => {
  try {
    loading.value = true
    const response = await axios.get('/api/db/status')

    if (response.data.code === 0) {
      const data = response.data.data
      healthStatus.value = data.health_status
      dbStats.value = data.pool_stats
      recommendations.value = data.recommendations
      lastUpdateTime.value = new Date().toLocaleString()
    } else {
      ElMessage.error(response.data.message || '获取数据库状态失败')
    }
  } catch (error) {
    console.error('获取数据库状态失败:', error)
    ElMessage.error('获取数据库状态失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 刷新状态
const refreshStatus = () => {
  fetchDatabaseStatus()
}

// 清理连接池
const cleanupConnections = async () => {
  try {
    cleanupLoading.value = true
    const response = await axios.post('/api/db/cleanup')

    if (response.data.code === 0) {
      ElMessage.success(response.data.message)
      // 记录后刷新状态
      setTimeout(() => {
        fetchDatabaseStatus()
      }, 1000)
    } else {
      ElMessage.error(response.data.message || '操作失败')
    }
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败，请检查网络连接')
  } finally {
    cleanupLoading.value = false
  }
}

// 获取健康状态标题
const getHealthTitle = () => {
  switch (healthStatus.value) {
    case 'critical':
      return '🚨 连接池状态危险'
    case 'warning':
      return '⚠️ 连接池状态警告'
    default:
      return '✅ 连接池状态正常'
  }
}

// 获取健康状态类型
const getHealthType = () => {
  switch (healthStatus.value) {
    case 'critical':
      return 'error'
    case 'warning':
      return 'warning'
    default:
      return 'success'
  }
}

// 获取健康状态描述
const getHealthDescription = () => {
  switch (healthStatus.value) {
    case 'critical':
      return '连接池几乎耗尽，请立即处理'
    case 'warning':
      return '连接池使用率较高，建议关注'
    default:
      return '连接池运行正常'
  }
}

// 获取连接数样式
const getConnectionStyle = () => {
  const active = dbStats.value.active_connections || 0
  if (active >= 30) return { color: '#F56C6C' }
  if (active >= 28) return { color: '#E6A23C' }
  return { color: '#67C23A' }
}

// 获取使用率百分比
const getUsagePercentage = () => {
  const active = dbStats.value.active_connections || 0
  const total = dbStats.value.pool_size || 32
  return Math.round((active / total) * 100)
}

// 获取进度条颜色
const getProgressColor = () => {
  const percentage = getUsagePercentage()
  if (percentage >= 94) return '#F56C6C' // 30/32
  if (percentage >= 88) return '#E6A23C' // 28/32
  return '#67C23A'
}

// 启动自动刷新
const startAutoRefresh = () => {
  autoRefreshTimer.value = setInterval(() => {
    fetchDatabaseStatus()
  }, 30000) // 每30秒刷新一次
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
    autoRefreshTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  fetchDatabaseStatus()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.database-monitor-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.health-overview {
  margin-bottom: 20px;
}

.stats-section {
  margin-bottom: 20px;
}

.stats-section h4 {
  margin-bottom: 15px;
  color: #303133;
}

.connection-progress {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.recommendations-section {
  margin-bottom: 20px;
}

.recommendations-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

.recommendations-list {
  list-style-type: disc;
  padding-left: 20px;
}

.recommendations-list li {
  margin-bottom: 5px;
  color: #606266;
}

.last-update {
  text-align: right;
  margin-top: 20px;
}
</style>
