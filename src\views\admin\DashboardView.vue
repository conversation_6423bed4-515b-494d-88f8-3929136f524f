<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h2>{{ isAdmin ? '管理员仪表盘' : '运营仪表盘' }}</h2>
      <p>欢迎回来，{{ userName }}</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="dashboard-card">
          <template #header>
            <div class="card-header">
              <h3>系统概览</h3>
            </div>
          </template>
          <div class="dashboard-stats">
            <div class="stat-item">
              <div class="stat-value">{{ stats.business_users || 0 }}</div>
              <div class="stat-label">商务用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ stats.operation_users || 0 }}</div>
              <div class="stat-label">运营用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ stats.talents || 0 }}</div>
              <div class="stat-label">达人数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="16">
        <el-card class="dashboard-card">
          <template #header>
            <div class="card-header">
              <h3>最近用户</h3>
              <el-button v-if="isAdmin" type="primary" size="small" @click="navigateToUsers">
                管理用户
              </el-button>
            </div>
          </template>
          <el-table :data="recentUsers" style="width: 100%" v-loading="loading">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="username" label="账号" width="120" />
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="role" label="角色" width="100">
              <template #default="scope">
                <el-tag :type="getRoleTagType(scope.row.role)">{{
                  getRoleName(scope.row.role)
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="update_time" label="更新时间" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card class="dashboard-card">
          <template #header>
            <div class="card-header">
              <h3>商务用户</h3>
              <el-button
                v-if="isAdmin"
                type="primary"
                size="small"
                @click="navigateToUsersByRole('business')"
              >
                管理商务
              </el-button>
            </div>
          </template>
          <el-table :data="businessUsers" style="width: 100%" v-loading="loading">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="username" label="账号" width="120" />
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="update_time" label="更新时间" />
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card class="dashboard-card">
          <template #header>
            <div class="card-header">
              <h3>运营用户</h3>
              <el-button
                v-if="isAdmin"
                type="primary"
                size="small"
                @click="navigateToUsersByRole('operation')"
              >
                管理运营
              </el-button>
            </div>
          </template>
          <el-table :data="operationUsers" style="width: 100%" v-loading="loading">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="username" label="账号" width="120" />
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="update_time" label="更新时间" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px" v-if="isAdmin">
      <el-col :span="24">
        <el-card class="dashboard-card">
          <template #header>
            <div class="card-header">
              <h3>达人转换配置</h3>
              <el-button type="primary" size="small" @click="loadConversionConfig">
                刷新配置
              </el-button>
            </div>
          </template>
          <el-table :data="conversionConfigs" style="width: 100%" v-loading="configLoading">
            <el-table-column prop="rule_name" label="规则名称" width="200" />
            <el-table-column prop="rule_description" label="规则描述" min-width="300" />
            <el-table-column prop="check_days" label="检查天数" width="100" align="center">
              <template #default="scope">
                <span>{{ scope.row.check_days }}天</span>
              </template>
            </el-table-column>
            <el-table-column prop="gmv_threshold" label="GMV阈值" width="120" align="center">
              <template #default="scope">
                <span>{{ formatAmount(scope.row.gmv_threshold) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="is_enabled" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.is_enabled ? 'success' : 'danger'">
                  {{ scope.row.is_enabled ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button type="primary" size="small" @click="editConfig(scope.row)">
                  编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="24">
        <el-card class="dashboard-card">
          <template #header>
            <div class="card-header">
              <h3>系统信息</h3>
            </div>
          </template>
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">系统名称：</span>
              <span class="info-value">苍穹严选商务管理系统</span>
            </div>
            <div class="info-item">
              <span class="info-label">当前版本：</span>
              <span class="info-value">1.0.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">上线时间：</span>
              <span class="info-value">2025-07-01</span>
            </div>
            <div class="info-item">
              <span class="info-label">系统状态：</span>
              <span class="info-value">
                <el-tag type="success">运行中</el-tag>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 编辑配置对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="'编辑配置 - ' + (currentConfig?.rule_name || '')"
      width="600px"
    >
      <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="120px">
        <el-form-item label="规则名称" prop="rule_name">
          <el-input v-model="editForm.rule_name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="规则描述" prop="rule_description">
          <el-input
            v-model="editForm.rule_description"
            type="textarea"
            :rows="3"
            placeholder="请输入规则描述"
          />
        </el-form-item>
        <el-form-item label="检查天数" prop="check_days">
          <el-input-number
            v-model="editForm.check_days"
            :min="1"
            :max="365"
            placeholder="请输入检查天数"
          />
          <span class="form-tip">达人状态检查的天数间隔</span>
        </el-form-item>
        <el-form-item label="GMV阈值" prop="gmv_threshold">
          <el-input-number
            v-model="editForm.gmv_threshold"
            :min="0"
            :precision="2"
            placeholder="请输入GMV阈值"
          />
          <span class="form-tip">单位：元，达人需要达到的GMV阈值</span>
        </el-form-item>
        <el-form-item label="启用状态" prop="is_enabled">
          <el-switch
            v-model="editForm.is_enabled"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveConfig" :loading="saveLoading"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const router = useRouter()
const route = useRoute()

// 判断是否是管理员
const isAdmin = computed(() => {
  return route.path.startsWith('/admin')
})

// 用户名
const userName = ref('')

// 加载状态
const loading = ref(false)

// 统计数据
const stats = reactive({
  business_users: 0,
  operation_users: 0,
  talents: 0,
})

// 用户数据
const recentUsers = ref([])
const businessUsers = ref([])
const operationUsers = ref([])

// 达人转换配置相关
const conversionConfigs = ref([])
const configLoading = ref(false)
const editDialogVisible = ref(false)
const saveLoading = ref(false)
const currentConfig = ref<any>(null)

// 编辑表单
const editForm = reactive({
  rule_name: '',
  rule_description: '',
  check_days: 1,
  gmv_threshold: 0,
  is_enabled: 1,
})

// 表单验证规则
const editRules = {
  rule_name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  rule_description: [{ required: true, message: '请输入规则描述', trigger: 'blur' }],
  check_days: [
    { required: true, message: '请输入检查天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 365, message: '天数必须在1-365之间', trigger: 'blur' },
  ],
  gmv_threshold: [
    { required: true, message: '请输入GMV阈值', trigger: 'blur' },
    { type: 'number', min: 0, message: 'GMV阈值不能小于0', trigger: 'blur' },
  ],
}

// 获取用户信息
const getUserInfo = () => {
  const userStr = localStorage.getItem('user')
  if (userStr) {
    try {
      const user = JSON.parse(userStr)
      userName.value = user.name || '用户'
    } catch (error) {
      console.error('解析用户信息失败:', error)
    }
  }
}

// 获取系统统计信息
const getSystemStats = async () => {
  loading.value = true
  try {
    const response = await axios.get('/api/auth/system-stats')
    if (response.data.code === 0) {
      const data = response.data.data
      stats.business_users = data.stats.business_users
      stats.operation_users = data.stats.operation_users
      stats.talents = data.stats.talents
      recentUsers.value = data.recent_users
      businessUsers.value = data.business_users
      operationUsers.value = data.operation_users
    } else {
      ElMessage.error(response.data.message || '获取系统统计信息失败')
    }
  } catch (error) {
    console.error('获取系统统计信息失败:', error)
    ElMessage.error('获取系统统计信息失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 获取角色名称
const getRoleName = (role: string): string => {
  switch (role) {
    case 'admin':
      return '管理员'
    case 'business':
      return '商务'
    case 'operation':
      return '运营'
    default:
      return '未知'
  }
}

// 获取角色标签类型
const getRoleTagType = (role: string): string => {
  switch (role) {
    case 'admin':
      return 'danger'
    case 'business':
      return 'primary'
    case 'operation':
      return 'success'
    default:
      return 'info'
  }
}

// 导航到用户管理页面
const navigateToUsers = () => {
  router.push('/admin/business-users')
}

// 导航到特定角色的用户管理页面
const navigateToUsersByRole = (role: string) => {
  router.push(`/admin/business-users?role=${role}`)
}

// 格式化金额
const formatAmount = (amount: number): string => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + '万'
  }
  return amount.toLocaleString()
}

// 加载转换配置
const loadConversionConfig = async () => {
  if (!isAdmin.value) return

  configLoading.value = true
  try {
    const response = await axios.get('/api/talent/conversion-config')
    if (response.data.code === 0) {
      conversionConfigs.value = response.data.data
    } else {
      ElMessage.error(response.data.message || '获取配置失败')
    }
  } catch (error) {
    console.error('获取转换配置失败:', error)
    ElMessage.error('获取转换配置失败')
  } finally {
    configLoading.value = false
  }
}

// 编辑配置
const editConfig = (config: any) => {
  currentConfig.value = config
  Object.assign(editForm, {
    rule_name: config.rule_name,
    rule_description: config.rule_description,
    check_days: config.check_days,
    gmv_threshold: config.gmv_threshold,
    is_enabled: config.is_enabled,
  })
  editDialogVisible.value = true
}

// 保存配置
const saveConfig = async () => {
  if (!currentConfig.value) return

  saveLoading.value = true
  try {
    const response = await axios.put(
      `/api/talent/conversion-config/${currentConfig.value.id}`,
      editForm,
    )
    if (response.data.code === 0) {
      ElMessage.success('保存成功')
      editDialogVisible.value = false
      await loadConversionConfig()
    } else {
      ElMessage.error(response.data.message || '保存失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saveLoading.value = false
  }
}

onMounted(() => {
  getUserInfo()
  getSystemStats()
  if (isAdmin.value) {
    loadConversionConfig()
  }
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 20px;
}

.dashboard-header h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.dashboard-header p {
  margin: 10px 0 0 0;
  color: #606266;
}

.dashboard-card {
  height: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.dashboard-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 36px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  margin-top: 10px;
  color: #606266;
}

.system-info {
  padding: 10px 0;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
}

.info-label {
  width: 120px;
  color: #606266;
  font-weight: 500;
}

.info-value {
  flex: 1;
  color: #303133;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}
</style>
