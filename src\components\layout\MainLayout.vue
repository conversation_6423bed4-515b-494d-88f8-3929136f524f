<template>
  <div class="main-layout">
    <el-container class="layout-container">
      <el-aside width="220px" class="sidebar">
        <div v-if="currentUser.role === 'admin'" class="logo-container">
          <h2>苍穹严选管理后台</h2>
        </div>
        <div v-else-if="currentUser.role === 'operation'" class="logo-container">
          <h2>苍穹严选运营后台</h2>
        </div>
        <div v-else class="logo-container">
          <h2>苍穹严选商务后台</h2>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          background-color="#304156"
          text-color="#fff"
          active-text-color="#ffd04b"
          router
        >
          <el-menu-item :index="homeRoute">
            <el-icon><HomeFilled /></el-icon>
            <span>首页</span>
          </el-menu-item>

          <!-- 根据角色和权限显示不同菜单 -->
          <!-- 达人管理 - 根据权限显示 -->
          <el-sub-menu
            v-if="
              (currentUser.role === 'business' || currentUser.role === 'operation') &&
              permissionStore.modulePermissions.talent()
            "
            index="/talent"
          >
            <template #title>
              <el-icon><User /></el-icon>
              <span>达人管理</span>
            </template>
            <el-menu-item index="/talent/public">公海达人</el-menu-item>
            <el-menu-item index="/talent/exclusive">专属达人</el-menu-item>
            <el-menu-item index="/talent/special">专享达人</el-menu-item>
            <el-menu-item index="/talent/shared">共享达人</el-menu-item>
          </el-sub-menu>

          <!-- 团长管理 - 根据权限显示 -->
          <el-sub-menu
            v-if="
              (currentUser.role === 'business' || currentUser.role === 'operation') &&
              permissionStore.modulePermissions.teamLeader()
            "
            index="/team"
          >
            <template #title>
              <el-icon><UserFilled /></el-icon>
              <span>团长管理</span>
            </template>
            <el-menu-item index="/team/public">公海团长</el-menu-item>
            <el-menu-item index="/team/exclusive">专属团长</el-menu-item>
            <el-menu-item index="/team/special">专享团长</el-menu-item>
            <el-menu-item index="/team/shared">共享团长</el-menu-item>
          </el-sub-menu>

          <!-- 商品管理 - 根据权限显示 -->
          <el-sub-menu v-if="permissionStore.modulePermissions.product()" index="/product">
            <template #title>
              <el-icon><Goods /></el-icon>
              <span>商品管理</span>
            </template>
            <template v-if="currentUser.role === 'business'">
              <el-menu-item index="/product/my-promotion">我的推广商品</el-menu-item>
            </template>
            <el-menu-item index="/product/list">商品管理</el-menu-item>
            <el-menu-item index="/product/hot-ranking">爆品排行</el-menu-item>
            <!-- 隐藏商品 - 只有有权限的用户才能看到 -->
            <el-menu-item
              v-if="
                currentUser.role === 'admin' ||
                (currentUser.role === 'operation' &&
                  permissionStore.modulePermissions.hideProduct())
              "
              index="/product/hidden"
            >
              隐藏商品
            </el-menu-item>
          </el-sub-menu>

          <!-- 订单管理 - 根据权限显示 -->
          <el-sub-menu v-if="permissionStore.modulePermissions.order()" index="order">
            <template #title>
              <el-icon><List /></el-icon>
              <span>订单管理</span>
            </template>
            <el-menu-item index="/order/search">全部订单</el-menu-item>
            <el-menu-item
              v-if="permissionStore.modulePermissions.merchantCommissionOrders()"
              index="/order/merchant-commission"
            >
              商家返佣订单
            </el-menu-item>
            <el-menu-item
              v-if="permissionStore.modulePermissions.reservedServiceFeeOrders()"
              index="/order/reserved-service-fee"
            >
              预留服务费订单
            </el-menu-item>
            <el-menu-item
              v-if="permissionStore.modulePermissions.teamLeaderCommissionOrders()"
              index="/order/team-leader-commission"
            >
              团长返佣订单
            </el-menu-item>
          </el-sub-menu>

          <!-- 助播管理 - 根据权限显示 -->
          <el-sub-menu
            v-if="
              (currentUser.role === 'business' || currentUser.role === 'operation') &&
              permissionStore.modulePermissions.boost()
            "
            index="/boost"
          >
            <template #title>
              <el-icon><Promotion /></el-icon>
              <span>助播管理</span>
            </template>
            <el-menu-item index="/boost/management">助播管理</el-menu-item>
          </el-sub-menu>

          <!-- 业绩管理 - 根据权限显示 -->
          <el-sub-menu
            v-if="
              (currentUser.role === 'business' || currentUser.role === 'operation') &&
              (permissionStore.modulePermissions.performanceOverview() ||
                permissionStore.modulePermissions.performanceStatistics())
            "
            index="/performance"
          >
            <template #title>
              <el-icon><DataLine /></el-icon>
              <span>业绩管理</span>
            </template>
            <el-menu-item
              v-if="permissionStore.modulePermissions.performanceOverview()"
              index="/performance-overview"
            >
              商务业绩概览
            </el-menu-item>
            <el-menu-item
              v-if="permissionStore.modulePermissions.performanceStatistics()"
              index="/performance-statistics"
            >
              商务业绩统计
            </el-menu-item>
            <el-menu-item
              v-if="permissionStore.modulePermissions.performanceStatistics()"
              index="/talent-statistics"
            >
              达人业绩统计
            </el-menu-item>
          </el-sub-menu>

          <!-- 寄样管理 - 根据权限显示 -->
          <el-menu-item
            v-if="permissionStore.modulePermissions.sampleManagement()"
            index="/sample/management"
          >
            <el-icon><Box /></el-icon>
            <span>寄样管理</span>
          </el-menu-item>

          <!-- 所有角色都显示个人设置 -->
          <el-menu-item index="/profile">
            <el-icon><Setting /></el-icon>
            <span>个人设置</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <el-container class="main-container">
        <el-header height="60px">
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="el-dropdown-link">
                <el-avatar
                  size="small"
                  :src="currentUser.avatar || '/assets/default-avatar.png'"
                  class="user-avatar"
                ></el-avatar>
                {{ currentUser.name || '未登录' }}
                <span class="role-tag" :class="getRoleClass()">{{ getRoleText() }}</span>
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                  <el-dropdown-item command="changePassword">修改密码</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <el-main>
          <router-view />
        </el-main>
      </el-container>
    </el-container>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="passwordDialogVisible" title="修改密码" width="30%">
      <el-form
        :model="passwordForm"
        :rules="passwordRules"
        ref="passwordFormRef"
        label-width="100px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitChangePassword" :loading="changing"
            >确认</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import axios from 'axios'
import {
  HomeFilled,
  User,
  Goods,
  List,
  Promotion,
  UserFilled,
  DataLine,
  Setting,
  ArrowDown,
  Box, // 添加 Box 图标
} from '@element-plus/icons-vue'
import { usePermissionStore } from '@/stores/permission'

const route = useRoute()
const router = useRouter()
const permissionStore = usePermissionStore()

const activeMenu = computed(() => {
  return route.path
})

// 当前用户信息
const currentUser = ref({
  id: '',
  name: '',
  username: '',
  role: 'business',
  avatar: '',
  phone: '',
})

// 获取当前用户信息
const getUserInfo = () => {
  const userStr = localStorage.getItem('user')
  if (userStr) {
    try {
      const user = JSON.parse(userStr)
      currentUser.value = user
    } catch (error) {
      console.error('解析用户信息失败:', error)
    }
  }
}

// 监听localStorage变化，实时更新头像
window.addEventListener('storage', (e) => {
  if (e.key === 'user' && e.newValue) {
    try {
      const user = JSON.parse(e.newValue)
      currentUser.value = user
    } catch (error) {
      console.error('解析用户信息失败:', error)
    }
  }
})

// 定期检查用户信息更新
const checkUserInfoInterval = setInterval(() => {
  getUserInfo()
}, 2000) // 每2秒检查一次

onMounted(async () => {
  getUserInfo()

  // 获取用户权限信息
  if (currentUser.value.role !== 'admin') {
    await permissionStore.fetchUserPermissions()
  }

  // 根据角色重定向到对应首页
  if (route.path === '/') {
    const role = localStorage.getItem('userRole') || currentUser.value.role
    if (role === 'operation') {
      router.replace('/operation-home')
    } else if (role === 'admin') {
      router.replace('/admin/dashboard')
    }
  }
})

// 组件卸载时清除定时器
onUnmounted(() => {
  clearInterval(checkUserInfoInterval)
})

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  if (command === 'logout') {
    handleLogout()
  } else if (command === 'changePassword') {
    passwordDialogVisible.value = true
  } else if (command === 'profile') {
    router.push('/profile')
  }
}

// 退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确认退出登录吗?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await axios.post('/api/auth/logout')
        // 清除本地存储的令牌和用户信息
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        localStorage.removeItem('userRole')
        localStorage.removeItem('userAvatar')
        // 清除权限信息
        permissionStore.clearPermissions()
        ElMessage.success('退出登录成功')
        // 跳转到登录页
        router.push('/login')
      } catch (error) {
        console.error('退出登录失败:', error)
        // 即使API调用失败，也清除本地存储并跳转到登录页
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        localStorage.removeItem('userRole')
        localStorage.removeItem('userAvatar')
        // 清除权限信息
        permissionStore.clearPermissions()
        router.push('/login')
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 修改密码相关
const passwordDialogVisible = ref(false)
const passwordFormRef = ref<FormInstance>()
const changing = ref(false)

const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// 自定义验证规则：确认密码必须与新密码一致
const validateConfirmPassword = (_rule: any, value: string, callback: any) => {
  if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const passwordRules = reactive({
  oldPassword: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' },
  ],
})

// 提交修改密码
const submitChangePassword = async () => {
  if (!passwordFormRef.value) return

  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      changing.value = true
      try {
        const response = await axios.post('/api/auth/change-password', {
          old_password: passwordForm.oldPassword,
          new_password: passwordForm.newPassword,
        })

        if (response.data.code === 0) {
          ElMessage.success('密码修改成功，请重新登录')
          passwordDialogVisible.value = false
          // 清除表单
          passwordForm.oldPassword = ''
          passwordForm.newPassword = ''
          passwordForm.confirmPassword = ''
          // 退出登录
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          router.push('/login')
        } else {
          ElMessage.error(response.data.message || '密码修改失败')
        }
      } catch (error: any) {
        if (error.response && error.response.data) {
          ElMessage.error(error.response.data.message || '密码修改失败')
        } else {
          ElMessage.error('密码修改失败，请检查网络连接')
        }
      } finally {
        changing.value = false
      }
    }
  })
}

// 获取角色文本
const getRoleText = () => {
  switch (currentUser.value.role) {
    case 'admin':
      return '管理员'
    case 'operation':
      return '运营'
    case 'business':
    default:
      return '商务'
  }
}

// 获取角色样式类
const getRoleClass = () => {
  switch (currentUser.value.role) {
    case 'admin':
      return 'role-admin'
    case 'operation':
      return 'role-operation'
    case 'business':
    default:
      return 'role-business'
  }
}

// 添加计算属性来动态确定首页路由
const homeRoute = computed(() => {
  if (currentUser.value.role === 'operation') {
    return '/operation-home'
  } else if (currentUser.value.role === 'admin') {
    return '/admin/dashboard'
  } else {
    return '/'
  }
})
</script>

<style scoped>
.main-layout {
  height: 100%;
  width: 100vw;
  overflow: hidden;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0;
  margin: 0;
  max-width: 100%;
}

.layout-container {
  width: 100%;
  height: 100%;
  display: flex;
  margin: 0;
  padding: 0;
  max-width: 100vw;
}

.sidebar {
  background-color: #304156;
  height: 100%;
  overflow-x: hidden;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.el-header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 20px;
  width: 100%;
}

.header-right {
  margin-right: 20px;
}

.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.user-avatar {
  margin-right: 8px;
}

.role-tag {
  display: inline-block;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
  color: #fff;
}

.role-admin {
  background-color: #f56c6c;
}

.role-operation {
  background-color: #67c23a;
}

.role-business {
  background-color: #409eff;
}

.el-main {
  padding: 0;
  background-color: #f5f7fa;
  height: calc(100vh - 60px);
  overflow: auto;
  width: 100%;
  flex: 1;
  margin: 0;
  max-width: 100%;
}

:deep(.el-container) {
  height: 100%;
  width: 100%;
  display: flex;
  margin: 0;
  padding: 0;
  max-width: 100vw;
}

.main-container {
  flex: 1;
  width: calc(100% - 220px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  max-width: calc(100vw - 220px);
}
.header h2 {
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
  font-weight: bold;
  font-size: 18px;
  color: #333;
  margin: 0;
  padding: 0;
  letter-spacing: 1px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}
</style>
