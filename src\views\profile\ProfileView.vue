<template>
  <div class="profile-container">
    <div class="profile-header">
      <h2>个人设置</h2>
      <p>管理您的个人信息和账户安全</p>
    </div>

    <div class="profile-content" v-loading="loading">
      <div class="profile-sidebar">
        <div class="avatar-section">
          <div class="avatar-wrapper">
            <el-avatar :size="120" :src="userInfo.avatar || defaultAvatar" />
            <div class="avatar-overlay">
              <el-upload
                class="avatar-uploader"
                action="/api/auth/upload-avatar"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
                name="file"
              >
                <el-icon><Camera /></el-icon>
              </el-upload>
            </div>
          </div>
          <h3>{{ userInfo.name }}</h3>
          <div class="role-badge">
            <el-tag :type="getRoleTagType(userInfo.role)" effect="dark" round>
              {{ getRoleName(userInfo.role) }}
            </el-tag>
          </div>
          <div class="user-meta">
            <div class="meta-item">
              <el-icon><User /></el-icon>
              <span>{{ userInfo.username }}</span>
            </div>
            <div class="meta-item" v-if="userInfo.phone">
              <el-icon><Phone /></el-icon>
              <span>{{ userInfo.phone }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="profile-main">
        <el-tabs type="border-card">
          <el-tab-pane label="基本信息">
            <el-form
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              label-width="100px"
              class="profile-form"
            >
              <el-form-item label="用户名">
                <el-input v-model="userInfo.username" disabled />
              </el-form-item>
              <el-form-item label="姓名">
                <el-input v-model="userInfo.name" disabled />
              </el-form-item>
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="profileForm.phone" placeholder="请输入手机号">
                  <template #prefix>
                    <el-icon><Phone /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item label="注册时间">
                <el-input v-model="userInfo.create_time" disabled>
                  <template #prefix>
                    <el-icon><Calendar /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveProfile" :loading="saving" class="submit-btn">
                  <el-icon><Check /></el-icon>
                  保存信息
                </el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <el-tab-pane label="账户安全">
            <div class="security-section">
              <div class="security-item">
                <div class="security-info">
                  <el-icon class="security-icon"><Lock /></el-icon>
                  <div>
                    <h4>账户密码</h4>
                    <p>定期更换密码可以保护您的账户安全</p>
                  </div>
                </div>
                <el-button @click="changePassword" type="primary" plain>修改密码</el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="passwordDialogVisible" title="修改密码" width="30%" destroy-on-close>
      <el-form
        :model="passwordForm"
        :rules="passwordRules"
        ref="passwordFormRef"
        label-width="100px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" type="password" show-password>
            <template #prefix>
              <el-icon><Key /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password>
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password>
            <template #prefix>
              <el-icon><Check /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitChangePassword" :loading="changing">
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import axios from 'axios'
import { User, Phone, Calendar, Camera, Check, Lock, Key } from '@element-plus/icons-vue'

// 默认头像
const defaultAvatar = '/assets/default-avatar.png'

// 上传头像的请求头
const uploadHeaders = {
  Authorization: `Bearer ${localStorage.getItem('token')}`,
}

// 用户信息
const userInfo = ref({
  id: '',
  username: '',
  name: '',
  role: '',
  phone: '',
  avatar: '',
  create_time: '',
  update_time: '',
})

// 表单数据
const profileForm = reactive({
  phone: '',
})

// 密码表单数据
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// 表单引用
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

// 状态控制
const loading = ref(false)
const saving = ref(false)
const changing = ref(false)
const passwordDialogVisible = ref(false)

// 表单验证规则
const profileRules = reactive({
  phone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }],
})

// 自定义验证规则：确认密码必须与新密码一致
const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 密码验证规则
const passwordRules = reactive({
  oldPassword: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' },
  ],
})

// 获取角色名称
const getRoleName = (role: string): string => {
  switch (role) {
    case 'admin':
      return '管理员'
    case 'business':
      return '商务'
    case 'operation':
      return '运营'
    default:
      return '未知'
  }
}

// 获取角色标签类型
const getRoleTagType = (role: string): string => {
  switch (role) {
    case 'admin':
      return 'danger'
    case 'business':
      return 'primary'
    case 'operation':
      return 'success'
    default:
      return 'info'
  }
}

// 获取用户信息
const getUserInfo = async () => {
  loading.value = true
  try {
    const response = await axios.get('/api/auth/profile')
    if (response.data.code === 0) {
      userInfo.value = response.data.data
      profileForm.phone = userInfo.value.phone || ''
    } else {
      ElMessage.error(response.data.message || '获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 保存个人信息
const saveProfile = async () => {
  if (!profileFormRef.value) return

  await profileFormRef.value.validate(async (valid) => {
    if (valid) {
      saving.value = true
      try {
        const response = await axios.put('/api/auth/profile', {
          phone: profileForm.phone,
        })

        if (response.data.code === 0) {
          ElMessage.success('保存成功')
          userInfo.value = response.data.data

          // 更新本地存储的用户信息
          const userStr = localStorage.getItem('user')
          if (userStr) {
            try {
              const user = JSON.parse(userStr)
              user.phone = profileForm.phone
              localStorage.setItem('user', JSON.stringify(user))
            } catch (error) {
              console.error('更新本地用户信息失败:', error)
            }
          }
        } else {
          ElMessage.error(response.data.message || '保存失败')
        }
      } catch (error) {
        console.error('保存个人信息失败:', error)
        ElMessage.error('保存个人信息失败，请检查网络连接')
      } finally {
        saving.value = false
      }
    }
  })
}

// 打开修改密码对话框
const changePassword = () => {
  passwordDialogVisible.value = true
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
}

// 提交修改密码
const submitChangePassword = async () => {
  if (!passwordFormRef.value) return

  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      changing.value = true
      try {
        const response = await axios.post('/api/auth/change-password', {
          old_password: passwordForm.oldPassword,
          new_password: passwordForm.newPassword,
        })

        if (response.data.code === 0) {
          ElMessage.success('密码修改成功，请重新登录')
          passwordDialogVisible.value = false

          // 退出登录
          setTimeout(() => {
            localStorage.removeItem('token')
            localStorage.removeItem('user')
            window.location.href = '/login'
          }, 1500)
        } else {
          ElMessage.error(response.data.message || '修改密码失败')
        }
      } catch (error: any) {
        if (error.response && error.response.data) {
          ElMessage.error(error.response.data.message || '修改密码失败')
        } else {
          ElMessage.error('修改密码失败，请检查网络连接')
        }
      } finally {
        changing.value = false
      }
    }
  })
}

// 头像上传前的处理
const beforeAvatarUpload = (file: File) => {
  const isJPG = file.type === 'image/jpeg'
  const isPNG = file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG && !isPNG) {
    ElMessage.error('头像只能是 JPG 或 PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('头像大小不能超过 2MB!')
    return false
  }
  return true
}

// 头像上传成功的处理
const handleAvatarSuccess = (response: any) => {
  if (response.code === 0) {
    userInfo.value.avatar = response.data.avatar_url
    ElMessage.success('头像上传成功')

    // 更新本地存储的用户信息，以便头像立即显示
    const userStr = localStorage.getItem('user')
    if (userStr) {
      try {
        const user = JSON.parse(userStr)
        user.avatar = response.data.avatar_url
        localStorage.setItem('user', JSON.stringify(user))
      } catch (error) {
        console.error('更新本地用户头像失败:', error)
      }
    }
  } else {
    ElMessage.error(response.message || '头像上传失败')
  }
}

onMounted(() => {
  getUserInfo()
})
</script>

<style scoped>
.profile-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

.profile-header {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
}

.profile-header h2 {
  font-size: 28px;
  margin-bottom: 8px;
  font-weight: 600;
}

.profile-header p {
  color: #909399;
  font-size: 14px;
}

.profile-content {
  display: flex;
  gap: 24px;
  max-width: 1100px;
  margin: 0 auto;
}

.profile-sidebar {
  width: 280px;
  flex-shrink: 0;
}

.profile-main {
  flex-grow: 1;
}

.avatar-section {
  background: linear-gradient(135deg, #ffffff 0%, #f0f2f5 100%);
  border-radius: 12px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.avatar-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.avatar-wrapper {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 20px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
  border-radius: 50%;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.avatar-overlay .el-icon {
  font-size: 24px;
  color: white;
}

.avatar-section h3 {
  font-size: 18px;
  margin: 10px 0;
  font-weight: 600;
  color: #303133;
}

.role-badge {
  margin: 10px 0 15px;
}

.user-meta {
  margin-top: 20px;
  text-align: left;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #606266;
  font-weight: 500;
}

.meta-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #409eff;
}

.profile-form {
  padding: 20px;
}

.security-section {
  padding: 20px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  margin-bottom: 16px;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.security-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.security-info {
  display: flex;
  align-items: center;
}

.security-icon {
  font-size: 24px;
  color: #409eff;
  margin-right: 16px;
  background-color: rgba(64, 158, 255, 0.1);
  padding: 10px;
  border-radius: 8px;
}

.security-info h4 {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.security-info p {
  margin: 0;
  color: #606266;
  font-size: 13px;
}

.submit-btn {
  width: 100%;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn .el-icon {
  margin-right: 6px;
}

.el-tabs__item {
  color: #303133;
  font-weight: 500;
}

.el-tabs__item.is-active {
  color: #409eff;
  font-weight: 600;
}

.el-form-item__label {
  color: #606266;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
  }

  .profile-sidebar {
    width: 100%;
  }
}
</style>
