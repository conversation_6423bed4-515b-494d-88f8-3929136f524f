#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手推广管理脚本
用于添加和移除商品推广
"""

import requests
import json
import time
import logging
from typing import List, Dict, Optional, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('promotion_manager.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class PromotionManager:
    """推广管理类"""
    
    def __init__(self, cookie_string: Optional[str] = None):
        # 基础配置
        self.base_url = "https://cps.kwaixiaodian.com"
        self.api_url = f"{self.base_url}/gateway/distribute/platform/investment/activity/adjustActivityPromoter"
        
        # 默认Cookie配置
        default_cookies = {
            "kuaishou.shop.b_st": "ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAE3tBozHFVZOn8pljtRICePPkO5r0KudHnwAKYs6cKFeR4iZ2kuwe3aY_uTEPJciQgEY7R3cPMo-Zs_Z83AQerRWidPl8vDub4WQdp_YD1EFwv1BmSppwUKKxrfyFD1MAMI0oFHlLPVdpSQK-m5yfSBSPE5AvYJkQ1QKZyRMXgmVs1RE4_wHZLmfACJibZ6mUJZC8jixMJ0KY1-1xjH83blGhISqzP1A3OvUjEirVmZwcinEd4iIJfDUuUOMJQnmTnc3NWgIAQd3LEVHp0oaIZSZ-gNVIyEKAUwAQ",
            "kuaishou.shop.b_ph": "70bed43b4a2ba56eb32fc3df6bcdff0a7ba5",
            "_did": "web_6109536701C516C8",
            "did": "web_m5p31mdccrdqpmzfrlb5k0pb18ya7tn1",
            "sid": "kuaishou.shop.b",
            "bUserId": "1000040627146",
            "userId": "2885180614"
        }
        
        # 如果提供了cookie字符串，则解析并更新
        if cookie_string:
            self.cookies = self._parse_cookie_string(cookie_string)
            # 合并默认cookie和提供的cookie
            self.cookies.update(default_cookies)
        else:
            self.cookies = default_cookies
        
        # 请求头
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Content-Type": "application/json",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Referer": "https://cps.kwaixiaodian.com/",
            "Origin": "https://cps.kwaixiaodian.com"
        }
        
        # 创建会话
        self.session = requests.Session()
        self.session.cookies.update(self.cookies)
        self.session.headers.update(self.headers)
    
    def _parse_cookie_string(self, cookie_string: str) -> dict:
        """
        解析cookie字符串为字典
        
        Args:
            cookie_string: cookie字符串，格式如 "name1=value1; name2=value2"
            
        Returns:
            dict: cookie字典
        """
        cookies = {}
        if not cookie_string:
            return cookies
        
        # 分割cookie字符串
        cookie_pairs = cookie_string.split(';')
        
        for pair in cookie_pairs:
            pair = pair.strip()
            if '=' in pair:
                name, value = pair.split('=', 1)
                cookies[name.strip()] = value.strip()
        
        return cookies
    
    def update_cookies(self, cookie_string: str):
        """
        更新cookie
        
        Args:
            cookie_string: 新的cookie字符串
        """
        new_cookies = self._parse_cookie_string(cookie_string)
        self.cookies.update(new_cookies)
        self.session.cookies.update(new_cookies)
        logging.info("Cookie已更新")
    
    def add_promotion(self, promoter_ids: Union[int, List[int]], activity_id: int) -> Dict:
        """
        添加推广
        
        Args:
            promoter_ids: 达人ID列表或单个达人ID
            activity_id: 活动ID
            
        Returns:
            Dict: 响应结果
        """
        return self._adjust_promotion(promoter_ids, activity_id, 1)
    
    def remove_promotion(self, promoter_ids: Union[int, List[int]], activity_id: int) -> Dict:
        """
        移除推广
        
        Args:
            promoter_ids: 达人ID列表或单个达人ID
            activity_id: 活动ID
            
        Returns:
            Dict: 响应结果
        """
        return self._adjust_promotion(promoter_ids, activity_id, 2)
    
    def _adjust_promotion(self, promoter_ids: Union[int, List[int]], activity_id: int, operator_type: int) -> Dict:
        """
        调整推广状态
        
        Args:
            promoter_ids: 达人ID列表或单个达人ID
            activity_id: 活动ID
            operator_type: 操作类型，1=添加，2=移除
            
        Returns:
            Dict: 响应结果
        """
        # 确保promoter_ids是列表
        if isinstance(promoter_ids, int):
            promoter_ids = [promoter_ids]
        
        # 构建请求数据
        payload = {
            "promoterId": promoter_ids,
            "activityId": activity_id,
            "operatorType": operator_type
        }
        
        operation_name = "添加" if operator_type == 1 else "移除"
        
        try:
            logging.info(f"开始{operation_name}推广: 达人IDs={promoter_ids}, 活动ID={activity_id}")
            
            # 发送请求
            response = self.session.post(
                self.api_url,
                json=payload,
                timeout=30
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            try:
                result = response.json()
                print(f"{operation_name}推广响应: {result}")
            except json.JSONDecodeError:
                # 处理Brotli压缩
                if 'br' in response.headers.get('Content-Encoding', ''):
                    try:
                        import brotli
                        decompressed_content = brotli.decompress(response.content)
                        result = json.loads(decompressed_content.decode('utf-8'))
                        logging.info(f"{operation_name}推广响应(Brotli解码): {result}")
                    except ImportError:
                        logging.error("缺少brotli库，无法解码Brotli压缩内容")
                        return {
                            "success": False,
                            "error": "缺少brotli库",
                            "message": f"{operation_name}推广失败：需要安装brotli库"
                        }
                    except Exception as e:
                        logging.error(f"Brotli解码失败: {e}")
                        return {
                            "success": False,
                            "error": str(e),
                            "message": f"{operation_name}推广失败：响应解码错误"
                        }
                else:
                    raise
            
            # 检查响应结果
            if result.get('result') == 1:
                return {
                    "success": True,
                    "data": result,
                    "message": f"{operation_name}推广成功"
                }
            # else:
            #     # 检查是否是活动状态不允许修改的错误
            #     error_msg = result.get('msg', '')
            #     if '当前活动状态不允许修改达人信息' in error_msg or result.get('result') == 80002:
            #         return {
            #             "success": False,
            #             "data": result,
            #             "message": "当前活动状态不允许修改达人信息",
            #             "activity_status_error": True  # 标记为活动状态错误
            #         }
            #     else:
            #         return {
            #             "success": False,
            #             "data": result,
            #             "message": error_msg or f'{operation_name}推广失败'
            #         }
            
        except requests.exceptions.RequestException as e:
            error_msg = f"{operation_name}推广请求失败: {str(e)}"
            logging.error(error_msg)
            return {
                "success": False,
                "error": str(e),
                "message": error_msg
            }
        except json.JSONDecodeError as e:
            error_msg = f"{operation_name}推广响应解析失败: {str(e)}"
            logging.error(error_msg)
            return {
                "success": False,
                "error": str(e),
                "message": error_msg
            }
        except Exception as e:
            error_msg = f"{operation_name}推广未知错误: {str(e)}"
            logging.error(error_msg)
            return {
                "success": False,
                "error": str(e),
                "message": error_msg
            }
    
    def batch_add_promotion(self, promotions: List[Dict]) -> List[Dict]:
        """
        批量添加推广
        
        Args:
            promotions: 推广列表，每个元素包含promoter_ids和activity_id
            
        Returns:
            List[Dict]: 批量操作结果
        """
        results = []
        
        for i, promotion in enumerate(promotions):
            promoter_ids = promotion.get('promoter_ids', [])
            activity_id = promotion.get('activity_id')
            
            if not activity_id:
                results.append({
                    "index": i,
                    "success": False,
                    "message": "缺少活动ID"
                })
                continue
            
            result = self.add_promotion(promoter_ids, activity_id)
            result["index"] = i
            results.append(result)
            
            # 添加延迟避免请求过快
            if i < len(promotions) - 1:
                time.sleep(1)
        
        return results
    
    def batch_remove_promotion(self, promotions: List[Dict]) -> List[Dict]:
        """
        批量移除推广
        
        Args:
            promotions: 推广列表，每个元素包含promoter_ids和activity_id
            
        Returns:
            List[Dict]: 批量操作结果
        """
        results = []
        
        for i, promotion in enumerate(promotions):
            promoter_ids = promotion.get('promoter_ids', [])
            activity_id = promotion.get('activity_id')
            
            if not activity_id:
                results.append({
                    "index": i,
                    "success": False,
                    "message": "缺少活动ID"
                })
                continue
            
            result = self.remove_promotion(promoter_ids, activity_id)
            result["index"] = i
            results.append(result)
            
            # 添加延迟避免请求过快
            if i < len(promotions) - 1:
                time.sleep(1)
        
        return results
    
    def test_connection(self) -> Dict:
        """
        测试连接
        
        Returns:
            Dict: 连接测试结果
        """
        try:
            # 发送一个简单的请求测试连接
            response = self.session.get(
                f"{self.base_url}/",
                timeout=10
            )
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "message": "连接正常"
                }
            else:
                return {
                    "success": False,
                    "message": f"连接异常，状态码: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"连接失败: {str(e)}"
            }


def main():
    """主函数，用于测试"""
    # 创建推广管理器
    manager = PromotionManager()
    
    # 测试连接
    print("测试连接...")
    test_result = manager.test_connection()
    print(f"连接测试结果: {test_result}")
    
    if not test_result["success"]:
        print("连接失败，请检查网络和Cookie配置")
        return
    
    # 测试添加推广
    print("\n测试添加推广...")
    add_result = manager.add_promotion(
        promoter_ids=[2284040888],  # 达人ID
        activity_id=9318379614      # 活动ID
    )
    print(f"添加推广结果: {add_result}")
    
    # 等待一段时间
    time.sleep(2)
    
    # 测试移除推广
    print("\n测试移除推广...")
    remove_result = manager.remove_promotion(
        promoter_ids=[2284040888],  # 达人ID
        activity_id=9318379614      # 活动ID
    )
    print(f"移除推广结果: {remove_result}")
    
    # 测试批量操作
    print("\n测试批量操作...")
    batch_promotions = [
        {
            "promoter_ids": [2284040888],
            "activity_id": 9318379614
        },
        {
            "promoter_ids": [2284040889],
            "activity_id": 9318379615
        }
    ]
    
    batch_add_results = manager.batch_add_promotion(batch_promotions)
    print(f"批量添加结果: {batch_add_results}")


if __name__ == "__main__":
    main() 