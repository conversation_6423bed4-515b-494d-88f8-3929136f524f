<template>
  <div class="product-link-container">
    <!-- 页面头部 -->
    <el-card class="header-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>商品链接管理</h3>
          <p class="subtitle">快速创建快手活动链接，支持一级团和二级团模式</p>
        </div>
      </template>

      <!-- 功能介绍卡片 -->
      <el-row :gutter="20" class="feature-cards">
        <el-col :span="12">
          <el-card class="feature-card secondary-card" shadow="hover">
            <div class="feature-content">
              <div class="feature-icon">
                <el-icon size="32"><Share /></el-icon>
              </div>
              <div class="feature-info">
                <h4>二级团链接</h4>
                <p>通过活动链接自动获取达人信息，智能筛选专享/专属达人，快速创建二级团活动</p>
                <ul class="feature-list">
                  <li>自动解析活动URL</li>
                  <li>智能达人筛选</li>
                  <li>专享/专属提醒</li>
                  <li>批量达人管理</li>
                </ul>
              </div>
            </div>
            <div class="feature-action">
              <el-button type="primary" size="large" @click="openSecondaryGroupDialog">
                <el-icon><Share /></el-icon>
                创建二级团链接
              </el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="feature-card primary-card" shadow="hover">
            <div class="feature-content">
              <div class="feature-icon">
                <el-icon size="32"><Star /></el-icon>
              </div>
              <div class="feature-info">
                <h4>一级团链接</h4>
                <p>手动输入达人ID列表，灵活配置活动参数，适合精准投放和定向邀请</p>
                <ul class="feature-list">
                  <li>手动输入达人ID</li>
                  <li>灵活时间配置</li>
                  <li>自定义活动标题</li>
                  <li>多格式ID支持</li>
                </ul>
              </div>
            </div>
            <div class="feature-action">
              <el-button type="success" size="large" @click="openPrimaryGroupDialog">
                <el-icon><Star /></el-icon>
                创建一级团链接
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 快捷操作 -->
      <el-row :gutter="20" class="quick-actions">
        <el-col :span="24">
          <el-card class="quick-actions-card" shadow="never">
            <template #header>
              <div class="quick-actions-header">
                <el-icon><Tools /></el-icon>
                <span>快捷操作</span>
              </div>
            </template>
            <div class="quick-buttons">
              <el-button-group>
                <el-button @click="showHelpDialog">
                  <el-icon><QuestionFilled /></el-icon>
                  使用帮助
                </el-button>
                <el-button @click="showTemplateDialog">
                  <el-icon><Document /></el-icon>
                  模板示例
                </el-button>
              </el-button-group>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 二级团链接对话框 -->
    <el-dialog
      v-model="secondaryGroupDialog.visible"
      title="二级团链接创建"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="secondaryGroupDialog.form" label-width="100px">
        <el-form-item label="活动链接">
          <el-input
            v-model="secondaryGroupDialog.form.activityUrl"
            placeholder="请输入快手活动链接，例如：https://cps.kwaixiaodian.com/pc/leader/base/activity-detail?activityId=9173858085&source=copy"
            type="textarea"
            :rows="3"
          />
          <div class="input-tip">支持快手CPS活动链接，系统将自动提取活动ID并获取达人信息</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="secondaryGroupDialog.visible = false">取消</el-button>
          <el-button
            type="primary"
            @click="parseActivityUrl"
            :loading="secondaryGroupDialog.loading"
          >
            解析链接
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 达人选择对话框 -->
    <el-dialog
      v-model="talentSelectionDialog.visible"
      title="选择达人"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="talentSelectionDialog.talents.length > 0">
        <div class="selection-header">
          <el-checkbox
            v-model="talentSelectionDialog.selectAll"
            @change="handleSelectAll"
            :indeterminate="talentSelectionDialog.indeterminate"
          >
            全选 ({{ talentSelectionDialog.selectedTalents.length }}/{{
              talentSelectionDialog.talents.length
            }})
          </el-checkbox>
          <el-tag type="info" size="small">
            已选择 {{ talentSelectionDialog.selectedTalents.length }} 个达人
          </el-tag>
        </div>

        <el-table
          :data="talentSelectionDialog.talents"
          @selection-change="handleTalentSelectionChange"
          style="width: 100%; margin-top: 15px"
          max-height="400px"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="talent_nickname" label="达人昵称" width="150" />
          <el-table-column prop="talent_id" label="达人ID" width="120" />
          <el-table-column label="状态" width="200">
            <template #default="scope">
              <div v-if="scope.row.is_exclusive_or_special">
                <el-tag type="warning" size="small">
                  已是商务{{ scope.row.business_contact }}的{{
                    scope.row.talent_category === 'special' ? '专享' : '专属'
                  }}
                </el-tag>
              </div>
              <el-tag v-else type="success" size="small">可用</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="talentSelectionDialog.visible = false">取消</el-button>
          <el-button @click="goToTimeSelection" type="primary">
            下一步 ({{ talentSelectionDialog.selectedTalents.length }}个达人)
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 时间选择对话框 -->
    <el-dialog
      v-model="timeSelectionDialog.visible"
      title="活动设置"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="timeSelectionDialog.form" label-width="120px">
        <el-form-item label="活动时间">
          <el-date-picker
            v-model="timeSelectionDialog.form.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="活动标题">
          <el-input v-model="timeSelectionDialog.form.activityTitle" placeholder="请输入活动标题" />
        </el-form-item>
        <el-form-item label="选中达人">
          <el-tag
            v-for="talent in talentSelectionDialog.selectedTalents"
            :key="talent.talent_id"
            style="margin-right: 8px; margin-bottom: 8px"
            size="small"
          >
            {{ talent.talent_nickname }}({{ talent.talent_id }})
          </el-tag>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="backToTalentSelection">上一步</el-button>
          <el-button
            type="primary"
            @click="createSecondaryGroup"
            :loading="timeSelectionDialog.loading"
          >
            创建二级团
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 一级团设置对话框 -->
    <el-dialog
      v-model="primaryGroupDialog.visible"
      title="一级团设置"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="primaryGroupDialog.form" label-width="120px">
        <el-form-item label="活动时间">
          <el-date-picker
            v-model="primaryGroupDialog.form.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="活动标题">
          <el-input v-model="primaryGroupDialog.form.activityTitle" placeholder="请输入活动标题" />
        </el-form-item>
        <el-form-item label="达人ID列表">
          <el-input
            v-model="primaryGroupDialog.form.talentIdsInput"
            type="textarea"
            :rows="6"
            placeholder="请输入达人ID，支持逗号、空格、换行分隔"
          />
          <div class="input-tip">支持多种分隔方式：逗号、空格、换行等</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="primaryGroupDialog.visible = false">取消</el-button>
          <el-button
            type="primary"
            @click="createPrimaryGroup"
            :loading="primaryGroupDialog.loading"
          >
            创建一级团
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 结果对话框 -->
    <el-dialog
      v-model="resultDialog.visible"
      title="创建结果"
      width="700px"
      :close-on-click-modal="false"
    >
      <div v-if="resultDialog.data" class="result-content">
        <el-alert
          :title="
            resultDialog.data.group_type === 'primary' ? '一级团创建成功！' : '二级团创建成功！'
          "
          type="success"
          :closable="false"
          style="margin-bottom: 20px"
        />

        <div class="result-info">
          <p><strong>活动ID：</strong>{{ resultDialog.data.activity_id }}</p>
          <p><strong>活动标题：</strong>{{ resultDialog.data.activity_title }}</p>
          <p><strong>达人数量：</strong>{{ resultDialog.data.talent_count }}个</p>
          <p>
            <strong>活动时间：</strong>{{ resultDialog.data.start_date }} 至
            {{ resultDialog.data.end_date }}
          </p>
        </div>

        <div class="result-links">
          <div class="link-item">
            <label>团长报名链接：</label>
            <div class="link-content">
              <el-input
                :value="resultDialog.data.leader_link"
                readonly
                style="flex: 1; margin-right: 10px"
              />
              <el-button type="primary" @click="copyToClipboard(resultDialog.data.leader_link)">
                复制
              </el-button>
            </div>
          </div>

          <div class="link-item">
            <label>商家报名链接：</label>
            <div class="link-content">
              <el-input
                :value="resultDialog.data.merchant_link"
                readonly
                style="flex: 1; margin-right: 10px"
              />
              <el-button type="primary" @click="copyToClipboard(resultDialog.data.merchant_link)">
                复制
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="resultDialog.visible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 帮助对话框 -->
    <el-dialog v-model="helpDialog.visible" title="使用帮助" width="600px">
      <div class="help-content">
        <h4>二级团链接使用方法：</h4>
        <ol>
          <li>复制快手CPS活动链接</li>
          <li>粘贴到输入框中，点击"解析链接"</li>
          <li>系统自动获取达人信息并标记专享/专属状态</li>
          <li>选择需要的达人（默认全选）</li>
          <li>设置活动时间和标题</li>
          <li>创建成功后获得团长和商家报名链接</li>
        </ol>

        <h4>一级团链接使用方法：</h4>
        <ol>
          <li>手动输入达人ID列表</li>
          <li>设置活动时间范围</li>
          <li>输入活动标题</li>
          <li>创建成功后获得团长和商家报名链接</li>
        </ol>
      </div>
    </el-dialog>

    <!-- 模板对话框 -->
    <el-dialog v-model="templateDialog.visible" title="模板示例" width="600px">
      <div class="template-content">
        <h4>活动链接示例：</h4>
        <el-input
          value="https://cps.kwaixiaodian.com/pc/leader/base/activity-detail?activityId=9173858085&source=copy"
          readonly
          style="margin-bottom: 15px"
        />

        <h4>达人ID列表示例：</h4>
        <el-input
          type="textarea"
          :rows="4"
          value="123456789&#10;987654321&#10;456789123"
          readonly
          style="margin-bottom: 15px"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Share, Star, Tools, QuestionFilled, Document, Refresh } from '@element-plus/icons-vue'
import axios from 'axios'

// 对话框状态
const helpDialog = reactive({ visible: false })
const templateDialog = reactive({ visible: false })

// 二级团对话框
const secondaryGroupDialog = reactive({
  visible: false,
  loading: false,
  form: {
    activityUrl: '',
  },
})

// 达人选择对话框
const talentSelectionDialog = reactive({
  visible: false,
  talents: [],
  selectedTalents: [],
  selectAll: false,
  indeterminate: false,
})

// 时间选择对话框
const timeSelectionDialog = reactive({
  visible: false,
  loading: false,
  form: {
    dateRange: [],
    activityTitle: '',
  },
})

// 一级团对话框
const primaryGroupDialog = reactive({
  visible: false,
  loading: false,
  form: {
    dateRange: [],
    activityTitle: '',
    talentIdsInput: '',
  },
})

// 结果对话框
const resultDialog = reactive({
  visible: false,
  data: null,
})

// 方法
const showHelpDialog = () => {
  helpDialog.visible = true
}

const showTemplateDialog = () => {
  templateDialog.visible = true
}

const refreshStats = () => {
  ElMessage.success('页面已刷新')
}

// 打开二级团对话框
const openSecondaryGroupDialog = () => {
  secondaryGroupDialog.form.activityUrl = ''
  secondaryGroupDialog.visible = true
}

// 打开一级团对话框
const openPrimaryGroupDialog = () => {
  // 设置默认时间和标题
  const today = new Date()
  const nextMonth = new Date(today)
  nextMonth.setMonth(nextMonth.getMonth() + 1)

  const formatDate = (date) => {
    return date.toISOString().split('T')[0]
  }

  const randomNum = Math.floor(Math.random() * 90) + 10
  const todayStr = formatDate(today).replace(/-/g, '.')

  primaryGroupDialog.form.dateRange = [formatDate(today), formatDate(nextMonth)]
  primaryGroupDialog.form.activityTitle = `一级团-${todayStr}-${randomNum}`
  primaryGroupDialog.form.talentIdsInput = ''
  primaryGroupDialog.visible = true
}

// 解析活动链接
const parseActivityUrl = async () => {
  if (!secondaryGroupDialog.form.activityUrl.trim()) {
    ElMessage.warning('请输入活动链接')
    return
  }

  secondaryGroupDialog.loading = true
  try {
    const response = await axios.post('/api/product/link/parse-activity-url', {
      activity_url: secondaryGroupDialog.form.activityUrl,
    })

    if (response.data.code === 0) {
      talentSelectionDialog.talents = response.data.data.talents || []
      talentSelectionDialog.selectedTalents = [...talentSelectionDialog.talents]
      talentSelectionDialog.selectAll = true
      talentSelectionDialog.indeterminate = false

      secondaryGroupDialog.visible = false
      talentSelectionDialog.visible = true

      ElMessage.success(`成功获取${talentSelectionDialog.talents.length}个达人信息`)
    } else {
      ElMessage.error(response.data.message || '解析失败')
    }
  } catch (error) {
    console.error('解析活动链接失败:', error)
    ElMessage.error('解析活动链接失败')
  } finally {
    secondaryGroupDialog.loading = false
  }
}

// 处理达人选择
const handleTalentSelectionChange = (selection) => {
  talentSelectionDialog.selectedTalents = selection
  const selectedCount = selection.length
  const totalCount = talentSelectionDialog.talents.length

  talentSelectionDialog.selectAll = selectedCount === totalCount
  talentSelectionDialog.indeterminate = selectedCount > 0 && selectedCount < totalCount
}

// 处理全选
const handleSelectAll = (checked) => {
  if (checked) {
    talentSelectionDialog.selectedTalents = [...talentSelectionDialog.talents]
  } else {
    talentSelectionDialog.selectedTalents = []
  }
  talentSelectionDialog.indeterminate = false
}

// 进入时间选择
const goToTimeSelection = () => {
  if (talentSelectionDialog.selectedTalents.length === 0) {
    ElMessage.warning('请至少选择一个达人')
    return
  }

  // 设置默认时间和标题
  const today = new Date()
  const nextMonth = new Date(today)
  nextMonth.setMonth(nextMonth.getMonth() + 1)

  const formatDate = (date) => {
    return date.toISOString().split('T')[0]
  }

  const randomNum = Math.floor(Math.random() * 90) + 10
  const todayStr = formatDate(today).replace(/-/g, '.')

  timeSelectionDialog.form.dateRange = [formatDate(today), formatDate(nextMonth)]
  timeSelectionDialog.form.activityTitle = `二级团-${todayStr}-${randomNum}`

  talentSelectionDialog.visible = false
  timeSelectionDialog.visible = true
}

// 返回达人选择
const backToTalentSelection = () => {
  timeSelectionDialog.visible = false
  talentSelectionDialog.visible = true
}

// 创建二级团
const createSecondaryGroup = async () => {
  if (!timeSelectionDialog.form.dateRange || timeSelectionDialog.form.dateRange.length !== 2) {
    ElMessage.warning('请选择活动时间范围')
    return
  }

  if (!timeSelectionDialog.form.activityTitle.trim()) {
    ElMessage.warning('请输入活动标题')
    return
  }

  timeSelectionDialog.loading = true
  try {
    const response = await axios.post('/api/product/link/create-secondary-group', {
      talent_ids: talentSelectionDialog.selectedTalents.map((t) => t.talent_id),
      start_date: timeSelectionDialog.form.dateRange[0],
      end_date: timeSelectionDialog.form.dateRange[1],
      activity_title: timeSelectionDialog.form.activityTitle,
    })

    if (response.data.code === 0) {
      resultDialog.data = { ...response.data.data, group_type: 'secondary' }
      timeSelectionDialog.visible = false
      resultDialog.visible = true
      ElMessage.success('二级团创建成功！')
    } else {
      ElMessage.error(response.data.message || '创建失败')
    }
  } catch (error) {
    console.error('创建二级团失败:', error)
    ElMessage.error('创建二级团失败')
  } finally {
    timeSelectionDialog.loading = false
  }
}

// 创建一级团
const createPrimaryGroup = async () => {
  if (!primaryGroupDialog.form.dateRange || primaryGroupDialog.form.dateRange.length !== 2) {
    ElMessage.warning('请选择活动时间范围')
    return
  }

  if (!primaryGroupDialog.form.activityTitle.trim()) {
    ElMessage.warning('请输入活动标题')
    return
  }

  if (!primaryGroupDialog.form.talentIdsInput.trim()) {
    ElMessage.warning('请输入达人ID列表')
    return
  }

  primaryGroupDialog.loading = true
  try {
    const response = await axios.post('/api/product/link/create-primary-group', {
      talent_ids_input: primaryGroupDialog.form.talentIdsInput,
      start_date: primaryGroupDialog.form.dateRange[0],
      end_date: primaryGroupDialog.form.dateRange[1],
      activity_title: primaryGroupDialog.form.activityTitle,
    })

    if (response.data.code === 0) {
      resultDialog.data = { ...response.data.data, group_type: 'primary' }
      primaryGroupDialog.visible = false
      resultDialog.visible = true
      ElMessage.success('一级团创建成功！')
    } else {
      ElMessage.error(response.data.message || '创建失败')
    }
  } catch (error) {
    console.error('创建一级团失败:', error)
    ElMessage.error('创建一级团失败')
  } finally {
    primaryGroupDialog.loading = false
  }
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}
</script>

<style scoped>
.product-link-container {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.card-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.feature-cards {
  margin-bottom: 30px;
}

.feature-card {
  height: 280px;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.secondary-card {
  border-left: 4px solid #409eff;
}

.primary-card {
  border-left: 4px solid #67c23a;
}

.feature-content {
  display: flex;
  flex-direction: column;
  height: 200px;
}

.feature-icon {
  text-align: center;
  margin-bottom: 15px;
  color: #409eff;
}

.primary-card .feature-icon {
  color: #67c23a;
}

.feature-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
}

.feature-info p {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
}

.feature-list {
  margin: 0;
  padding-left: 20px;
  color: #909399;
  font-size: 13px;
}

.feature-list li {
  margin-bottom: 5px;
}

.feature-action {
  text-align: center;
  margin-top: auto;
}

.quick-actions-card {
  background: #f8f9fa;
}

.quick-actions-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-weight: 600;
}

.quick-buttons {
  text-align: center;
}

.input-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.result-content {
  padding: 10px 0;
}

.result-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
}

.result-info p {
  margin: 8px 0;
  color: #606266;
}

.result-links {
  margin-top: 20px;
}

.link-item {
  margin-bottom: 15px;
}

.link-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #303133;
}

.link-content {
  display: flex;
  align-items: center;
}

.help-content h4 {
  color: #303133;
  margin: 20px 0 10px 0;
}

.help-content ol,
.help-content ul {
  padding-left: 20px;
  color: #606266;
}

.help-content li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.template-content h4 {
  color: #303133;
  margin: 20px 0 10px 0;
}

.template-content ul {
  padding-left: 20px;
  color: #606266;
}

.template-content li {
  margin-bottom: 5px;
}
</style>
