-- 更新助播申请表字段名
-- 将旧字段名改为新字段名，以符合用户需求

-- 1. 将 target_gmv_rate 改名为 boost_rate (助播费率)
ALTER TABLE boost_application CHANGE COLUMN target_gmv_rate boost_rate DECIMAL(5,2) DEFAULT NULL COMMENT '助播费率(%)';

-- 2. 将 fixed_amount 改名为 boost_fee (助播费)
ALTER TABLE boost_application CHANGE COLUMN fixed_amount boost_fee DECIMAL(10,2) DEFAULT NULL COMMENT '助播费(元)';

-- 3. 将 operation_notes 改名为 review_notes (复盘)
ALTER TABLE boost_application CHANGE COLUMN operation_notes review_notes TEXT DEFAULT NULL COMMENT '复盘内容';

-- 4. 删除 settlement_status 字段 (不再需要结算状态)
ALTER TABLE boost_application DROP COLUMN settlement_status;

-- 5. 修改 status 字段类型为 VARCHAR，并更新默认值
ALTER TABLE boost_application CHANGE COLUMN status status VARCHAR(20) DEFAULT '未上播' COMMENT '状态：未上播、已上播、已废弃';

-- 6. 更新现有数据的状态值
UPDATE boost_application SET status = '未上播' WHERE status = '0';
UPDATE boost_application SET status = '已上播' WHERE status = '1';
UPDATE boost_application SET status = '已废弃' WHERE status = '2';
UPDATE boost_application SET status = '已废弃' WHERE status = '3';
