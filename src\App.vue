<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import MainLayout from '@/components/layout/MainLayout.vue'

const route = useRoute()

// 判断当前路由是否需要布局
const needLayout = computed(() => {
  // 登录页面不需要布局
  if (route.path === '/login') {
    return false
  }

  // 管理员后台不使用主布局
  if (route.path.startsWith('/admin')) {
    return false
  }

  return true
})
</script>

<template>
  <!-- 根据路由决定是否使用布局 -->
  <MainLayout v-if="needLayout" />
  <router-view v-else />
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family:
    'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑',
    Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background-color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  width: 100vw;
  min-width: 100%;
  max-width: 100vw;
}

#app {
  height: 100%;
  width: 100vw;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
  min-width: 100%;
  max-width: 100vw;
}

/* 全局样式覆盖 */
.el-table__inner-wrapper {
  width: 100% !important;
  max-width: 100% !important;
}

.el-table {
  width: 100% !important;
  max-width: 100% !important;
}

.el-card__body {
  width: 100% !important;
  max-width: 100% !important;
  padding: 20px !important;
  box-sizing: border-box !important;
}

.el-row {
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100% !important;
}
</style>
