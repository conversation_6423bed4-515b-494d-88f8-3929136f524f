from flask import Blueprint, request, jsonify
from app.utils.config_utils import get_kuaishou_cookie
import sys
import os
import logging

# 导入爬虫模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../python')))
try:
    from activity_list import fetch_activity_list
    logging.info("成功导入快手活动爬虫模块")
except ImportError as e:
    logging.error(f"导入快手活动爬虫模块失败: {str(e)}")
    fetch_activity_list = None

activity_bp = Blueprint('activity', __name__, url_prefix='/api/activity')

@activity_bp.route('/list', methods=['GET'])
def get_activity_list():
    """获取活动列表，支持分页和筛选"""
    try:
        # 获取查询参数
        page_type1 = request.args.get('page_type1', 1, type=int)
        page_size_type1 = request.args.get('page_size_type1', 10, type=int)
        page_type2 = request.args.get('page_type2', 1, type=int)
        page_size_type2 = request.args.get('page_size_type2', 10, type=int)
        activity_status = request.args.get('activity_status', 3, type=int)
        type_filter = request.args.get('type', 'both')
        
        # 从数据库获取cookie配置
        cookie = get_kuaishou_cookie()
        
        if fetch_activity_list is None:
            return jsonify({
                'code': 500,
                'message': '活动爬虫模块未导入，无法获取活动列表',
                'data': None
            }), 500
        
        result_type1 = {}
        result_type2 = {}
        
        if type_filter == 'both' or type_filter == 'type1':
            # 调用爬虫获取活动列表 - 普通招商
            result_type1 = fetch_activity_list(
                cookie=cookie,
                activity_type=1,
                activity_status=activity_status,
                page=page_type1,
                page_size=page_size_type1
            )
            if "error" in result_type1:
                return jsonify({
                    'code': 500,
                    'message': f'获取普通招商活动列表失败: {result_type1.get("error", "")}',
                    'data': None
                }), 500
        
        if type_filter == 'both' or type_filter == 'type2':
            # 调用爬虫获取活动列表 - 专属招商
            result_type2 = fetch_activity_list(
                cookie=cookie,
                activity_type=2,
                activity_status=activity_status,
                page=page_type2,
                page_size=page_size_type2
            )
            if "error" in result_type2:
                return jsonify({
                    'code': 500,
                    'message': f'获取专属招商活动列表失败: {result_type2.get("error", "")}',
                    'data': None
                }), 500
        
        return jsonify({
            'code': 0,
            'message': 'success',
            'data': {
                'type1': result_type1,
                'type2': result_type2
            }
        })
    except Exception as e:
        logging.error(f"获取活动列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取活动列表失败: {str(e)}',
            'data': None
        }), 500 