<template>
  <div class="sample-management-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>寄样管理</h3>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="商品名称">
            <el-input v-model="searchForm.productName" placeholder="请输入商品名称" clearable />
          </el-form-item>
          <el-form-item label="商品ID">
            <el-input v-model="searchForm.productId" placeholder="请输入商品ID" clearable />
          </el-form-item>
          <el-form-item label="物流单号">
            <el-input v-model="searchForm.trackingNumber" placeholder="请输入物流单号" clearable />
          </el-form-item>
          <el-form-item label="对接商务">
            <el-input v-model="searchForm.businessName" placeholder="请输入商务姓名" clearable />
          </el-form-item>
          <el-form-item label="对接运营">
            <el-input v-model="searchForm.operationName" placeholder="请输入运营姓名" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="申请中" :value="0" />
              <el-option label="已发货" :value="1" />
              <el-option label="已收货" :value="2" />
              <el-option label="已拒绝" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <div class="table-container" v-loading="loading">
        <el-table :data="sampleList" border style="width: 100%">
          <el-table-column type="expand">
            <template #default="props">
              <div class="sample-detail">
                <div class="detail-item">
                  <span class="detail-label">收货人微信:</span>
                  <span>{{ props.row.receiver_wechat || '未提供' }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">收货地址:</span>
                  <span>{{ props.row.receiver_address }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">备注:</span>
                  <span>{{ props.row.remark || '无' }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="申请时间" prop="create_time" width="160" />
          <el-table-column label="商品信息" min-width="250">
            <template #default="scope">
              <div class="product-info-cell">
                <el-image
                  :src="scope.row.product_image_url"
                  fit="cover"
                  style="width: 50px; height: 50px"
                >
                  <template #error>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div class="product-info-content">
                  <div class="product-name">{{ scope.row.product_name }}</div>
                  <div class="product-id">ID: {{ scope.row.product_id }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="收货信息" min-width="200">
            <template #default="scope">
              <div>
                <div>{{ scope.row.receiver_name }}</div>
                <div>{{ scope.row.receiver_phone }}</div>
                <div>数量: {{ scope.row.sample_count }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="对接商务" prop="business_name" width="120" />
          <el-table-column label="处理运营" prop="operation_name" width="120">
            <template #default="scope">
              {{ scope.row.operation_name || '未分配' }}
            </template>
          </el-table-column>
          <el-table-column label="物流单号" prop="tracking_number" width="150">
            <template #default="scope">
              {{ scope.row.tracking_number || '未发货' }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getSampleStatusType(scope.row.status)">
                {{ getSampleStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <!-- 商务用户和管理员可以确认收货 -->
              <el-button
                v-if="(isBusinessRole || isAdminRole) && scope.row.status === 1"
                type="success"
                size="small"
                @click="confirmReceived(scope.row)"
                >确认收货</el-button
              >

              <!-- 运营用户和管理员可以处理申请 -->
              <template v-if="isOperationRole || isAdminRole">
                <!-- 编辑按钮 - 所有状态都可以编辑 -->
                <el-button type="info" size="small" @click="editSample(scope.row)">编辑</el-button>

                <!-- 发货按钮 - 只有申请中状态可以发货 -->
                <el-button
                  v-if="scope.row.status === 0"
                  type="primary"
                  size="small"
                  @click="processSample(scope.row, 'approve')"
                  >发货</el-button
                >

                <!-- 拒绝按钮 - 只有申请中状态可以拒绝 -->
                <el-button
                  v-if="scope.row.status === 0"
                  type="danger"
                  size="small"
                  @click="processSample(scope.row, 'reject')"
                  >拒绝</el-button
                >
              </template>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 发货对话框 -->
    <el-dialog v-model="shipDialogVisible" title="填写物流信息" width="400px">
      <el-form ref="shipFormRef" :model="shipForm" :rules="shipRules" label-width="100px">
        <el-form-item label="物流单号" prop="trackingNumber">
          <el-input v-model="shipForm.trackingNumber" placeholder="请输入物流单号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shipDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitShipForm" :loading="submittingShip"
            >确认发货</el-button
          >
        </span>
      </template>
    </el-dialog>

    <!-- 编辑样品申请对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑样品申请" width="600px">
      <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="120px">
        <el-form-item label="收件人姓名" prop="receiverName">
          <el-input v-model="editForm.receiverName" placeholder="请输入收件人姓名" />
        </el-form-item>
        <el-form-item label="收件人电话" prop="receiverPhone">
          <el-input v-model="editForm.receiverPhone" placeholder="请输入收件人电话" />
        </el-form-item>
        <el-form-item label="收件人微信">
          <el-input v-model="editForm.receiverWechat" placeholder="请输入收件人微信（可选）" />
        </el-form-item>
        <el-form-item label="收件地址" prop="receiverAddress">
          <el-input
            v-model="editForm.receiverAddress"
            type="textarea"
            :rows="3"
            placeholder="请输入详细收件地址"
          />
        </el-form-item>
        <el-form-item label="样品数量" prop="sampleCount">
          <el-input-number
            v-model="editForm.sampleCount"
            :min="1"
            :max="10"
            placeholder="请输入样品数量"
          />
        </el-form-item>
        <el-form-item label="物流单号">
          <el-input v-model="editForm.trackingNumber" placeholder="请输入物流单号（可选）" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="editForm.status" placeholder="请选择状态">
            <el-option label="申请中" :value="0" />
            <el-option label="已发货" :value="1" />
            <el-option label="已收货" :value="2" />
            <el-option label="已拒绝" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="editForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEditForm" :loading="submittingEdit"
            >保存修改</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import axios from 'axios'
import { useUserStore } from '@/stores/user'

// 添加用户类型定义
interface User {
  id: number | string
  name: string
  username?: string
  role?: string
}

// 修改用户列表类型
const businessUsers = ref<User[]>([])
const operationUsers = ref<User[]>([])

// 修复 userStore.role 问题，从 localStorage 获取用户信息
const getUserRole = () => {
  const userStr = localStorage.getItem('user')
  if (userStr) {
    try {
      const user = JSON.parse(userStr)
      return user.role || ''
    } catch (error) {
      console.error('解析用户信息失败:', error)
      return ''
    }
  }
  return ''
}

// 修改角色判断
const isOperationRole = computed(() => getUserRole() === 'operation')
const isBusinessRole = computed(() => getUserRole() === 'business')
const isAdminRole = computed(() => getUserRole() === 'admin')

// 添加样品类型定义
interface Sample {
  id: number
  product_id: string
  business_user_id: number
  business_name: string
  receiver_name: string
  receiver_phone: string
  receiver_wechat?: string
  receiver_address: string
  sample_count: number
  remark?: string
  status: number
  operation_user_id?: number
  operation_name?: string
  tracking_number?: string
  create_time: string
  update_time: string
  product_name?: string
  product_image_url?: string
}

// 修改样品列表类型
const sampleList = ref<Sample[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)
const currentSample = ref<Sample | null>(null)

// 搜索表单
interface SearchForm {
  productName: string
  productId: string
  trackingNumber: string
  businessName: string
  operationName: string
  status: string | number
  dateRange: string[]
  [key: string]: string | number | string[] // 添加索引签名
}

const searchForm = reactive<SearchForm>({
  productName: '',
  productId: '',
  trackingNumber: '',
  businessName: '',
  operationName: '',
  status: '',
  dateRange: [],
})

// 获取样品列表
const fetchSamples = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      product_name: searchForm.productName,
      product_id: searchForm.productId,
      tracking_number: searchForm.trackingNumber,
      business_name: searchForm.businessName,
      operation_name: searchForm.operationName,
      status: searchForm.status,
      start_date: searchForm.dateRange?.[0] || '',
      end_date: searchForm.dateRange?.[1] || '',
    }

    const response = await axios.get('/api/product/samples', { params })
    if (response.data.code === 0) {
      sampleList.value = response.data.data.list || []
      total.value = response.data.data.total || 0
    } else {
      ElMessage.error(response.data.message || '获取样品列表失败')
      sampleList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取样品列表失败:', error)
    ElMessage.error('获取样品列表失败，请检查网络连接')
    sampleList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 获取商务和运营用户列表
const fetchUsers = async () => {
  try {
    // 获取商务用户
    try {
      const businessResponse = await axios.get('/api/auth/business-users', {
        params: { role: 'business' },
      })
      if (businessResponse.data.code === 0) {
        businessUsers.value = businessResponse.data.data || []
      } else {
        businessUsers.value = []
      }
    } catch (error) {
      console.error('获取商务用户列表失败:', error)
      businessUsers.value = []
    }

    // 获取运营用户
    try {
      const operationResponse = await axios.get('/api/auth/business-users', {
        params: { role: 'operation' },
      })
      if (operationResponse.data.code === 0) {
        operationUsers.value = operationResponse.data.data || []
      } else {
        operationUsers.value = []
      }
    } catch (error) {
      console.error('获取运营用户列表失败:', error)
      operationUsers.value = []
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    businessUsers.value = []
    operationUsers.value = []
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchSamples()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach((key) => {
    if (key === 'dateRange') {
      searchForm[key] = []
    } else {
      searchForm[key] = ''
    }
  })
  currentPage.value = 1
  fetchSamples()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchSamples()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchSamples()
}

// 获取样品状态文本
const getSampleStatusText = (status: number) => {
  switch (status) {
    case 0:
      return '申请中'
    case 1:
      return '已发货'
    case 2:
      return '已收货'
    case 3:
      return '已拒绝'
    default:
      return '未知状态'
  }
}

// 获取样品状态样式
const getSampleStatusType = (status: number) => {
  switch (status) {
    case 0:
      return 'info'
    case 1:
      return 'warning'
    case 2:
      return 'success'
    case 3:
      return 'danger'
    default:
      return 'info'
  }
}

// 发货对话框
const shipDialogVisible = ref(false)
const submittingShip = ref(false)
const shipFormRef = ref<FormInstance | null>(null)

const shipForm = reactive({
  trackingNumber: '',
})

const shipRules = {
  trackingNumber: [
    { required: true, message: '请输入物流单号', trigger: 'blur' },
    { min: 5, max: 50, message: '物流单号长度在 5 到 50 个字符', trigger: 'blur' },
  ],
}

// 编辑对话框
const editDialogVisible = ref(false)
const submittingEdit = ref(false)
const editFormRef = ref<FormInstance | null>(null)

const editForm = reactive({
  id: 0,
  receiverName: '',
  receiverPhone: '',
  receiverWechat: '',
  receiverAddress: '',
  sampleCount: 1,
  trackingNumber: '',
  status: 0,
  remark: '',
})

const editRules = {
  receiverName: [
    { required: true, message: '请输入收件人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  receiverPhone: [
    { required: true, message: '请输入收件人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  receiverAddress: [
    { required: true, message: '请输入收件地址', trigger: 'blur' },
    { min: 10, max: 200, message: '地址长度在 10 到 200 个字符', trigger: 'blur' },
  ],
  sampleCount: [
    { required: true, message: '请输入样品数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 10, message: '样品数量在 1 到 10 之间', trigger: 'blur' },
  ],
}

// 处理样品申请
const processSample = (sample: Sample, action: string) => {
  currentSample.value = sample

  if (action === 'approve') {
    shipDialogVisible.value = true
  } else if (action === 'reject') {
    ElMessageBox.confirm('确认拒绝此样品申请?', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        try {
          const response = await axios.post(`/api/product/reject-sample/${sample.id}`)
          if (response.data.code === 0) {
            ElMessage.success('已拒绝样品申请')
            fetchSamples()
          } else {
            ElMessage.error(response.data.message || '操作失败')
          }
        } catch (error) {
          console.error('操作失败:', error)
          ElMessage.error('操作失败，请检查网络连接')
        }
      })
      .catch(() => {
        // 用户取消操作
      })
  }
}

// 提交发货表单
const submitShipForm = async () => {
  if (!shipFormRef.value || !currentSample.value) return

  await shipFormRef.value.validate(async (valid: boolean) => {
    if (valid && currentSample.value) {
      // 添加额外检查
      submittingShip.value = true
      try {
        const response = await axios.post(`/api/product/ship-sample/${currentSample.value.id}`, {
          tracking_number: shipForm.trackingNumber,
        })

        if (response.data.code === 0) {
          ElMessage.success('样品发货成功')
          shipDialogVisible.value = false
          fetchSamples()
        } else {
          ElMessage.error(response.data.message || '样品发货失败')
        }
      } catch (error) {
        console.error('样品发货失败:', error)
        ElMessage.error('样品发货失败，请检查网络连接')
      } finally {
        submittingShip.value = false
      }
    }
  })
}

// 确认收货
const confirmReceived = (sample: Sample) => {
  ElMessageBox.confirm('确认已收到样品?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'info',
  })
    .then(async () => {
      try {
        const response = await axios.post(`/api/product/confirm-sample/${sample.id}`)
        if (response.data.code === 0) {
          ElMessage.success('已确认收货')
          fetchSamples()
        } else {
          ElMessage.error(response.data.message || '操作失败')
        }
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败，请检查网络连接')
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 编辑样品申请
const editSample = (sample: Sample) => {
  // 填充编辑表单
  editForm.id = sample.id
  editForm.receiverName = sample.receiver_name
  editForm.receiverPhone = sample.receiver_phone
  editForm.receiverWechat = sample.receiver_wechat || ''
  editForm.receiverAddress = sample.receiver_address
  editForm.sampleCount = sample.sample_count
  editForm.trackingNumber = sample.tracking_number || ''
  editForm.status = sample.status
  editForm.remark = sample.remark || ''

  editDialogVisible.value = true
}

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return

  try {
    const valid = await editFormRef.value.validate()
    if (!valid) return

    submittingEdit.value = true

    const response = await axios.put(`/api/product/update-sample/${editForm.id}`, {
      receiver_name: editForm.receiverName,
      receiver_phone: editForm.receiverPhone,
      receiver_wechat: editForm.receiverWechat,
      receiver_address: editForm.receiverAddress,
      sample_count: editForm.sampleCount,
      tracking_number: editForm.trackingNumber,
      status: editForm.status,
      remark: editForm.remark,
    })

    if (response.data.code === 0) {
      ElMessage.success('修改成功')
      editDialogVisible.value = false
      fetchSamples()
    } else {
      ElMessage.error(response.data.message || '修改失败')
    }
  } catch (error) {
    console.error('修改失败:', error)
    ElMessage.error('修改失败，请检查网络连接')
  } finally {
    submittingEdit.value = false
  }
}

onMounted(() => {
  fetchSamples()
})
</script>

<style scoped>
.sample-management-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
}

.table-container {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.product-info-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-info-content {
  display: flex;
  flex-direction: column;
}

.product-name {
  font-weight: 500;
  margin-bottom: 5px;
}

.product-id {
  font-size: 12px;
  color: #909399;
}

.sample-detail {
  padding: 10px 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.detail-item {
  margin-bottom: 8px;
  line-height: 24px;
}

.detail-label {
  display: inline-block;
  width: 100px;
  color: #909399;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
}

.image-placeholder .el-icon {
  font-size: 20px;
  color: #c0c4cc;
}
</style>
