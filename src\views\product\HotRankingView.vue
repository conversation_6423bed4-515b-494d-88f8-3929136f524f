<template>
  <div class="hot-ranking-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2 class="title">
            <el-icon class="title-icon"><TrendCharts /></el-icon>
            爆品排行榜
          </h2>
          <div class="header-actions">
            <el-button type="primary" @click="updateSalesAmount" :loading="updateLoading">
              <el-icon><Refresh /></el-icon>
              更新销售额
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-area">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="独家类型">
            <el-select v-model="filterForm.exclusiveType" placeholder="请选择独家类型" clearable>
              <el-option label="全部" value="" />
              <el-option label="独家" value="exclusive" />
              <el-option label="非独家" value="non_exclusive" />
            </el-select>
          </el-form-item>
          <el-form-item label="自定义标签">
            <el-select
              v-model="filterForm.customTags"
              multiple
              filterable
              allow-create
              placeholder="请选择或输入标签"
              style="width: 200px"
            >
              <el-option v-for="tag in commonTags" :key="tag" :label="tag" :value="tag" />
            </el-select>
          </el-form-item>
          <el-form-item label="排序方式">
            <el-select v-model="filterForm.sortBy" placeholder="请选择排序方式">
              <el-option label="销售额" value="sales_amount" />
              <el-option label="销量" value="sales" />
              <el-option label="价格" value="price" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetFilter">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 排行榜列表 -->
      <div class="ranking-list" v-loading="loading">
        <div
          v-for="(product, index) in productList"
          :key="product.product_id"
          class="ranking-item"
          :class="getRankingClass(product.rank)"
        >
          <!-- 排名徽章 -->
          <div class="rank-badge" :class="getRankBadgeClass(product.rank)">
            <span v-if="product.rank <= 3" class="rank-icon">
              <el-icon v-if="product.rank === 1"><Trophy /></el-icon>
              <el-icon v-else-if="product.rank === 2"><Medal /></el-icon>
              <el-icon v-else><Star /></el-icon>
            </span>
            <span v-else class="rank-number">{{ product.rank }}</span>
          </div>

          <!-- 商品信息 -->
          <div class="product-info" @click="goToProductDetail(product)" style="cursor: pointer">
            <div class="product-image">
              <el-image
                :src="product.product_image_url"
                fit="cover"
                :preview-src-list="[product.product_image_url]"
              >
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>

            <div class="product-details">
              <div class="product-name">{{ product.product_name }}</div>
              <div class="product-meta">
                <span class="product-count">合并商品: {{ product.product_count || 1 }}个</span>
                <span class="merchant-name">{{ product.merchant_name || '未知商家' }}</span>
                <el-tag v-if="product.tag" size="small" :type="getTagType(product.tag)">
                  {{ product.tag }}
                </el-tag>
              </div>
              <div class="product-category" v-if="product.category">
                分类: {{ product.category }}
              </div>
            </div>

            <!-- 点击提示 -->
            <div class="click-hint">
              <el-icon><Edit /></el-icon>
              <span>点击编辑</span>
            </div>
          </div>

          <!-- 数据指标 -->
          <div class="metrics">
            <div class="metric-item primary">
              <div class="metric-label">销售额</div>
              <div class="metric-value">¥{{ formatNumber(product.sales_amount) }}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">销量</div>
              <div class="metric-value">{{ product.sales }}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">价格</div>
              <div class="metric-value">¥{{ formatNumber(product.price) }}</div>
            </div>
          </div>

          <!-- 佣金信息 -->
          <div class="commission-info">
            <div class="commission-item">
              <span class="commission-label">商家佣金率:</span>
              <span class="commission-value">{{ product.merchant_commission || '0' }}%</span>
            </div>
            <div class="commission-item">
              <span class="commission-label">服务费率:</span>
              <span class="commission-value">{{ product.investment_commission || '0' }}%</span>
            </div>
            <div class="store-score" v-if="product.store_score">
              <el-rate
                :model-value="parseFloat(parseFloat(product.store_score).toFixed(2))"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              />
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <el-empty v-if="!loading && productList.length === 0" description="暂无数据" />
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  TrendCharts,
  Refresh,
  Search,
  RefreshLeft,
  Trophy,
  Medal,
  Star,
  Picture,
  Edit,
} from '@element-plus/icons-vue'
import axios from 'axios'

// 路由实例
const router = useRouter()

// 获取用户角色
const userRole = computed(() => localStorage.getItem('userRole') || 'business')

// 响应式数据
const loading = ref(false)
const updateLoading = ref(false)
const productList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 筛选表单
const filterForm = reactive({
  exclusiveType: '',
  customTags: [],
  sortBy: 'sales_amount',
})

// 可用标签列表
const commonTags = ref([])

// 格式化数字
const formatNumber = (num) => {
  if (!num) return '0'
  return parseFloat(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}

// 获取排名样式类
const getRankingClass = (rank) => {
  if (rank <= 3) return 'top-three'
  if (rank <= 10) return 'top-ten'
  return ''
}

// 获取排名徽章样式类
const getRankBadgeClass = (rank) => {
  if (rank === 1) return 'gold'
  if (rank === 2) return 'silver'
  if (rank === 3) return 'bronze'
  return 'normal'
}

// 获取标签类型
const getTagType = (tag) => {
  if (tag === '独家') return 'danger'
  if (tag === '热销') return 'success'
  if (tag === '新品') return 'primary'
  return 'info'
}

// 获取排行榜数据
const fetchRankingData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      exclusive_type: filterForm.exclusiveType,
      custom_tags: filterForm.customTags.join(','),
      sort_by: filterForm.sortBy,
    }

    const response = await axios.get('/api/product/hot-ranking', { params })

    if (response.data.code === 0) {
      productList.value = response.data.data.products
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.message || '获取排行榜数据失败')
    }
  } catch (error) {
    console.error('获取排行榜数据失败:', error)
    ElMessage.error('获取排行榜数据失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 更新销售额
const updateSalesAmount = async () => {
  updateLoading.value = true
  try {
    const response = await axios.post('/api/product/update-sales-amount')

    if (response.data.code === 0) {
      ElMessage.success(response.data.message)
      // 重新获取数据
      await fetchRankingData()
    } else {
      ElMessage.error(response.data.message || '更新销售额失败')
    }
  } catch (error) {
    console.error('更新销售额失败:', error)
    ElMessage.error('更新销售额失败，请检查网络连接')
  } finally {
    updateLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchRankingData()
}

// 重置筛选
const resetFilter = () => {
  filterForm.exclusiveType = ''
  filterForm.customTags = []
  filterForm.sortBy = 'sales_amount'
  currentPage.value = 1
  fetchRankingData()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchRankingData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchRankingData()
}

// 获取标签列表
const fetchTags = async () => {
  try {
    const response = await axios.get('/api/tags/list')
    if (response.data.code === 0) {
      commonTags.value = response.data.data.map((tag) => tag.name)
    } else {
      console.error('获取标签列表失败:', response.data.message)
    }
  } catch (error) {
    console.error('获取标签列表出错:', error)
  }
}

// 跳转到商品详情页面
const goToProductDetail = (product) => {
  // 根据用户角色跳转到不同的商品管理页面
  let targetPath = '/product/list' // 默认路径（商务用户和运营用户）

  if (userRole.value === 'admin') {
    targetPath = '/admin/product/list'
  } else if (userRole.value === 'operation') {
    targetPath = '/product/list' // 运营用户使用普通路径
  }

  router.push({
    path: targetPath,
    query: {
      productName: product.product_name,
      highlight: 'true', // 标记需要高亮显示
    },
  })
}

onMounted(() => {
  fetchTags()
  fetchRankingData()
})
</script>

<style scoped>
.hot-ranking-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-icon {
  margin-right: 8px;
  font-size: 28px;
  color: #ff6b6b;
}

.filter-area {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.filter-form {
  margin: 0;
}

.filter-form :deep(.el-form-item__label) {
  color: white;
  font-weight: 500;
}

.ranking-list {
  min-height: 400px;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 16px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ranking-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ranking-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
}

.ranking-item:hover::before {
  opacity: 1;
}

.ranking-item.top-three {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border: 2px solid #fdcb6e;
}

.ranking-item.top-ten {
  background: linear-gradient(135deg, #a8e6cf 0%, #88d8c0 100%);
  border: 2px solid #00b894;
}

.rank-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: 18px;
  font-weight: bold;
  margin-right: 20px;
  flex-shrink: 0;
}

.rank-badge.gold {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #8b4513;
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4);
}

.rank-badge.silver {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #555;
  box-shadow: 0 4px 20px rgba(192, 192, 192, 0.4);
}

.rank-badge.bronze {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: white;
  box-shadow: 0 4px 20px rgba(205, 127, 50, 0.4);
}

.rank-badge.normal {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
  box-shadow: 0 4px 20px rgba(116, 185, 255, 0.3);
}

.rank-icon {
  font-size: 24px;
}

.rank-number {
  font-size: 20px;
}

.product-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 20px;
  position: relative;
  transition: all 0.3s ease;
}

.product-info:hover {
  background: rgba(64, 158, 255, 0.05);
  border-radius: 8px;
  padding: 8px;
  margin: -8px;
  margin-right: 12px;
}

.click-hint {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-info:hover .click-hint {
  opacity: 1;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 16px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-image .el-image {
  width: 100%;
  height: 100%;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #c0c4cc;
  font-size: 24px;
}

.product-details {
  flex: 1;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.product-count {
  font-size: 12px;
  color: #667eea;
  background: #e6f3ff;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.merchant-name {
  font-size: 12px;
  color: #4a5568;
  font-weight: 500;
}

.product-category {
  font-size: 12px;
  color: #718096;
}

.metrics {
  display: flex;
  gap: 24px;
  margin-right: 20px;
}

.metric-item {
  text-align: center;
  min-width: 80px;
}

.metric-item.primary .metric-value {
  color: #e53e3e;
  font-weight: bold;
  font-size: 18px;
}

.metric-label {
  font-size: 12px;
  color: #718096;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
}

.commission-info {
  min-width: 200px;
  text-align: right;
}

.commission-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.commission-label {
  color: #718096;
}

.commission-value {
  color: #38a169;
  font-weight: 600;
}

.store-score {
  margin-top: 8px;
}

.pagination-container {
  margin-top: 24px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics {
    gap: 16px;
  }

  .metric-item {
    min-width: 60px;
  }
}

@media (max-width: 768px) {
  .ranking-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .product-info {
    width: 100%;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .metrics {
    width: 100%;
    justify-content: space-around;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .commission-info {
    width: 100%;
    text-align: left;
  }
}
</style>
