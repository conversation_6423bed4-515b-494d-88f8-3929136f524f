from flask import Blueprint, request, jsonify
from app.utils.db_utils import get_connection
import json

role_templates_bp = Blueprint('role_templates', __name__, url_prefix='/api/role-templates')

def admin_required(f):
    """管理员权限装饰器"""
    def decorated_function(*args, **kwargs):
        # 这里应该添加JWT验证逻辑，暂时简化
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# 获取所有角色模板
@role_templates_bp.route('', methods=['GET'])
@admin_required
def get_role_templates():
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        query = """
            SELECT id, name, role, description, is_system, permissions,
                   DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
                   DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time
            FROM role_templates
            WHERE role != 'admin'
            ORDER BY is_system DESC, create_time ASC
        """
        
        cursor.execute(query)
        templates = cursor.fetchall()
        
        # 解析permissions JSON字段
        for template in templates:
            if template['permissions']:
                try:
                    template['permissions'] = json.loads(template['permissions'])
                except json.JSONDecodeError:
                    template['permissions'] = {}
        
        return jsonify({
            'code': 0,
            'message': '获取角色模板成功',
            'data': templates
        })
    except Exception as e:
        print(f"获取角色模板失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取角色模板失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 获取单个角色模板
@role_templates_bp.route('/<int:template_id>', methods=['GET'])
@admin_required
def get_role_template(template_id):
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        query = f"""
            SELECT id, name, role, description, is_system, permissions,
                   DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
                   DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time
            FROM role_templates WHERE id = {template_id}
        """
        cursor.execute(query)
        template = cursor.fetchone()

        if not template:
            return jsonify({
                'code': 404,
                'message': '角色模板不存在',
                'data': None
            }), 404

        # 解析权限JSON
        if template['permissions']:
            try:
                template['permissions'] = json.loads(template['permissions'])
            except json.JSONDecodeError:
                template['permissions'] = {}

        return jsonify({
            'code': 0,
            'message': '获取角色模板成功',
            'data': template
        })
    except Exception as e:
        print(f"获取角色模板失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取角色模板失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 根据role获取角色模板
@role_templates_bp.route('/by-role/<role>', methods=['GET'])
@admin_required
def get_role_template_by_role(role):
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        safe_role = role.replace("'", "''")
        query = f"""
            SELECT id, name, role, description, is_system, permissions,
                   DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
                   DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time
            FROM role_templates WHERE role = '{safe_role}'
        """
        cursor.execute(query)
        template = cursor.fetchone()

        if not template:
            return jsonify({
                'code': 404,
                'message': '角色模板不存在',
                'data': None
            }), 404

        # 解析权限JSON
        if template['permissions']:
            try:
                template['permissions'] = json.loads(template['permissions'])
            except json.JSONDecodeError:
                template['permissions'] = {}

        return jsonify({
            'code': 0,
            'message': '获取角色模板成功',
            'data': template
        })
    except Exception as e:
        print(f"获取角色模板失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取角色模板失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 创建角色模板
@role_templates_bp.route('', methods=['POST'])
@admin_required
def create_role_template():
    connection = None
    cursor = None
    try:
        data = request.json
        print(f"创建角色模板接收到的数据: {data}")

        if not data:
            return jsonify({
                'code': 400,
                'message': '请提供角色模板数据',
                'data': None
            }), 400

        name = data.get('name', '').strip()
        role = data.get('role', '').strip()
        description = data.get('description', '').strip()
        permissions = data.get('permissions', {})

        print(f"解析后的数据 - name: {name}, role: {role}, description: {description}, permissions: {permissions}")

        if not name:
            return jsonify({
                'code': 400,
                'message': '角色名称不能为空',
                'data': None
            }), 400

        if not role:
            return jsonify({
                'code': 400,
                'message': '角色类型不能为空',
                'data': None
            }), 400
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查角色名称是否已存在
        safe_name = name.replace("'", "''")
        check_query = f"SELECT id FROM role_templates WHERE name = '{safe_name}'"
        cursor.execute(check_query)
        if cursor.fetchone():
            return jsonify({
                'code': 400,
                'message': '角色名称已存在',
                'data': None
            }), 400

        # 插入新角色模板
        safe_role = role.replace("'", "''")
        safe_description = description.replace("'", "''")
        safe_permissions = json.dumps(permissions).replace("'", "''")
        insert_query = f"""
            INSERT INTO role_templates (name, role, description, permissions)
            VALUES ('{safe_name}', '{safe_role}', '{safe_description}', '{safe_permissions}')
        """
        cursor.execute(insert_query)
        connection.commit()

        # 获取新创建的角色模板
        template_id = cursor.lastrowid
        select_query = f"""
            SELECT id, name, role, description, is_system, permissions,
                   DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
                   DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time
            FROM role_templates WHERE id = {template_id}
        """
        cursor.execute(select_query)
        template = cursor.fetchone()
        
        if template and template['permissions']:
            try:
                template['permissions'] = json.loads(template['permissions'])
            except json.JSONDecodeError:
                template['permissions'] = {}
        
        return jsonify({
            'code': 0,
            'message': '创建角色模板成功',
            'data': template
        })
    except Exception as e:
        print(f"创建角色模板失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'创建角色模板失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 更新角色模板
@role_templates_bp.route('/<int:template_id>', methods=['PUT'])
@admin_required
def update_role_template(template_id):
    connection = None
    cursor = None
    try:
        data = request.json
        if not data:
            return jsonify({
                'code': 400,
                'message': '请提供更新数据',
                'data': None
            }), 400
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查角色模板是否存在
        check_query = f"SELECT id, is_system FROM role_templates WHERE id = {template_id}"
        cursor.execute(check_query)
        existing_template = cursor.fetchone()

        if not existing_template:
            return jsonify({
                'code': 404,
                'message': '角色模板不存在',
                'data': None
            }), 404

        # 构建更新字段
        update_fields = []

        if 'name' in data:
            # 检查名称是否与其他模板冲突
            safe_name = data['name'].replace("'", "''")
            name_check_query = f"SELECT id FROM role_templates WHERE name = '{safe_name}' AND id != {template_id}"
            cursor.execute(name_check_query)
            if cursor.fetchone():
                return jsonify({
                    'code': 400,
                    'message': '角色名称已存在',
                    'data': None
                }), 400
            update_fields.append(f"name = '{safe_name}'")

        if 'role' in data:
            safe_role = data['role'].replace("'", "''")
            update_fields.append(f"role = '{safe_role}'")

        if 'description' in data:
            safe_description = data['description'].replace("'", "''")
            update_fields.append(f"description = '{safe_description}'")

        if 'permissions' in data:
            safe_permissions = json.dumps(data['permissions']).replace("'", "''")
            update_fields.append(f"permissions = '{safe_permissions}'")

        if not update_fields:
            return jsonify({
                'code': 400,
                'message': '没有提供有效的更新字段',
                'data': None
            }), 400

        # 执行更新
        update_query = f"UPDATE role_templates SET {', '.join(update_fields)} WHERE id = {template_id}"
        cursor.execute(update_query)
        connection.commit()

        # 获取更新后的角色模板
        select_query = f"""
            SELECT id, name, role, description, is_system, permissions,
                   DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
                   DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time
            FROM role_templates WHERE id = {template_id}
        """
        cursor.execute(select_query)
        template = cursor.fetchone()
        
        if template and template['permissions']:
            try:
                template['permissions'] = json.loads(template['permissions'])
            except json.JSONDecodeError:
                template['permissions'] = {}
        
        return jsonify({
            'code': 0,
            'message': '更新角色模板成功',
            'data': template
        })
    except Exception as e:
        print(f"更新角色模板失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新角色模板失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 删除角色模板
@role_templates_bp.route('/<int:template_id>', methods=['DELETE'])
@admin_required
def delete_role_template(template_id):
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查角色模板是否存在且不是系统角色
        check_query = f"SELECT id, is_system, name FROM role_templates WHERE id = {template_id}"
        cursor.execute(check_query)
        template = cursor.fetchone()

        if not template:
            return jsonify({
                'code': 404,
                'message': '角色模板不存在',
                'data': None
            }), 404

        if template['is_system']:
            return jsonify({
                'code': 400,
                'message': '系统角色模板不能删除',
                'data': None
            }), 400

        # 检查是否有用户使用此角色模板
        user_check_query = f"SELECT COUNT(*) as count FROM business_user WHERE role_template_id = {template_id}"
        cursor.execute(user_check_query)
        user_count = cursor.fetchone()['count']

        if user_count > 0:
            return jsonify({
                'code': 400,
                'message': f'该角色模板正在被 {user_count} 个用户使用，无法删除',
                'data': None
            }), 400

        # 删除角色模板
        delete_query = f"DELETE FROM role_templates WHERE id = {template_id}"
        cursor.execute(delete_query)
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '删除角色模板成功',
            'data': None
        })
    except Exception as e:
        print(f"删除角色模板失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'删除角色模板失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
