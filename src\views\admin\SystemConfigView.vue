<template>
  <div class="system-config-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>系统配置管理</h3>
          <el-tag type="danger" size="small">管理员专用</el-tag>
        </div>
      </template>

      <!-- Cookie配置区域 -->
      <div class="config-section">
        <h4>快手小店Cookie配置</h4>
        <p class="config-desc">用于商品和订单数据爬取的Cookie配置</p>

        <el-form :model="cookieForm" label-width="120px">
          <el-form-item label="Cookie值">
            <el-input
              v-model="cookieForm.cookie"
              type="textarea"
              :rows="4"
              placeholder="请输入快手小店的Cookie值"
              show-word-limit
              maxlength="5000"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="updateCookie" :loading="updating">
              更新Cookie
            </el-button>
            <el-button @click="testCookie" :loading="testing"> 测试Cookie </el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-divider />

      <!-- 自动同步管理 -->
      <div class="config-section">
        <h4>自动同步管理</h4>
        <p class="config-desc">管理商品和订单的自动同步服务</p>

        <div class="sync-status">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>商品同步</span>
                    <el-tag
                      :type="syncStatus.product_thread_alive ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ syncStatus.product_thread_alive ? '运行中' : '已停止' }}
                    </el-tag>
                  </div>
                </template>
                <div class="sync-info">
                  <p><strong>同步间隔:</strong> {{ syncStatus.product_sync_interval || 600 }} 秒</p>
                  <p>
                    <strong>当前状态:</strong>
                    {{ syncStatus.product_sync_running ? '同步中' : '空闲' }}
                  </p>
                  <p><strong>同步内容:</strong> 所有活动中的商品</p>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>订单同步</span>
                    <el-tag
                      :type="syncStatus.order_thread_alive ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ syncStatus.order_thread_alive ? '运行中' : '已停止' }}
                    </el-tag>
                  </div>
                </template>
                <div class="sync-info">
                  <p><strong>同步间隔:</strong> {{ syncStatus.order_sync_interval || 10 }} 秒</p>
                  <p>
                    <strong>当前状态:</strong>
                    {{ syncStatus.order_sync_running ? '同步中' : '空闲' }}
                  </p>
                  <p>
                    <strong>起始日期:</strong> {{ syncStatus.order_start_date || '2025-06-01' }}
                  </p>
                  <div>
                    <p>
                      <strong>收入订单最后同步:</strong>
                      {{ syncStatus.income_last_sync_timestamp || '未同步' }}
                    </p>
                    <p>
                      <strong>支出订单最后同步:</strong>
                      {{ syncStatus.expense_last_sync_timestamp || '未同步' }}
                    </p>
                    <p>
                      <strong>同步计数:</strong>
                      {{ syncStatus.order_sync_count || 0 }}/{{
                        syncStatus.order_sync_count_limit || 360
                      }}
                      <span style="color: #909399; font-size: 12px">
                        ({{
                          Math.round(
                            ((syncStatus.order_sync_count || 0) /
                              (syncStatus.order_sync_count_limit || 360)) *
                              100,
                          )
                        }}%)
                      </span>
                    </p>
                    <p>
                      <strong>同步策略:</strong>
                      每次最多10天，增量同步；每360次(1小时)扩展为近两天同步
                    </p>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <div class="sync-controls" style="margin-top: 20px">
          <el-button
            type="success"
            @click="startAutoSync"
            :loading="operationLoading"
            :disabled="syncStatus.product_thread_alive && syncStatus.order_thread_alive"
          >
            启动自动同步
          </el-button>
          <el-button
            type="danger"
            @click="stopAutoSync"
            :loading="operationLoading"
            :disabled="!syncStatus.product_thread_alive && !syncStatus.order_thread_alive"
          >
            停止自动同步
          </el-button>
          <el-button @click="refreshSyncStatus" :loading="statusLoading"> 刷新状态 </el-button>
        </div>
      </div>

      <el-divider />

      <!-- 其他系统配置 -->
      <div class="config-section">
        <h4>其他系统配置</h4>

        <el-table :data="configList" border style="width: 100%">
          <el-table-column prop="config_key" label="配置键" width="200" />
          <el-table-column prop="config_desc" label="配置描述" width="250" />
          <el-table-column prop="config_value" label="配置值" show-overflow-tooltip />
          <el-table-column prop="update_time" label="更新时间" width="180" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="editConfig(scope.row)">
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 编辑配置对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑系统配置" width="500px">
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="100px">
        <el-form-item label="配置键" prop="configKey">
          <el-input v-model="editForm.configKey" :disabled="editForm.isEdit" />
        </el-form-item>
        <el-form-item label="配置描述" prop="configDesc">
          <el-input v-model="editForm.configDesc" />
        </el-form-item>
        <el-form-item label="配置值" prop="configValue">
          <el-input
            v-model="editForm.configValue"
            type="textarea"
            :rows="3"
            placeholder="请输入配置值"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEditForm" :loading="submitting"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import axios from 'axios'

// 响应式数据
const cookieForm = reactive({
  cookie: '',
})

const configList = ref([])
const updating = ref(false)
const testing = ref(false)

// 自动同步状态
const syncStatus = ref({
  product_sync_running: false,
  order_sync_running: false,
  product_thread_alive: false,
  order_thread_alive: false,
  product_sync_interval: 600,
  order_sync_interval: 10,
  order_start_date: '2025-06-01',
  last_product_count: 0,
  order_sync_count: 0,
  order_sync_count_limit: 360,
  income_order_sync_info: {
    synced_count: 0,
    total_count: 0,
    last_sync_time: '',
  },
  expense_order_sync_info: {
    synced_count: 0,
    total_count: 0,
    last_sync_time: '',
  },
  income_last_sync_timestamp: '',
  expense_last_sync_timestamp: '',
})

const operationLoading = ref(false)
const statusLoading = ref(false)

// 编辑对话框
const editDialogVisible = ref(false)
const submitting = ref(false)
const editFormRef = ref<FormInstance | null>(null)

const editForm = reactive({
  configKey: '',
  configDesc: '',
  configValue: '',
  isEdit: false,
})

const editRules = {
  configKey: [{ required: true, message: '请输入配置键', trigger: 'blur' }],
  configValue: [{ required: true, message: '请输入配置值', trigger: 'blur' }],
}

// 获取系统配置
const fetchConfigs = async () => {
  try {
    const response = await axios.get('/api/system/config')
    if (response.data.code === 0) {
      configList.value = response.data.data

      // 找到cookie配置并填充表单
      const cookieConfig = configList.value.find(
        (config: any) => config.config_key === 'kuaishou_cookie',
      ) as any
      if (cookieConfig && cookieConfig.config_value) {
        cookieForm.cookie = cookieConfig.config_value
      }
    } else {
      ElMessage.error(response.data.message || '获取系统配置失败')
    }
  } catch (error) {
    console.error('获取系统配置失败:', error)
    ElMessage.error('获取系统配置失败，请检查网络连接')
  }
}

// 更新Cookie
const updateCookie = async () => {
  if (!cookieForm.cookie.trim()) {
    ElMessage.warning('请输入Cookie值')
    return
  }

  updating.value = true
  try {
    const response = await axios.put('/api/system/config/kuaishou_cookie', {
      config_value: cookieForm.cookie,
      config_desc: '快手小店Cookie配置',
    })

    if (response.data.code === 0) {
      ElMessage.success('Cookie更新成功')
      fetchConfigs() // 刷新配置列表
    } else {
      ElMessage.error(response.data.message || 'Cookie更新失败')
    }
  } catch (error) {
    console.error('Cookie更新失败:', error)
    ElMessage.error('Cookie更新失败，请检查网络连接')
  } finally {
    updating.value = false
  }
}

// 测试Cookie
const testCookie = async () => {
  if (!cookieForm.cookie.trim()) {
    ElMessage.warning('请先输入Cookie值')
    return
  }

  testing.value = true
  try {
    // 这里可以调用一个测试接口来验证Cookie是否有效
    // 暂时用一个简单的提示
    await new Promise((resolve) => setTimeout(resolve, 1000))
    ElMessage.success('Cookie测试完成（请手动验证功能是否正常）')
  } catch (error) {
    console.error('Cookie测试失败:', error)
    ElMessage.error('Cookie测试失败')
  } finally {
    testing.value = false
  }
}

// 编辑配置
const editConfig = (config: any) => {
  editForm.configKey = config.config_key
  editForm.configDesc = config.config_desc
  editForm.configValue = config.config_value
  editForm.isEdit = true
  editDialogVisible.value = true
}

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return

  try {
    const valid = await editFormRef.value.validate()
    if (!valid) return

    submitting.value = true

    const response = await axios.put(`/api/system/config/${editForm.configKey}`, {
      config_value: editForm.configValue,
      config_desc: editForm.configDesc,
    })

    if (response.data.code === 0) {
      ElMessage.success('配置更新成功')
      editDialogVisible.value = false
      fetchConfigs() // 刷新配置列表

      // 如果是cookie配置，同时更新cookie表单
      if (editForm.configKey === 'kuaishou_cookie') {
        cookieForm.cookie = editForm.configValue
      }
    } else {
      ElMessage.error(response.data.message || '配置更新失败')
    }
  } catch (error) {
    console.error('配置更新失败:', error)
    ElMessage.error('配置更新失败，请检查网络连接')
  } finally {
    submitting.value = false
  }
}

// 防抖控制
let fetchStatusTimer: number | null = null

// 获取自动同步状态
const fetchSyncStatus = async (showError = true) => {
  // 防抖：如果正在请求中，则跳过
  if (statusLoading.value) {
    return
  }

  statusLoading.value = true
  try {
    const response = await axios.get('/api/system/auto-sync/status')
    if (response.data.code === 0) {
      syncStatus.value = response.data.data
    } else {
      if (showError) {
        ElMessage.error(response.data.message || '获取同步状态失败')
      }
    }
  } catch (error) {
    console.error('获取同步状态失败:', error)
    if (showError) {
      ElMessage.error('获取同步状态失败，请检查网络连接')
    }
  } finally {
    statusLoading.value = false
  }
}

// 启动自动同步
const startAutoSync = async () => {
  operationLoading.value = true
  try {
    const response = await axios.post('/api/system/auto-sync/start')
    if (response.data.code === 0) {
      ElMessage.success('自动同步服务启动成功')
      await fetchSyncStatus() // 刷新状态
    } else {
      ElMessage.error(response.data.message || '启动自动同步失败')
    }
  } catch (error) {
    console.error('启动自动同步失败:', error)
    ElMessage.error('启动自动同步失败，请检查网络连接')
  } finally {
    operationLoading.value = false
  }
}

// 停止自动同步
const stopAutoSync = async () => {
  operationLoading.value = true
  try {
    const response = await axios.post('/api/system/auto-sync/stop')
    if (response.data.code === 0) {
      ElMessage.success('自动同步服务停止成功')
      await fetchSyncStatus() // 刷新状态
    } else {
      ElMessage.error(response.data.message || '停止自动同步失败')
    }
  } catch (error) {
    console.error('停止自动同步失败:', error)
    ElMessage.error('停止自动同步失败，请检查网络连接')
  } finally {
    operationLoading.value = false
  }
}

// 刷新同步状态
const refreshSyncStatus = () => {
  fetchSyncStatus()
}

// 打开数据库监控
const openDatabaseMonitor = () => {
  // 在新窗口中打开数据库监控页面
  window.open('/admin/database-monitor', '_blank')
}

onMounted(() => {
  fetchConfigs()
  fetchSyncStatus() // 获取同步状态

  // 定时刷新同步状态（每60秒，减少请求频率）
  fetchStatusTimer = setInterval(() => {
    fetchSyncStatus(false) // 静默获取，不显示错误
  }, 60000) as unknown as number
})

onUnmounted(() => {
  // 清除定时器
  if (fetchStatusTimer !== null) {
    clearInterval(fetchStatusTimer)
  }
})
</script>

<style scoped>
.system-config-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-section {
  margin-bottom: 30px;
}

.config-section h4 {
  margin-bottom: 10px;
  color: #333;
}

.config-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

/* 自动同步管理样式 */
.sync-status .el-card {
  height: 100%;
}

.sync-info p {
  margin: 8px 0;
  font-size: 14px;
}

.sync-info strong {
  color: #333;
  font-weight: 600;
}

.sync-controls {
  text-align: center;
}

.sync-controls .el-button {
  margin: 0 8px;
}
</style>
