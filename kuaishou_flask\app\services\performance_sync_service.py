"""
业绩统计同步服务
每10秒同步一次，从6月1日开始，一次最多同步十天的数据
"""
import logging
import threading
import time
from datetime import datetime, timedelta
from app.utils.db_utils import get_connection

class PerformanceSyncService:
    def __init__(self):
        self.running = False
        self.sync_thread = None
        self.sync_interval = 30  # 10秒同步一次
        self.batch_days = 10     # 一次最多同步10天
        self.start_date = datetime(2025, 6, 1)  # 从6月1日开始
        
    def start(self):
        """启动业绩同步服务"""
        if self.running:
            return
            
        self.running = True
        self.sync_thread = threading.Thread(target=self._sync_loop, daemon=True)
        self.sync_thread.start()
        print("📊 业绩统计同步服务已启动")
        logging.info("业绩统计同步服务已启动")
        
    def stop(self):
        """停止业绩同步服务"""
        self.running = False
        if self.sync_thread:
            self.sync_thread.join()
        print("📊 业绩统计同步服务已停止")
        logging.info("业绩统计同步服务已停止")
        
    def _sync_loop(self):
        """同步循环"""
        while self.running:
            try:
                # 每次同步前先检查并更新配置
                self._update_sync_configs()

                # 同步达人业绩
                self._sync_promoter_performance()

                # 等待一段时间再同步商务业绩
                time.sleep(2)

                # 同步商务业绩
                self._sync_business_performance()

            except Exception as e:
                print(f"❌ 业绩同步出错: {str(e)}")
                logging.error(f"业绩同步出错: {str(e)}")

            # 等待下次同步
            time.sleep(self.sync_interval)

    def _update_sync_configs(self):
        """检查并更新同步配置（仅达人业绩需要配置）"""
        connection = None
        cursor = None
        try:
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)

            # 只确保达人业绩配置存在
            self._ensure_promoter_config(cursor)

            connection.commit()

        except Exception as e:
            logging.error(f"更新同步配置失败: {str(e)}")
            if connection:
                connection.rollback()
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def _ensure_promoter_config(self, cursor):
        """确保达人业绩配置存在"""
        try:
            # 检查达人业绩配置是否存在
            check_query = """
                SELECT id FROM sync_config
                WHERE table_name = 'promoter_performance'
                AND (key_field IS NULL OR key_field = '')
            """
            cursor.execute(check_query)
            result = cursor.fetchone()

            if not result:
                # 插入达人业绩配置
                insert_query = """
                    INSERT INTO sync_config (table_name, last_sync_date, create_time, update_time)
                    VALUES ('promoter_performance', %s, NOW(), NOW())
                """
                cursor.execute(insert_query, [self.start_date.strftime('%Y-%m-%d')])
                print("📊 创建达人业绩同步配置")

        except Exception as e:
            logging.error(f"确保达人业绩配置失败: {str(e)}")

    def _get_next_sync_date(self, table_name, key_field=None, key_value=None):
        """获取下次需要同步的日期"""
        connection = None
        cursor = None
        try:
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # 从配置表获取最后同步时间
            if key_field and key_value:
                # 商务业绩需要按商务名称和商品ID查询
                config_query = """
                    SELECT last_sync_date FROM sync_config 
                    WHERE table_name = %s AND key_field = %s AND key_value = %s
                """
                cursor.execute(config_query, [table_name, key_field, key_value])
            else:
                # 达人业绩只需要查询表名
                config_query = """
                    SELECT last_sync_date FROM sync_config 
                    WHERE table_name = %s AND (key_field IS NULL OR key_field = '')
                """
                cursor.execute(config_query, [table_name])
                
            result = cursor.fetchone()
            
            if result and result['last_sync_date']:
                # 从上次同步日期的下一天开始
                last_date = result['last_sync_date']
                # 如果是字符串，转换为日期对象
                if isinstance(last_date, str):
                    last_date = datetime.strptime(last_date, '%Y-%m-%d')
                # 如果是date对象，转换为datetime对象
                elif hasattr(last_date, 'year'):
                    last_date = datetime.combine(last_date, datetime.min.time())

                return last_date + timedelta(days=1)
            else:
                # 如果没有记录，从6月1日开始
                return self.start_date
                
        except Exception as e:
            logging.error(f"获取同步日期失败: {str(e)}")
            return self.start_date
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
                
    def _update_sync_config(self, table_name, sync_date, key_field=None, key_value=None):
        """更新同步配置"""
        connection = None
        cursor = None
        try:
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)

            # 确保sync_date是datetime对象，然后格式化为字符串
            if hasattr(sync_date, 'strftime'):
                sync_date_str = sync_date.strftime('%Y-%m-%d')
            else:
                sync_date_str = str(sync_date)
            
            if key_field and key_value:
                # 检查记录是否存在
                check_query = """
                    SELECT id FROM sync_config 
                    WHERE table_name = %s AND key_field = %s AND key_value = %s
                """
                cursor.execute(check_query, [table_name, key_field, key_value])
                result = cursor.fetchone()
                
                if result:
                    # 更新现有记录
                    update_query = """
                        UPDATE sync_config 
                        SET last_sync_date = %s, update_time = NOW()
                        WHERE id = %s
                    """
                    cursor.execute(update_query, [sync_date_str, result['id']])
                else:
                    # 插入新记录
                    insert_query = """
                        INSERT INTO sync_config (table_name, key_field, key_value, last_sync_date, create_time, update_time)
                        VALUES (%s, %s, %s, %s, NOW(), NOW())
                    """
                    cursor.execute(insert_query, [table_name, key_field, key_value, sync_date_str])
            else:
                # 达人业绩配置
                check_query = """
                    SELECT id FROM sync_config 
                    WHERE table_name = %s AND (key_field IS NULL OR key_field = '')
                """
                cursor.execute(check_query, [table_name])
                result = cursor.fetchone()
                
                if result:
                    # 更新现有记录
                    update_query = """
                        UPDATE sync_config 
                        SET last_sync_date = %s, update_time = NOW()
                        WHERE id = %s
                    """
                    cursor.execute(update_query, [sync_date_str, result['id']])
                else:
                    # 插入新记录
                    insert_query = """
                        INSERT INTO sync_config (table_name, last_sync_date, create_time, update_time)
                        VALUES (%s, %s, NOW(), NOW())
                    """
                    cursor.execute(insert_query, [table_name, sync_date_str])
                    
            connection.commit()
            
        except Exception as e:
            logging.error(f"更新同步配置失败: {str(e)}")
            if connection:
                connection.rollback()
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
                
    def _sync_promoter_performance(self):
        """同步达人业绩"""
        try:
            # 获取下次需要同步的日期
            next_date = self._get_next_sync_date('promoter_performance')
            
            # 如果已经同步到今天，跳过
            today = datetime.now().date()
            if next_date.date() > today:
                return
                
            # 计算本次同步的结束日期（最多10天）
            end_date = min(next_date + timedelta(days=self.batch_days - 1), datetime.now())
            
            print(f"📊 开始同步达人业绩: {next_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            
            connection = None
            cursor = None
            try:
                connection = get_connection()
                cursor = connection.cursor(dictionary=True)
                
                # 按天同步达人业绩数据
                current_date = next_date
                sync_count = 0
                
                while current_date <= end_date:
                    date_str = current_date.strftime('%Y-%m-%d')

                    # 查询该天的达人业绩数据
                    promoter_query = """
                        SELECT
                            promoter_id,
                            promoter_name,
                            promotion_type as promoter_type,
                            COALESCE(talent_business, '') as business_contact,
                            COUNT(*) as order_count,
                            SUM(payment_amount) as payment_amount,
                            SUM(talent_commission) as commission_amount
                        FROM `order`
                        WHERE DATE(order_time) = %s
                        AND promoter_id IS NOT NULL
                        AND promoter_id != ''
                        AND promoter_name IS NOT NULL
                        AND promoter_name != ''
                        AND promotion_type = 1  -- 只统计达人
                        AND order_status != 80  -- 排除已失效订单
                        AND fund_type = 1  -- 只统计收入订单
                        GROUP BY promoter_id, promoter_name, promotion_type, talent_business
                    """

                    cursor.execute(promoter_query, [date_str])
                    promoters = cursor.fetchall()

                    if promoters:
                        # 插入或更新业绩数据
                        for promoter in promoters:
                            self._upsert_promoter_performance(cursor, promoter, date_str)
                            sync_count += 1

                    current_date += timedelta(days=1)
                
                connection.commit()
                
                # 更新同步配置
                self._update_sync_config('promoter_performance', end_date)
                
                if sync_count > 0:
                    print(f"✅ 达人业绩同步完成，共处理 {sync_count} 条记录")
                    
            except Exception as e:
                logging.error(f"同步达人业绩失败: {str(e)}")
                if connection:
                    connection.rollback()
            finally:
                if cursor:
                    cursor.close()
                if connection:
                    connection.close()
                    
        except Exception as e:
            logging.error(f"达人业绩同步出错: {str(e)}")

    def _upsert_promoter_performance(self, cursor, promoter, date_str):
        """插入或更新达人业绩记录"""
        try:
            # 检查记录是否存在
            check_query = """
                SELECT id FROM promoter_performance
                WHERE promoter_id = %s AND promoter_type = %s AND business_contact = %s AND day = %s
            """
            cursor.execute(check_query, [
                promoter['promoter_id'],
                promoter['promoter_type'],
                promoter['business_contact'] or '',
                date_str
            ])
            result = cursor.fetchone()

            # 计算月份
            month = date_str[:7]  # YYYY-MM

            if result:
                # 更新现有记录
                update_query = """
                    UPDATE promoter_performance
                    SET
                        promoter_name = %s,
                        month = %s,
                        order_count = %s,
                        payment_amount = %s,
                        commission_amount = %s,
                        update_time = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_query, [
                    promoter['promoter_name'],
                    month,
                    promoter['order_count'],
                    float(promoter['payment_amount'] or 0),
                    float(promoter['commission_amount'] or 0),
                    result['id']
                ])
                # 不打印更新信息，减少日志输出
                pass
            else:
                # 插入新记录
                insert_query = """
                    INSERT INTO promoter_performance (
                        promoter_id, promoter_name, promoter_type, business_contact,
                        month, day, order_count, payment_amount, commission_amount,
                        update_time
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                """
                cursor.execute(insert_query, [
                    promoter['promoter_id'],
                    promoter['promoter_name'],
                    promoter['promoter_type'],
                    promoter['business_contact'] or '',
                    month,
                    date_str,
                    promoter['order_count'],
                    float(promoter['payment_amount'] or 0),
                    float(promoter['commission_amount'] or 0)
                ])
                # 不打印插入信息，减少日志输出

        except Exception as e:
            logging.error(f"更新达人业绩记录失败: {str(e)}")

    def _sync_business_performance(self):
        """同步商务业绩（使用线程池控制并发）"""
        try:
            connection = None
            cursor = None
            try:
                connection = get_connection()
                cursor = connection.cursor(dictionary=True)

                # 获取所有商务用户
                business_query = """
                    SELECT DISTINCT name as business_name
                    FROM business_user
                    WHERE role = 'business'
                    AND name IS NOT NULL
                    AND name != ''
                    ORDER BY name
                """
                cursor.execute(business_query)
                businesses = cursor.fetchall()


                # 使用线程池控制并发数量，避免数据库连接过多
                from concurrent.futures import ThreadPoolExecutor

                # 设置最大并发线程数为5，避免数据库连接池耗尽
                max_workers = min(5, len(businesses))

                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 提交所有任务
                    futures = []
                    for business in businesses:
                        business_name = business['business_name']
                        future = executor.submit(self._sync_single_business_performance, business_name)
                        futures.append(future)

                    # 等待所有任务完成
                    completed_count = 0
                    for future in futures:
                        try:
                            future.result()  # 获取结果，如果有异常会抛出
                            completed_count += 1
                        except Exception as e:
                            logging.error(f"商务业绩同步任务失败: {str(e)}")

            except Exception as e:
                logging.error(f"同步商务业绩失败: {str(e)}")
            finally:
                if cursor:
                    cursor.close()
                if connection:
                    connection.close()

        except Exception as e:
            logging.error(f"商务业绩同步出错: {str(e)}")

    def _sync_single_business_performance(self, business_name):
        """同步单个商务的业绩数据"""
        connection = None
        cursor = None
        try:
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)

            # 先检查该商务是否有订单
            check_query = """
                SELECT COUNT(*) as total_orders
                FROM `order`
                WHERE talent_business = %s
                AND order_status != 80
            """
            cursor.execute(check_query, [business_name])
            check_result = cursor.fetchone()

            if check_result['total_orders'] == 0:
                return

            # 查询该商务的所有订单数据，按天分组（不限制时间）
            business_query = """
                SELECT
                    DATE(order_time) as order_date,
                    -- 收入订单统计
                    SUM(CASE WHEN fund_type = 1 THEN 1 ELSE 0 END) as income_order_count,
                    SUM(CASE WHEN fund_type = 1 THEN payment_amount ELSE 0 END) as income_payment_amount,
                    -- 收入预估服务费：排除已失效订单，有商品信息的按商品费率计算，最后乘以0.85
                    SUM(CASE
                        WHEN fund_type = 1 AND order_status != 80 AND p.product_id IS NOT NULL
                        THEN payment_amount * COALESCE(p.investment_commission, 0) / 100 * 0.85
                        ELSE 0
                    END) as income_estimated_service_fee,
                    -- 收入结算服务费：只统计已结算订单（状态=60），有商品信息的按商品费率计算，最后乘以0.85
                    SUM(CASE
                        WHEN fund_type = 1 AND order_status = 60 AND p.product_id IS NOT NULL
                        THEN payment_amount * COALESCE(p.investment_commission, 0) / 100 * 0.85
                        ELSE 0
                    END) as income_settled_service_fee,
                    -- 支出订单统计
                    SUM(CASE WHEN fund_type = 2 THEN 1 ELSE 0 END) as expense_order_count,
                    SUM(CASE WHEN fund_type = 2 THEN payment_amount ELSE 0 END) as expense_payment_amount,
                    -- 支出预估服务费：直接使用订单表的estimated_service_fee字段
                    SUM(CASE WHEN fund_type = 2 THEN COALESCE(estimated_service_fee, 0) ELSE 0 END) as expense_estimated_service_fee,
                    -- 支出结算服务费：直接使用订单表的actual_service_fee字段
                    SUM(CASE WHEN fund_type = 2 THEN COALESCE(actual_service_fee, 0) ELSE 0 END) as expense_settled_service_fee
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE o.talent_business = %s
                GROUP BY DATE(order_time)
                HAVING (income_order_count > 0 OR expense_order_count > 0)
                ORDER BY order_date
            """

            cursor.execute(business_query, [business_name])
            daily_data = cursor.fetchall()

            if not daily_data:
                return

            # 处理每天的数据
            sync_count = 0
            for data in daily_data:
                date_str = data['order_date'].strftime('%Y-%m-%d')

                # 插入或更新业绩记录
                self._upsert_business_performance_simple(cursor, business_name, data, date_str)
                sync_count += 1

            connection.commit()
            print(f"✅ 商务 {business_name} 同步完成，共 {sync_count} 天数据")

        except Exception as e:
            logging.error(f"同步商务 {business_name} 业绩失败: {str(e)}")
            if connection:
                connection.rollback()
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def _upsert_business_performance(self, cursor, business_name, product_id, data, date_str):
        """插入或更新商务业绩记录"""
        try:
            # 检查记录是否存在
            check_query = """
                SELECT id FROM business_performance
                WHERE business_name = %s AND product_id = %s AND day = %s
            """
            cursor.execute(check_query, [business_name, product_id, date_str])
            result = cursor.fetchone()

            # 计算月份和净服务费
            month = date_str[:7]  # YYYY-MM
            income_estimated = float(data['income_estimated_service_fee'] or 0)
            expense_estimated = float(data['expense_estimated_service_fee'] or 0)
            net_estimated = income_estimated - expense_estimated

            income_settled = float(data['income_settled_service_fee'] or 0)
            expense_settled = float(data['expense_settled_service_fee'] or 0)
            net_settled = income_settled - expense_settled

            if result:
                # 更新现有记录
                update_query = """
                    UPDATE business_performance
                    SET
                        month = %s,
                        income_order_count = %s,
                        income_payment_amount = %s,
                        income_estimated_service_fee = %s,
                        income_settled_service_fee = %s,
                        expense_order_count = %s,
                        expense_payment_amount = %s,
                        expense_estimated_service_fee = %s,
                        expense_settled_service_fee = %s,
                        net_estimated_service_fee = %s,
                        net_settled_service_fee = %s,
                        update_time = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_query, [
                    month,
                    data['income_order_count'] or 0,
                    float(data['income_payment_amount'] or 0),
                    income_estimated,
                    income_settled,
                    data['expense_order_count'] or 0,
                    float(data['expense_payment_amount'] or 0),
                    expense_estimated,
                    expense_settled,
                    net_estimated,
                    net_settled,
                    result['id']
                ])
                print(f"   ✅ 更新商务记录: {business_name}-{product_id} ({date_str})")
            else:
                # 插入新记录
                insert_query = """
                    INSERT INTO business_performance (
                        business_name, product_id, month, day,
                        income_order_count, income_payment_amount, income_estimated_service_fee, income_settled_service_fee,
                        expense_order_count, expense_payment_amount, expense_estimated_service_fee, expense_settled_service_fee,
                        net_estimated_service_fee, net_settled_service_fee, update_time
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                """
                cursor.execute(insert_query, [
                    business_name, product_id, month, date_str,
                    data['income_order_count'] or 0,
                    float(data['income_payment_amount'] or 0),
                    income_estimated,
                    income_settled,
                    data['expense_order_count'] or 0,
                    float(data['expense_payment_amount'] or 0),
                    expense_estimated,
                    expense_settled,
                    net_estimated,
                    net_settled
                ])
                print(f"   ✅ 插入商务记录: {business_name}-{product_id} ({date_str}) - 收入订单{data['income_order_count']}条")

        except Exception as e:
            logging.error(f"更新商务业绩记录失败: {str(e)}")

    def _upsert_business_performance_simple(self, cursor, business_name, data, date_str):
        """插入或更新商务业绩记录（简化版，不需要product_id）"""
        try:
            # 检查记录是否存在（product_id为空字符串）
            check_query = """
                SELECT id FROM business_performance
                WHERE business_name = %s AND day = %s AND product_id = ''
            """
            cursor.execute(check_query, [business_name, date_str])
            result = cursor.fetchone()

            # 计算月份和净服务费
            month = date_str[:7]  # YYYY-MM

            # 获取所有字段值并转换为正确的数据类型
            income_order_count = int(data['income_order_count'] or 0)
            income_payment_amount = float(data['income_payment_amount'] or 0)
            income_estimated_service_fee = float(data['income_estimated_service_fee'] or 0)
            income_settled_service_fee = float(data['income_settled_service_fee'] or 0)

            expense_order_count = int(data['expense_order_count'] or 0)
            expense_payment_amount = float(data['expense_payment_amount'] or 0)
            expense_estimated_service_fee = float(data['expense_estimated_service_fee'] or 0)
            expense_settled_service_fee = float(data['expense_settled_service_fee'] or 0)

            # 计算净服务费
            net_estimated_service_fee = income_estimated_service_fee - expense_estimated_service_fee
            net_settled_service_fee = income_settled_service_fee - expense_settled_service_fee

            if result:
                # 更新现有记录
                update_query = """
                    UPDATE business_performance
                    SET
                        month = %s,
                        income_order_count = %s,
                        income_payment_amount = %s,
                        income_estimated_service_fee = %s,
                        income_settled_service_fee = %s,
                        expense_order_count = %s,
                        expense_payment_amount = %s,
                        expense_estimated_service_fee = %s,
                        expense_settled_service_fee = %s,
                        net_estimated_service_fee = %s,
                        net_settled_service_fee = %s,
                        update_time = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_query, [
                    month,
                    income_order_count,
                    income_payment_amount,
                    income_estimated_service_fee,
                    income_settled_service_fee,
                    expense_order_count,
                    expense_payment_amount,
                    expense_estimated_service_fee,
                    expense_settled_service_fee,
                    net_estimated_service_fee,
                    net_settled_service_fee,
                    result['id']
                ])
            else:
                # 插入新记录
                insert_query = """
                    INSERT INTO business_performance (
                        business_name, product_id, month, day,
                        income_order_count, income_payment_amount, income_estimated_service_fee, income_settled_service_fee,
                        expense_order_count, expense_payment_amount, expense_estimated_service_fee, expense_settled_service_fee,
                        net_estimated_service_fee, net_settled_service_fee, update_time
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                """
                cursor.execute(insert_query, [
                    business_name,                  # business_name
                    '',                            # product_id (空字符串)
                    month,                         # month (YYYY-MM)
                    date_str,                      # day (YYYY-MM-DD)
                    income_order_count,            # income_order_count
                    income_payment_amount,         # income_payment_amount
                    income_estimated_service_fee,  # income_estimated_service_fee
                    income_settled_service_fee,    # income_settled_service_fee
                    expense_order_count,           # expense_order_count
                    expense_payment_amount,        # expense_payment_amount
                    expense_estimated_service_fee, # expense_estimated_service_fee
                    expense_settled_service_fee,   # expense_settled_service_fee
                    net_estimated_service_fee,     # net_estimated_service_fee
                    net_settled_service_fee        # net_settled_service_fee
                ])

        except Exception as e:
            print(f"   ❌ 商务业绩记录失败: {business_name} ({date_str}) - {str(e)}")
            logging.error(f"更新商务业绩记录失败: {str(e)}")

    def force_config_update(self):
        """强制更新配置（外部调用接口）"""
        try:
            print("🔄 强制更新同步配置...")
            self._update_sync_configs()
            print("✅ 同步配置更新完成")
        except Exception as e:
            print(f"❌ 强制更新配置失败: {str(e)}")
            logging.error(f"强制更新配置失败: {str(e)}")

    def get_sync_status(self):
        """获取同步状态"""
        connection = None
        cursor = None
        try:
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)

            # 获取达人业绩同步状态
            promoter_query = """
                SELECT last_sync_date FROM sync_config
                WHERE table_name = 'promoter_performance'
                AND (key_field IS NULL OR key_field = '')
            """
            cursor.execute(promoter_query)
            promoter_config = cursor.fetchone()

            # 获取商务业绩同步状态
            business_query = """
                SELECT COUNT(*) as total_configs,
                       MIN(last_sync_date) as earliest_date,
                       MAX(last_sync_date) as latest_date
                FROM sync_config
                WHERE table_name = 'business_performance'
                AND key_field IS NOT NULL
            """
            cursor.execute(business_query)
            business_config = cursor.fetchone()

            # 获取业绩表记录数
            promoter_count_query = "SELECT COUNT(*) as count FROM promoter_performance"
            cursor.execute(promoter_count_query)
            promoter_count = cursor.fetchone()

            business_count_query = "SELECT COUNT(*) as count FROM business_performance"
            cursor.execute(business_count_query)
            business_count = cursor.fetchone()

            return {
                'promoter_performance': {
                    'last_sync_date': promoter_config['last_sync_date'] if promoter_config else None,
                    'record_count': promoter_count['count']
                },
                'business_performance': {
                    'total_configs': business_config['total_configs'],
                    'earliest_date': business_config['earliest_date'],
                    'latest_date': business_config['latest_date'],
                    'record_count': business_count['count']
                }
            }

        except Exception as e:
            logging.error(f"获取同步状态失败: {str(e)}")
            return None
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

# 创建全局实例
performance_sync_service = PerformanceSyncService()
