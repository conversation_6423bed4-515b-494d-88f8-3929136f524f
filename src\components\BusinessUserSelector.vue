<template>
  <div class="business-user-selector">
    <!-- 单选模式 -->
    <el-select
      v-if="!multiple"
      v-model="singleValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :loading="loading"
      filterable
      clearable
      @change="handleSingleChange"
      style="width: 100%"
    >
      <el-option v-for="user in businessUsers" :key="user.id" :label="user.name" :value="user.name">
        <span style="float: left">{{ user.name }}</span>
        <span style="float: right; color: #8492a6; font-size: 13px">{{ user.role }}</span>
      </el-option>
    </el-select>

    <!-- 多选模式 -->
    <el-select
      v-else
      v-model="multipleValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :loading="loading"
      multiple
      filterable
      clearable
      :multiple-limit="multipleLimit"
      @change="handleMultipleChange"
      style="width: 100%"
    >
      <el-option v-for="user in businessUsers" :key="user.id" :label="user.name" :value="user.name">
        <span style="float: left">{{ user.name }}</span>
        <span style="float: right; color: #8492a6; font-size: 13px">{{ user.role }}</span>
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

// 定义接口
interface BusinessUser {
  id: number
  name: string
  username: string
  role: string
}

// 定义props
interface Props {
  modelValue?: string | string[]
  multiple?: boolean
  placeholder?: string
  disabled?: boolean
  multipleLimit?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  multiple: false,
  placeholder: '请选择商务',
  disabled: false,
  multipleLimit: 10,
})

// 定义emits
const emit = defineEmits<{
  'update:modelValue': [value: string | string[]]
  change: [value: string | string[]]
}>()

// 响应式数据
const businessUsers = ref<BusinessUser[]>([])
const loading = ref(false)
const singleValue = ref<string>('')
const multipleValue = ref<string[]>([])

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (props.multiple) {
      multipleValue.value = Array.isArray(newValue) ? newValue : []
    } else {
      singleValue.value = typeof newValue === 'string' ? newValue : ''
    }
  },
  { immediate: true },
)

// 获取商务用户列表
const fetchBusinessUsers = async () => {
  loading.value = true
  try {
    const response = await axios.get('/api/auth/business-users-only')
    if (response.data.code === 0) {
      businessUsers.value = response.data.data || []
    } else {
      ElMessage.error(response.data.message || '获取商务用户列表失败')
    }
  } catch (error) {
    console.error('获取商务用户列表失败:', error)
    ElMessage.error('获取商务用户列表失败')
  } finally {
    loading.value = false
  }
}

// 处理单选变化
const handleSingleChange = (value: string) => {
  emit('update:modelValue', value)
  emit('change', value)
}

// 处理多选变化
const handleMultipleChange = (value: string[]) => {
  emit('update:modelValue', value)
  emit('change', value)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchBusinessUsers()
})
</script>

<style scoped>
.business-user-selector {
  width: 100%;
}
</style>
