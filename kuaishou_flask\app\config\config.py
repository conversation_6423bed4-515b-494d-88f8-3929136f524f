import os

# 数据库配置
DB_CONFIG = {
    'pool_name': 'kuaishou_pool',
    'pool_size': 32,  # MySQL Connector/Python最大支持32个连接
    'host': 'localhost',
    'user': 'root',
    'password': '123456',
    'database': 'kuaishou',
    'port': 3306,
    'pool_reset_session': True,
    'autocommit': True,  # 自动提交，减少长时间占用连接
    'connect_timeout': 10,  # 连接超时时间
    'use_unicode': True,
    'charset': 'utf8mb4'
}

# 应用配置
DEBUG = True
SECRET_KEY = os.urandom(24) 