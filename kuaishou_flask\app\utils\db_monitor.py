"""
数据库连接池监控和管理工具
"""

import time
import threading
import logging
from datetime import datetime
from .db_utils import get_pool_stats, connection_pool
import mysql.connector
from mysql.connector import Error

class DatabaseMonitor:
    """数据库连接池监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.alert_threshold = 28  # 当活跃连接超过28时发出警告
        self.critical_threshold = 30  # 当活跃连接超过30时发出严重警告
        
    def start_monitoring(self, interval=30):
        """开始监控连接池状态"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop, 
            args=(interval,), 
            daemon=True
        )
        self.monitor_thread.start()
        logging.info("数据库连接池监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logging.info("数据库连接池监控已停止")
        
    def _monitor_loop(self, interval):
        """监控循环"""
        while self.monitoring:
            try:
                self._check_pool_health()
                time.sleep(interval)
            except Exception as e:
                logging.error(f"连接池监控出错: {e}")
                time.sleep(interval)
                
    def _check_pool_health(self):
        """检查连接池健康状态"""
        stats = get_pool_stats()
        active_connections = stats['active_connections']
        total_requests = stats['total_requests']
        failed_requests = stats['failed_requests']
        
        # 计算失败率
        failure_rate = (failed_requests / max(total_requests, 1)) * 100
        
        # 记录状态
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        status_msg = (f"[{timestamp}] 连接池状态 - "
                     f"活跃: {active_connections}/32, "
                     f"总请求: {total_requests}, "
                     f"失败: {failed_requests} ({failure_rate:.1f}%)")
        
        # 根据状态发出不同级别的警告
        if active_connections >= self.critical_threshold:
            logging.critical(f"🚨 {status_msg} - 连接池几乎耗尽!")
            print(f"🚨 {status_msg} - 连接池几乎耗尽!")
        elif active_connections >= self.alert_threshold:
            logging.warning(f"⚠️ {status_msg} - 连接池使用率过高!")
            print(f"⚠️ {status_msg} - 连接池使用率过高!")
        else:
            logging.info(status_msg)
            
        # 如果失败率过高，发出警告
        if failure_rate > 5:
            logging.warning(f"⚠️ 连接失败率过高: {failure_rate:.1f}%")
            
    def get_detailed_stats(self):
        """获取详细的连接池统计信息"""
        stats = get_pool_stats()
        
        # 尝试获取连接池的内部状态
        try:
            pool_size = connection_pool._pool_size
            used_connections = len([conn for conn in connection_pool._cnx_queue.queue if conn])
            available_connections = pool_size - stats['active_connections']
        except:
            pool_size = 32
            used_connections = "未知"
            available_connections = "未知"
            
        return {
            'pool_size': pool_size,
            'active_connections': stats['active_connections'],
            'available_connections': available_connections,
            'used_connections': used_connections,
            'total_requests': stats['total_requests'],
            'failed_requests': stats['failed_requests'],
            'failure_rate': (stats['failed_requests'] / max(stats['total_requests'], 1)) * 100
        }
        
    def force_cleanup_connections(self):
        """强制清理可能的僵死连接"""
        try:
            # 这是一个激进的方法，仅在紧急情况下使用
            logging.warning("正在强制清理连接池...")
            
            # 尝试创建新连接来测试连接池
            test_connections = []
            for i in range(5):
                try:
                    conn = connection_pool.get_connection()
                    if conn.is_connected():
                        test_connections.append(conn)
                    else:
                        conn.close()
                except:
                    break
                    
            # 关闭测试连接
            for conn in test_connections:
                try:
                    conn.close()
                except:
                    pass
                    
            logging.info("连接池清理完成")
            return True
            
        except Exception as e:
            logging.error(f"连接池清理失败: {e}")
            return False

# 全局监控器实例
db_monitor = DatabaseMonitor()

def start_db_monitoring():
    """启动数据库监控"""
    db_monitor.start_monitoring()

def stop_db_monitoring():
    """停止数据库监控"""
    db_monitor.stop_monitoring()

def get_db_stats():
    """获取数据库统计信息"""
    return db_monitor.get_detailed_stats()

def cleanup_db_connections():
    """清理数据库连接"""
    return db_monitor.force_cleanup_connections()

# 自动启动监控
start_db_monitoring()
