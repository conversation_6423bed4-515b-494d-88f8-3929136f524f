from flask import Blueprint, request, jsonify
from app.utils.db_utils import get_connection
from app.utils.config_utils import get_kuaishou_cookie
import sys
import os
import logging
from datetime import datetime, timedelta

# 导入爬虫模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../python')))
try:
    from order import fetch_order_data, get_all_orders
    logging.info("成功导入快手订单爬虫模块")
except ImportError as e:
    logging.error(f"导入快手订单爬虫模块失败: {str(e)}")
    fetch_order_data = None
    get_all_orders = None

order_bp = Blueprint('order', __name__, url_prefix='/api/order')

# 快手订单爬虫包装类
class KuaishouOrderCrawler:
    """快手订单爬虫类，用于从快手后台获取订单数据"""
    
    def __init__(self):
        # 从数据库获取cookie配置
        self.cookie = get_kuaishou_cookie()
    
    def get_orders(self, start_time, end_time, max_pages=5, **kwargs):
        """获取订单数据"""
        if get_all_orders is None:
            return {"error": "订单爬虫模块未成功导入"}
        
        try:
            orders = get_all_orders(
                start_time=start_time,
                end_time=end_time,
                cookie=self.cookie,
                max_pages=max_pages,
                **kwargs
            )
            return {"orders": orders}
        except Exception as e:
            logging.error(f"获取订单数据失败: {str(e)}")
            return {"error": f"获取订单数据失败: {str(e)}"}

# 创建爬虫实例
order_crawler = KuaishouOrderCrawler()

# 格式化日期时间为北京时间
def format_datetime(dt):
    if not dt:
        return None
    
    if isinstance(dt, str):
        try:
            dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return dt
    
    return dt.strftime('%Y-%m-%d %H:%M:%S')

@order_bp.route('/list', methods=['GET'])
def get_order_list():
    """获取订单列表"""
    connection = None
    cursor = None
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        order_id = request.args.get('orderId')
        product_id = request.args.get('productId')
        merchant_id = request.args.get('merchantId')
        status = request.args.get('status')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        fund_type = request.args.get('fundType', '1')  # 默认查询收入订单
        talent_id = request.args.get('talentId')
        talent_name = request.args.get('talentName')
        order_type = request.args.get('orderType')
        team_leader_id = request.args.get('teamLeaderId')
        promotion_type = request.args.get('promoterType')
        is_shipped = request.args.get('isShipped')
        order_channel = request.args.get('orderChannel')
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 构建查询条件
        conditions = []
        params = []

        # 根据资金类型决定是否使用表别名
        table_prefix = "o." if int(fund_type) == 1 else ""

        # 资金类型过滤（收入/支出）
        conditions.append(f"{table_prefix}fund_type = %s")
        params.append(int(fund_type))

        if order_id:
            order_id = order_id.strip()
            conditions.append(f"{table_prefix}order_id = %s")
            params.append(order_id)

        if promotion_type:
            conditions.append(f"{table_prefix}promotion_type = %s")
            params.append(promotion_type)

        if product_id:
            product_id = product_id.strip()
            conditions.append(f"{table_prefix}product_id = %s")
            params.append(product_id)

        if merchant_id:
            merchant_id = merchant_id.strip()
            conditions.append(f"{table_prefix}shop_id = %s")
            params.append(merchant_id)

        if status:
            conditions.append(f"{table_prefix}order_status = %s")
            params.append(status)

        if talent_id:
            talent_id = talent_id.strip()
            conditions.append(f"{table_prefix}promoter_id = %s")
            params.append(talent_id)

        if talent_name:
            talent_name = talent_name.strip()
            conditions.append(f"{table_prefix}promoter_name LIKE %s")
            params.append(f"%{talent_name}%")

        if team_leader_id:
            team_leader_id = team_leader_id.strip()
            conditions.append(f"{table_prefix}promotion_id = %s")
            params.append(team_leader_id)

        if order_channel:
            conditions.append(f"{table_prefix}order_channel = %s")
            params.append(order_channel)

        if is_shipped is not None:
            conditions.append(f"{table_prefix}is_shipped = %s")
            params.append(int(is_shipped))

        if start_date and end_date:
            conditions.append(f"{table_prefix}order_time BETWEEN %s AND %s")
            params.append(f"{start_date} 00:00:00")
            params.append(f"{end_date} 23:59:59")
        
        # 构建SQL语句
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        if int(fund_type) == 1:  # 收入订单使用JOIN
            count_query = f"SELECT COUNT(*) as total FROM `order` o WHERE {where_clause}"
        else:  # 支出订单不使用JOIN
            count_query = f"SELECT COUNT(*) as total FROM `order` WHERE {where_clause}"

        # 计算总数
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']

        # 分页查询
        offset = (page - 1) * page_size
        if int(fund_type) == 1:  # 收入订单才返回商务服务费率
            query = f"""
                SELECT o.*, p.investment_commission as business_service_fee_rate FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        else:  # 支出订单保持原有逻辑
            query = f"""
                SELECT o.* FROM `order` o
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        cursor.execute(query, params + [page_size, offset])
        orders = cursor.fetchall()
        
        # 处理订单状态码和日期格式
        for order in orders:
            if order['order_status'] == 30:
                order['status_text'] = '已付款'
            elif order['order_status'] == 50:
                order['status_text'] = '已收货'
            elif order['order_status'] == 60:
                order['status_text'] = '已结算'
            elif order['order_status'] == 80:
                order['status_text'] = '已失效'
            else:
                order['status_text'] = '未知'
            
            # 格式化日期时间
            if 'order_time' in order and order['order_time']:
                order['order_time'] = format_datetime(order['order_time'])
                
            # 添加资金类型描述
            order['fund_type_text'] = '收入' if order.get('fund_type', 1) == 1 else '支出'
            
            # 添加推广类型描述
            if order.get('promotion_type') == 1:
                order['promotion_type_text'] = '达人'
            elif order.get('promotion_type') == 2:
                order['promotion_type_text'] = '团长'
            else:
                order['promotion_type_text'] = '未知'
        
        # 计算业绩数据统计
        stats = {}
        
        # 构建统计查询条件
        stats_where_clause = where_clause

        # 计算订单量
        if int(fund_type) == 1:  # 收入订单使用JOIN
            stats_query = f"SELECT COUNT(*) as order_count FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        else:  # 支出订单不使用JOIN
            stats_query = f"SELECT COUNT(*) as order_count FROM `order` WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['order_count'] = cursor.fetchone()['order_count']

        # 计算支付金额
        if int(fund_type) == 1:  # 收入订单使用JOIN
            stats_query = f"SELECT SUM(o.payment_amount) as total_payment FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        else:  # 支出订单不使用JOIN
            stats_query = f"SELECT SUM(payment_amount) as total_payment FROM `order` WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['total_payment'] = float(cursor.fetchone()['total_payment'] or 0)

        if int(fund_type) == 1:  # 收入订单
            # 通过JOIN商品表获取费率，计算预估服务费 - 非已失效订单，乘以0.85
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.investment_commission / 100 * 0.85) as estimated_service_fee
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status != 80
            """
            cursor.execute(stats_query, params)
            stats['estimated_service_fee'] = float(cursor.fetchone()['estimated_service_fee'] or 0)

            # 计算结算服务费 - 已结算订单，乘以0.85
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.investment_commission / 100 * 0.85) as settled_service_fee
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status = 60
            """
            cursor.execute(stats_query, params)
            stats['settled_service_fee'] = float(cursor.fetchone()['settled_service_fee'] or 0)     
            
        else:  # 支出订单
            # 直接使用订单表中的预估服务费和结算服务费
            stats_query = f"""
                SELECT 
                    SUM(estimated_service_fee) as estimated_service_fee,
                    SUM(actual_service_fee) as settled_service_fee
                FROM `order` 
                WHERE {stats_where_clause}
            """
            cursor.execute(stats_query, params)
            result = cursor.fetchone()
            stats['estimated_service_fee'] = float(result['estimated_service_fee'] or 0)
            stats['settled_service_fee'] = float(result['settled_service_fee'] or 0)
        
        return jsonify({
            'code': 0,
            'message': '获取订单列表成功',
            'data': {
                'orders': orders,
                'total': total,
                'page': page,
                'pageSize': page_size,
                'stats': stats  # 添加业绩统计数据
            }
        })
    except Exception as e:
        logging.error(f"获取订单列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取订单列表失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@order_bp.route('/sync', methods=['POST'])
def sync_orders():
    """同步订单数据"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空',
                'data': None
            }), 400
        
        start_time = data.get('startTime')
        end_time = data.get('endTime')
        fund_type = data.get('fundType', 1)  # 默认同步收入订单
        
        if not start_time or not end_time:
            return jsonify({
                'code': 400,
                'message': '开始时间和结束时间不能为空',
                'data': None
            }), 400
            
        logging.info(f"开始同步订单数据，时间范围：{start_time} 至 {end_time}，资金类型：{fund_type}")
        
        # 获取订单数据
        result = order_crawler.get_orders(start_time, end_time, max_pages=5, fund_type=fund_type)
        
        if "error" in result:
            return jsonify({
                'code': 500,
                'message': result["error"],
                'data': None
            }), 500
        
        orders = result["orders"]
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 统计数据
        total_orders = len(orders)
        new_orders = 0
        updated_orders = 0
        
        # 处理订单数据
        for order_data in orders:
            # 从订单数据中提取需要的字段
            if not isinstance(order_data, dict):
                logging.warning(f"跳过非字典类型的订单数据: {type(order_data)}")
                continue
                
            # 获取订单ID
            if 'oid' not in order_data:
                logging.warning("订单数据中缺少oid字段")
                continue
                
            order_id = str(order_data['oid'])
            
            # 检查订单是否已存在 - 使用订单ID和资金类型的二元组判断
            check_query = "SELECT COUNT(*) as count FROM `order` WHERE order_id = %s AND fund_type = %s"
            cursor.execute(check_query, [order_id, fund_type])
            result = cursor.fetchone()
            
            # 提取商品信息
            item_list = order_data.get('itemList', [])
            product_name = ''
            product_id = ''
            product_image_url = ''
            product_price = 0
            
            if item_list and len(item_list) > 0 and isinstance(item_list[0], dict):
                product_name = item_list[0].get('itemTitle', '')
                product_id = str(item_list[0].get('itemId', ''))
                product_image_url = item_list[0].get('imageUrl', '')
                product_price = float(item_list[0].get('reservePrice', 0)) / 100
            
            # 准备订单数据
            order_status = order_data.get('orderStatus', 0)
            order_create_time = order_data.get('orderCreateTime', 0)
            order_time = datetime.fromtimestamp(int(order_create_time) / 1000)
            is_shipped = order_data.get('sendStatus', 0)
            shop_id = str(order_data.get('sellerId', ''))
            promoter_name = order_data.get('promoterNickName', '')
            promoter_id = str(order_data.get('promoterId', ''))
            payment_amount = float(order_data.get('payAmount', 0)) / 100
            activity_id = str(order_data.get('activityId', ''))
            
            # 新增字段
            promotion_type = order_data.get('promotionType', 1)
            promotion_id = str(order_data.get('promotionId', ''))
            promotion_nick_name = order_data.get('promotionNickName', '')
            second_regimental_estimate_settle_amount = float(order_data.get('secondRegimentalEstimateSettleAmount', 0)) / 100
            second_regimental_promotion_rate = (order_data.get('secondRegimentalPromotionRate', 0)) / 10
            order_channel = order_data.get('orderChannel', '')
            
            # 服务费率计算
            service_fee_rate = int(order_data.get('regimentalPromotionRate', 0)) / 10
            
            # 佣金和服务费计算
            talent_commission = '0'
            estimated_service_fee = float(order_data.get('totalRegimentalEstimateSettleAmount', 0)) / 100
            actual_service_fee = float(order_data.get('totalRegimentalSettleAmount', 0)) / 100
            
            # 商务和运营字段（待后续实现）
            talent_business = ''
            business_operation = ''
            
            if result and result['count'] > 0:
                # 更新订单
                update_query = """
                    UPDATE `order` SET 
                    product_name = %s,
                    product_id = %s,
                    product_image_url = %s,
                    product_price = %s,
                    order_status = %s,
                    order_time = %s,
                    is_shipped = %s,
                    shop_id = %s,
                    promoter_name = %s,
                    promoter_id = %s,
                    payment_amount = %s,
                    talent_commission = %s,
                    estimated_service_fee = %s,
                    actual_service_fee = %s,
                    activity_id = %s,
                    promotion_type = %s,
                    promotion_id = %s,
                    promotion_nick_name = %s,
                    second_regimental_estimate_settle_amount = %s,
                    second_regimental_promotion_rate = %s,
                    order_channel = %s,
                    fund_type = %s,
                    service_fee_rate = %s,
                    talent_business = %s,
                    business_operation = %s
                    WHERE order_id = %s
                """
                
                cursor.execute(update_query, [
                    product_name, product_id, product_image_url, product_price,
                    order_status, order_time, is_shipped, shop_id,
                    promoter_name, promoter_id, payment_amount,
                    talent_commission, estimated_service_fee, actual_service_fee,
                    activity_id, promotion_type, promotion_id, promotion_nick_name,
                    second_regimental_estimate_settle_amount, second_regimental_promotion_rate,
                    order_channel, fund_type, service_fee_rate, talent_business, business_operation,
                    order_id
                ])
                
                updated_orders += 1
            else:
                # 插入订单
                insert_query = """
                    INSERT INTO `order` (
                        order_id, product_name, product_id, product_image_url,
                        product_price, order_status, order_time, is_shipped,
                        shop_id, promoter_name, promoter_id, payment_amount,
                        talent_commission, estimated_service_fee, actual_service_fee,
                        activity_id, promotion_type, promotion_id, promotion_nick_name,
                        second_regimental_estimate_settle_amount, second_regimental_promotion_rate,
                        order_channel, fund_type, service_fee_rate, talent_business, business_operation
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(insert_query, [
                    order_id, product_name, product_id, product_image_url,
                    product_price, order_status, order_time, is_shipped,
                    shop_id, promoter_name, promoter_id, payment_amount,
                    talent_commission, estimated_service_fee, actual_service_fee,
                    activity_id, promotion_type, promotion_id, promotion_nick_name,
                    second_regimental_estimate_settle_amount, second_regimental_promotion_rate,
                    order_channel, fund_type, service_fee_rate, talent_business, business_operation
                ])
                
                new_orders += 1
        
        # 提交事务
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '同步订单数据成功',
            'data': {
                'total': total_orders,
                'new': new_orders,
                'updated': updated_orders
            }
        })
    except Exception as e:
        if connection:
            connection.rollback()
        logging.error(f"同步订单数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"同步订单数据失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@order_bp.route('/statistics', methods=['GET'])
def get_order_statistics():
    """获取订单统计数据"""
    connection = None
    cursor = None
    try:
        # 获取请求参数
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        
        if not start_date or not end_date:
            # 默认查询最近30天的数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
        logging.info(f"获取订单统计数据，时间范围：{start_date} 至 {end_date}")
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询当前时间段的统计数据
        stats_query = """
            SELECT 
                COUNT(*) as total_orders,
                SUM(payment_amount) as total_amount,
                SUM(estimated_service_fee) as estimated_service_fee,
                SUM(actual_service_fee) as settlement_service_fee,
                SUM(talent_commission) as talent_commission,
                COUNT(CASE WHEN order_status = 30 THEN 1 END) as pending_shipment,
                COUNT(CASE WHEN order_status >= 40 THEN 1 END) as shipped_orders
            FROM `order`
            WHERE order_time BETWEEN %s AND %s
        """
        
        cursor.execute(stats_query, [f"{start_date} 00:00:00", f"{end_date} 23:59:59"])
        current_stats = cursor.fetchone()
        
        # 查询上一时间段的统计数据（用于计算环比）
        date_diff = (datetime.strptime(end_date, '%Y-%m-%d') - datetime.strptime(start_date, '%Y-%m-%d')).days + 1
        prev_end_date = (datetime.strptime(start_date, '%Y-%m-%d') - timedelta(days=1)).strftime('%Y-%m-%d')
        prev_start_date = (datetime.strptime(start_date, '%Y-%m-%d') - timedelta(days=date_diff)).strftime('%Y-%m-%d')
        
        cursor.execute(stats_query, [f"{prev_start_date} 00:00:00", f"{prev_end_date} 23:59:59"])
        prev_stats = cursor.fetchone()
        
        # 计算环比变化率
        def calculate_change(current, previous):
            if not previous or previous == 0:
                return 0
            return round((current - previous) / previous * 100, 2)
        
        # 准备返回数据
        statistics = {
            'totalOrders': current_stats['total_orders'] or 0,
            'orderCompare': calculate_change(
                current_stats['total_orders'] or 0, 
                prev_stats['total_orders'] or 0
            ),
            'totalAmount': float(current_stats['total_amount'] or 0),
            'amountCompare': calculate_change(
                float(current_stats['total_amount'] or 0), 
                float(prev_stats['total_amount'] or 0)
            ),
            'estimatedServiceFee': float(current_stats['estimated_service_fee'] or 0),
            'serviceFeeCompare': calculate_change(
                float(current_stats['estimated_service_fee'] or 0), 
                float(prev_stats['estimated_service_fee'] or 0)
            ),
            'settlementServiceFee': float(current_stats['settlement_service_fee'] or 0),
            'settlementFeeCompare': calculate_change(
                float(current_stats['settlement_service_fee'] or 0), 
                float(prev_stats['settlement_service_fee'] or 0)
            ),
            'estimatedMerchantCommission': 0,  # 暂无此数据
            'merchantCommissionCompare': 0,
            'estimatedTalentCommission': float(current_stats['talent_commission'] or 0),
            'talentCommissionCompare': calculate_change(
                float(current_stats['talent_commission'] or 0), 
                float(prev_stats['talent_commission'] or 0)
            ),
            'shippedOrders': current_stats['shipped_orders'] or 0,
            'shippedCompare': calculate_change(
                current_stats['shipped_orders'] or 0, 
                prev_stats['shipped_orders'] or 0
            ),
            'pendingShipment': current_stats['pending_shipment'] or 0,
            'pendingCompare': calculate_change(
                current_stats['pending_shipment'] or 0, 
                prev_stats['pending_shipment'] or 0
            )
        }
        
        return jsonify({
            'code': 0,
            'message': '获取订单统计数据成功',
            'data': statistics
        })
    except Exception as e:
        logging.error(f"获取订单统计数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取订单统计数据失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@order_bp.route('/trend', methods=['GET'])
def get_order_trend():
    """获取订单趋势数据"""
    connection = None
    cursor = None
    try:
        # 获取请求参数
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        trend_type = request.args.get('type', 'day')  # 默认按天统计
        
        if not start_date or not end_date:
            # 默认查询最近30天的数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
        logging.info(f"获取订单趋势数据，时间范围：{start_date} 至 {end_date}，类型：{trend_type}")
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 根据趋势类型选择不同的分组方式
        if trend_type == 'month':
            date_format = '%Y-%m'
            date_sql = "DATE_FORMAT(order_time, '%Y-%m')"
        elif trend_type == 'week':
            date_format = '%Y-%u'  # ISO周格式 (年-周数)
            date_sql = "DATE_FORMAT(order_time, '%Y-%u')"
        else:  # day
            date_format = '%Y-%m-%d'
            date_sql = "DATE(order_time)"
        
        # 查询趋势数据
        trend_query = f"""
            SELECT 
                {date_sql} as date,
                COUNT(*) as order_count,
                SUM(payment_amount) as amount,
                SUM(estimated_service_fee) as service_fee,
                SUM(actual_service_fee) as settlement_fee,
                SUM(talent_commission) as talent_commission,
                COUNT(CASE WHEN order_status >= 40 THEN 1 END) as shipped_count,
                COUNT(CASE WHEN order_status = 30 THEN 1 END) as pending_count
            FROM `order`
            WHERE order_time BETWEEN %s AND %s
            GROUP BY {date_sql}
            ORDER BY {date_sql}
        """
        
        cursor.execute(trend_query, [f"{start_date} 00:00:00", f"{end_date} 23:59:59"])
        trend_data = cursor.fetchall()
        
        # 格式化数据
        result = []
        for item in trend_data:
            result.append({
                'date': item['date'],
                'orderCount': item['order_count'],
                'amount': float(item['amount'] or 0),
                'serviceFee': float(item['service_fee'] or 0),
                'settlementFee': float(item['settlement_fee'] or 0),
                'merchantCommission': 0,  # 暂无此数据
                'talentCommission': float(item['talent_commission'] or 0),
                'shippedCount': item['shipped_count'],
                'pendingCount': item['pending_count']
            })
        
        return jsonify({
            'code': 0,
            'message': '获取订单趋势数据成功',
            'data': {
                'trend': result,
                'total': len(result)
            }
        })
    except Exception as e:
        logging.error(f"获取订单趋势数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取订单趋势数据失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@order_bp.route('/business-orders', methods=['GET'])
def get_business_orders():
    """获取商务用户的订单列表（根据talent_business字段过滤）"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            import jwt
            SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            business_name = payload.get('name', '')
            
            if not business_name:
                return jsonify({
                    'code': 400,
                    'message': '无法获取商务名称',
                    'data': None
                }), 400
                
        except Exception as e:
            return jsonify({
                'code': 401,
                'message': f'令牌验证失败: {str(e)}',
                'data': None
            }), 401
        
        # 获取请求参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        order_id = request.args.get('orderId')
        product_id = request.args.get('productId')
        merchant_id = request.args.get('merchantId')
        status = request.args.get('status')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        fund_type = request.args.get('fundType', '1')  # 默认查询收入订单
        talent_id = request.args.get('talentId')
        talent_name = request.args.get('talentName')
        order_type = request.args.get('orderType')
        team_leader_id = request.args.get('teamLeaderId')
        promoter_type = request.args.get('promoterType')
        is_shipped = request.args.get('isShipped')
        order_channel = request.args.get('orderChannel')
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 构建查询条件
        conditions = []
        params = []

        # 根据资金类型决定是否使用表别名
        table_prefix = "o." if int(fund_type) == 1 else ""

        # 资金类型过滤（收入/支出）
        conditions.append(f"{table_prefix}fund_type = %s")
        params.append(int(fund_type))

        # 根据商务名称过滤
        conditions.append(f"{table_prefix}talent_business = %s")
        params.append(business_name)

        if order_id:
            order_id = order_id.strip()
            conditions.append(f"{table_prefix}order_id = %s")
            params.append(order_id)

        if product_id:
            product_id = product_id.strip()
            conditions.append(f"{table_prefix}product_id = %s")
            params.append(product_id)

        if merchant_id:
            merchant_id = merchant_id.strip()
            conditions.append(f"{table_prefix}shop_id = %s")
            params.append(merchant_id)

        if status:
            conditions.append(f"{table_prefix}order_status = %s")
            params.append(status)

        if talent_id:
            talent_id = talent_id.strip()
            conditions.append(f"{table_prefix}promoter_id = %s")
            params.append(talent_id)

        if talent_name:
            talent_name = talent_name.strip()
            conditions.append(f"{table_prefix}promoter_name LIKE %s")
            params.append(f"%{talent_name}%")

        if team_leader_id:
            team_leader_id = team_leader_id.strip()
            conditions.append(f"{table_prefix}promotion_id = %s")
            params.append(team_leader_id)

        if order_channel:
            conditions.append(f"{table_prefix}order_channel = %s")
            params.append(order_channel)

        if is_shipped is not None:
            conditions.append(f"{table_prefix}is_shipped = %s")
            params.append(int(is_shipped))

        if start_date and end_date:
            conditions.append(f"{table_prefix}order_time BETWEEN %s AND %s")
            params.append(f"{start_date} 00:00:00")
            params.append(f"{end_date} 23:59:59")
        
        # 构建SQL语句
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        if int(fund_type) == 1:  # 收入订单使用JOIN
            count_query = f"SELECT COUNT(*) as total FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {where_clause}"
        else:  # 支出订单不使用JOIN
            count_query = f"SELECT COUNT(*) as total FROM `order` WHERE {where_clause}"

        # 计算总数
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']

        # 分页查询
        offset = (page - 1) * page_size
        if int(fund_type) == 1:  # 收入订单才返回商务服务费率
            query = f"""
                SELECT o.*, p.investment_commission as business_service_fee_rate FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        else:  # 支出订单保持原有逻辑
            query = f"""
                SELECT o.* FROM `order` o
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        cursor.execute(query, params + [page_size, offset])
        orders = cursor.fetchall()
        
        # 处理订单状态码和日期格式
        for order in orders:
            if order['order_status'] == 30:
                order['status_text'] = '已付款'
            elif order['order_status'] == 50:
                order['status_text'] = '已收货'
            elif order['order_status'] == 60:
                order['status_text'] = '已结算'
            elif order['order_status'] == 80:
                order['status_text'] = '已失效'
            else:
                order['status_text'] = '未知'
            
            # 格式化日期时间
            if 'order_time' in order and order['order_time']:
                order['order_time'] = format_datetime(order['order_time'])
                
            # 添加资金类型描述
            order['fund_type_text'] = '收入' if order.get('fund_type', 1) == 1 else '支出'
            
            # 添加推广类型描述
            if order.get('promotion_type') == 1:
                order['promotion_type_text'] = '达人'
            elif order.get('promotion_type') == 2:
                order['promotion_type_text'] = '团长'
            else:
                order['promotion_type_text'] = '未知'
        
        # 计算业绩数据统计
        stats = {}
        
        # 构建统计查询条件
        stats_where_clause = where_clause

        # 计算订单量
        if int(fund_type) == 1:  # 收入订单使用JOIN
            stats_query = f"SELECT COUNT(*) as order_count FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        else:  # 支出订单不使用JOIN
            stats_query = f"SELECT COUNT(*) as order_count FROM `order` WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['order_count'] = cursor.fetchone()['order_count']

        # 计算支付金额
        if int(fund_type) == 1:  # 收入订单使用JOIN
            stats_query = f"SELECT SUM(o.payment_amount) as total_payment FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        else:  # 支出订单不使用JOIN
            stats_query = f"SELECT SUM(payment_amount) as total_payment FROM `order` WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['total_payment'] = float(cursor.fetchone()['total_payment'] or 0)

        if int(fund_type) == 1:  # 收入订单
            # 通过JOIN商品表获取费率，计算预估服务费 - 非已失效订单，乘以0.85
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.investment_commission / 100 * 0.85) as estimated_service_fee
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status != 80
            """
            cursor.execute(stats_query, params)
            stats['estimated_service_fee'] = float(cursor.fetchone()['estimated_service_fee'] or 0)

            # 计算结算服务费 - 已结算订单，乘以0.85
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.investment_commission / 100 * 0.85) as settled_service_fee
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status = 60
            """
            cursor.execute(stats_query, params)
            stats['settled_service_fee'] = float(cursor.fetchone()['settled_service_fee'] or 0)
            
        else:  # 支出订单
            # 直接使用订单表中的预估服务费和结算服务费
            stats_query = f"""
                SELECT 
                    SUM(estimated_service_fee) as estimated_service_fee,
                    SUM(actual_service_fee) as settled_service_fee
                FROM `order` 
                WHERE {stats_where_clause}
            """
            cursor.execute(stats_query, params)
            result = cursor.fetchone()
            stats['estimated_service_fee'] = float(result['estimated_service_fee'] or 0)
            stats['settled_service_fee'] = float(result['settled_service_fee'] or 0)
        
        return jsonify({
            'code': 0,
            'message': '获取商务订单列表成功',
            'data': {
                'orders': orders,
                'total': total,
                'page': page,
                'pageSize': page_size,
                'stats': stats  # 添加业绩统计数据
            }
        })
    except Exception as e:
        logging.error(f"获取商务订单列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取商务订单列表失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 


# 商家返佣订单接口
@order_bp.route('/merchant-commission-orders', methods=['GET'])
def get_merchant_commission_orders():
    """获取商家返佣订单列表"""
    connection = None
    cursor = None
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        order_id = request.args.get('orderId')
        product_id = request.args.get('productId')
        merchant_id = request.args.get('merchantId')
        status = request.args.get('status')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        fund_type = request.args.get('fundType', '1')  # 默认查询收入订单
        talent_id = request.args.get('talentId')
        talent_name = request.args.get('talentName')
        team_leader_id = request.args.get('teamLeaderId')
        is_shipped = request.args.get('isShipped')
        order_channel = request.args.get('orderChannel')
        promotion_type = request.args.get('promoterType')
        business_name = request.args.get('businessName')  # 商务参数

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建查询条件
        conditions = []
        params = []

        # 根据资金类型决定是否使用表别名
        table_prefix = "o." if int(fund_type) == 1 else ""

        # 资金类型过滤（收入/支出）
        conditions.append(f"{table_prefix}fund_type = %s")
        params.append(int(fund_type))

        # 商家返佣订单过滤：merchant_commission_rate > 0
        if int(fund_type) == 1:
            conditions.append("p.merchant_commission_rate > 0")
        else:
            # 支出订单也需要过滤商家返佣订单，但需要子查询
            conditions.append("o.product_id IN (SELECT product_id FROM product WHERE merchant_commission_rate > 0)")

        # 商务参数过滤：如果提供了商务名称，只查看该商务的订单
        if business_name:
            conditions.append(f"{table_prefix}talent_business = %s")
            params.append(business_name)

        if order_id:
            order_id = order_id.strip()
            conditions.append(f"{table_prefix}order_id = %s")
            params.append(order_id)

        if promotion_type:
            conditions.append(f"{table_prefix}promotion_type = %s")
            params.append(promotion_type)

        if product_id:
            product_id = product_id.strip()
            conditions.append(f"{table_prefix}product_id = %s")
            params.append(product_id)

        if merchant_id:
            merchant_id = merchant_id.strip()
            conditions.append(f"{table_prefix}shop_id = %s")
            params.append(merchant_id)

        if status:
            conditions.append(f"{table_prefix}order_status = %s")
            params.append(status)

        if talent_id:
            talent_id = talent_id.strip()
            conditions.append(f"{table_prefix}promoter_id = %s")
            params.append(talent_id)

        if talent_name:
            talent_name = talent_name.strip()
            conditions.append(f"{table_prefix}promoter_name LIKE %s")
            params.append(f"%{talent_name}%")

        if team_leader_id:
            team_leader_id = team_leader_id.strip()
            conditions.append(f"{table_prefix}promotion_id = %s")
            params.append(team_leader_id)

        if order_channel:
            conditions.append(f"{table_prefix}order_channel = %s")
            params.append(order_channel)

        if is_shipped is not None:
            conditions.append(f"{table_prefix}is_shipped = %s")
            params.append(int(is_shipped))

        if start_date and end_date:
            conditions.append(f"{table_prefix}order_time BETWEEN %s AND %s")
            params.append(f"{start_date} 00:00:00")
            params.append(f"{end_date} 23:59:59")

        # 构建SQL语句
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        if int(fund_type) == 1:  # 收入订单使用JOIN
            count_query = f"SELECT COUNT(*) as total FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {where_clause}"
        else:  # 支出订单不使用JOIN，但需要别名以匹配WHERE条件
            count_query = f"SELECT COUNT(*) as total FROM `order` o WHERE {where_clause}"

        # 计算总数
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']

        # 分页查询
        offset = (page - 1) * page_size
        if int(fund_type) == 1:  # 收入订单返回商家返佣费率
            query = f"""
                SELECT o.*, p.merchant_commission_rate FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        else:  # 支出订单保持原有逻辑
            query = f"""
                SELECT o.* FROM `order` o
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        cursor.execute(query, params + [page_size, offset])
        orders = cursor.fetchall()

        # 处理订单状态码和日期格式
        for order in orders:
            if order['order_status'] == 30:
                order['status_text'] = '已付款'
            elif order['order_status'] == 50:
                order['status_text'] = '已收货'
            elif order['order_status'] == 60:
                order['status_text'] = '已结算'
            elif order['order_status'] == 80:
                order['status_text'] = '已失效'
            else:
                order['status_text'] = '未知'

            # 格式化日期时间
            if 'order_time' in order and order['order_time']:
                order['order_time'] = format_datetime(order['order_time'])

            # 添加资金类型描述
            order['fund_type_text'] = '收入' if order.get('fund_type', 1) == 1 else '支出'

            # 添加推广类型描述
            if order.get('promotion_type') == 1:
                order['promotion_type_text'] = '达人'
            elif order.get('promotion_type') == 2:
                order['promotion_type_text'] = '团长'
            else:
                order['promotion_type_text'] = '未知'

            # 为收入订单计算商家返佣相关字段
            if int(fund_type) == 1 and order.get('merchant_commission_rate'):
                payment_amount = float(order.get('payment_amount', 0))
                commission_rate = float(order.get('merchant_commission_rate', 0))

                # 计算预估商家返佣（非已失效订单）
                if order.get('order_status') != 80:
                    order['estimated_merchant_commission'] = payment_amount * commission_rate / 100
                else:
                    order['estimated_merchant_commission'] = 0

                # 计算结算商家返佣（已结算订单）
                if order.get('order_status') == 60:
                    order['settled_merchant_commission'] = payment_amount * commission_rate / 100
                else:
                    order['settled_merchant_commission'] = 0

        # 计算业绩数据统计
        stats = {}

        # 构建统计查询条件
        stats_where_clause = where_clause

        # 计算订单量
        stats_query = f"SELECT COUNT(*) as order_count FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['order_count'] = cursor.fetchone()['order_count']

        # 计算支付金额
        stats_query = f"SELECT SUM(o.payment_amount) as total_payment FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['total_payment'] = float(cursor.fetchone()['total_payment'] or 0)

        if int(fund_type) == 1:  # 收入订单
            # 计算预估商家返佣 - 非已失效订单
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.merchant_commission_rate / 100 * 0.85) as estimated_merchant_commission
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status != 80
            """
            cursor.execute(stats_query, params)
            stats['estimated_merchant_commission'] = float(cursor.fetchone()['estimated_merchant_commission'] or 0)

            # 计算结算商家返佣 - 已结算订单
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.merchant_commission_rate / 100 * 0.85) as settled_merchant_commission
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status = 60
            """
            cursor.execute(stats_query, params)
            stats['settled_merchant_commission'] = float(cursor.fetchone()['settled_merchant_commission'] or 0)

        return jsonify({
            'code': 0,
            'message': '获取商家返佣订单列表成功',
            'data': {
                'orders': orders,
                'total': total,
                'page': page,
                'pageSize': page_size,
                'stats': stats
            }
        })
    except Exception as e:
        logging.error(f"获取商家返佣订单列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取商家返佣订单列表失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 预留服务费订单接口
@order_bp.route('/reserved-service-fee-orders', methods=['GET'])
def get_reserved_service_fee_orders():
    """获取预留服务费订单列表"""
    connection = None
    cursor = None
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        order_id = request.args.get('orderId')
        product_id = request.args.get('productId')
        merchant_id = request.args.get('merchantId')
        status = request.args.get('status')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        fund_type = request.args.get('fundType', '1')  # 默认查询收入订单
        talent_id = request.args.get('talentId')
        talent_name = request.args.get('talentName')
        team_leader_id = request.args.get('teamLeaderId')
        is_shipped = request.args.get('isShipped')
        order_channel = request.args.get('orderChannel')
        promotion_type = request.args.get('promoterType')
        business_name = request.args.get('businessName')  # 商务参数

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建查询条件
        conditions = []
        params = []

        # 根据资金类型决定是否使用表别名
        table_prefix = "o." if int(fund_type) == 1 else ""

        # 资金类型过滤（收入/支出）
        conditions.append(f"{table_prefix}fund_type = %s")
        params.append(int(fund_type))

        # 预留服务费订单过滤：reserved_service_fee_rate > 0
        if int(fund_type) == 1:
            conditions.append("p.reserved_service_fee_rate > 0")
        else:
            # 支出订单也需要过滤预留服务费订单，但需要子查询
            conditions.append("o.product_id IN (SELECT product_id FROM product WHERE reserved_service_fee_rate > 0)")

        # 商务参数过滤：如果提供了商务名称，只查看该商务的订单
        if business_name:
            conditions.append(f"{table_prefix}talent_business = %s")
            params.append(business_name)

        if order_id:
            order_id = order_id.strip()
            conditions.append(f"{table_prefix}order_id = %s")
            params.append(order_id)

        if promotion_type:
            conditions.append(f"{table_prefix}promotion_type = %s")
            params.append(promotion_type)

        if product_id:
            product_id = product_id.strip()
            conditions.append(f"{table_prefix}product_id = %s")
            params.append(product_id)

        if merchant_id:
            merchant_id = merchant_id.strip()
            conditions.append(f"{table_prefix}shop_id = %s")
            params.append(merchant_id)

        if status:
            conditions.append(f"{table_prefix}order_status = %s")
            params.append(status)

        if talent_id:
            talent_id = talent_id.strip()
            conditions.append(f"{table_prefix}promoter_id = %s")
            params.append(talent_id)

        if talent_name:
            talent_name = talent_name.strip()
            conditions.append(f"{table_prefix}promoter_name LIKE %s")
            params.append(f"%{talent_name}%")

        if team_leader_id:
            team_leader_id = team_leader_id.strip()
            conditions.append(f"{table_prefix}promotion_id = %s")
            params.append(team_leader_id)

        if order_channel:
            conditions.append(f"{table_prefix}order_channel = %s")
            params.append(order_channel)

        if is_shipped is not None:
            conditions.append(f"{table_prefix}is_shipped = %s")
            params.append(int(is_shipped))

        if start_date and end_date:
            conditions.append(f"{table_prefix}order_time BETWEEN %s AND %s")
            params.append(f"{start_date} 00:00:00")
            params.append(f"{end_date} 23:59:59")

        # 构建SQL语句
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        if int(fund_type) == 1:  # 收入订单使用JOIN
            count_query = f"SELECT COUNT(*) as total FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {where_clause}"
        else:  # 支出订单不使用JOIN，但需要别名以匹配WHERE条件
            count_query = f"SELECT COUNT(*) as total FROM `order` o WHERE {where_clause}"

        # 计算总数
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']

        # 分页查询
        offset = (page - 1) * page_size
        if int(fund_type) == 1:  # 收入订单返回预留服务费率
            query = f"""
                SELECT o.*, p.reserved_service_fee_rate FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        else:  # 支出订单保持原有逻辑
            query = f"""
                SELECT o.* FROM `order` o
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        cursor.execute(query, params + [page_size, offset])
        orders = cursor.fetchall()

        # 处理订单状态码和日期格式
        for order in orders:
            if order['order_status'] == 30:
                order['status_text'] = '已付款'
            elif order['order_status'] == 50:
                order['status_text'] = '已收货'
            elif order['order_status'] == 60:
                order['status_text'] = '已结算'
            elif order['order_status'] == 80:
                order['status_text'] = '已失效'
            else:
                order['status_text'] = '未知'

            # 格式化日期时间
            if 'order_time' in order and order['order_time']:
                order['order_time'] = format_datetime(order['order_time'])

            # 添加资金类型描述
            order['fund_type_text'] = '收入' if order.get('fund_type', 1) == 1 else '支出'

            # 添加推广类型描述
            if order.get('promotion_type') == 1:
                order['promotion_type_text'] = '达人'
            elif order.get('promotion_type') == 2:
                order['promotion_type_text'] = '团长'
            else:
                order['promotion_type_text'] = '未知'

            # 为收入订单计算预留服务费相关字段
            if int(fund_type) == 1 and order.get('reserved_service_fee_rate'):
                payment_amount = float(order.get('payment_amount', 0))
                fee_rate = float(order.get('reserved_service_fee_rate', 0))

                # 计算预估预留服务费（非已失效订单）
                if order.get('order_status') != 80:
                    order['estimated_reserved_service_fee'] = payment_amount * fee_rate / 100
                else:
                    order['estimated_reserved_service_fee'] = 0

                # 计算结算预留服务费（已结算订单）
                if order.get('order_status') == 60:
                    order['settled_reserved_service_fee'] = payment_amount * fee_rate / 100
                else:
                    order['settled_reserved_service_fee'] = 0

        # 计算业绩数据统计
        stats = {}

        # 构建统计查询条件
        stats_where_clause = where_clause

        # 计算订单量
        stats_query = f"SELECT COUNT(*) as order_count FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['order_count'] = cursor.fetchone()['order_count']

        # 计算支付金额
        stats_query = f"SELECT SUM(o.payment_amount) as total_payment FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['total_payment'] = float(cursor.fetchone()['total_payment'] or 0)

        if int(fund_type) == 1:  # 收入订单
            # 计算预估预留服务费 - 非已失效订单
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.reserved_service_fee_rate / 100 * 0.85) as estimated_reserved_fee
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status != 80
            """
            cursor.execute(stats_query, params)
            stats['estimated_reserved_fee'] = float(cursor.fetchone()['estimated_reserved_fee'] or 0)

            # 计算结算预留服务费 - 已结算订单
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.reserved_service_fee_rate / 100 * 0.85) as settled_reserved_fee
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status = 60
            """
            cursor.execute(stats_query, params)
            stats['settled_reserved_fee'] = float(cursor.fetchone()['settled_reserved_fee'] or 0)

        return jsonify({
            'code': 0,
            'message': '获取预留服务费订单列表成功',
            'data': {
                'orders': orders,
                'total': total,
                'page': page,
                'pageSize': page_size,
                'stats': stats
            }
        })
    except Exception as e:
        logging.error(f"获取预留服务费订单列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取预留服务费订单列表失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 团长返佣订单接口
@order_bp.route('/team-leader-commission-orders', methods=['GET'])
def get_team_leader_commission_orders():
    """获取团长返佣订单列表"""
    connection = None
    cursor = None
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        order_id = request.args.get('orderId')
        product_id = request.args.get('productId')
        merchant_id = request.args.get('merchantId')
        status = request.args.get('status')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        fund_type = request.args.get('fundType', '1')  # 默认查询收入订单
        talent_id = request.args.get('talentId')
        talent_name = request.args.get('talentName')
        team_leader_id = request.args.get('teamLeaderId')
        is_shipped = request.args.get('isShipped')
        order_channel = request.args.get('orderChannel')
        promotion_type = request.args.get('promoterType')
        business_name = request.args.get('businessName')  # 商务参数

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建查询条件
        conditions = []
        params = []

        # 根据资金类型决定是否使用表别名
        table_prefix = "o." if int(fund_type) == 1 else ""

        # 资金类型过滤（收入/支出）
        conditions.append(f"{table_prefix}fund_type = %s")
        params.append(int(fund_type))

        # 团长返佣订单过滤：team_leader_commission_rate > 0
        if int(fund_type) == 1:
            conditions.append("p.team_leader_commission_rate > 0")
        else:
            # 支出订单也需要过滤团长返佣订单，但需要子查询
            conditions.append("o.product_id IN (SELECT product_id FROM product WHERE team_leader_commission_rate > 0)")

        # 商务参数过滤：如果提供了商务名称，只查看该商务的订单
        if business_name:
            conditions.append(f"{table_prefix}talent_business = %s")
            params.append(business_name)

        if order_id:
            order_id = order_id.strip()
            conditions.append(f"{table_prefix}order_id = %s")
            params.append(order_id)

        if promotion_type:
            conditions.append(f"{table_prefix}promotion_type = %s")
            params.append(promotion_type)

        if product_id:
            product_id = product_id.strip()
            conditions.append(f"{table_prefix}product_id = %s")
            params.append(product_id)

        if merchant_id:
            merchant_id = merchant_id.strip()
            conditions.append(f"{table_prefix}shop_id = %s")
            params.append(merchant_id)

        if status:
            conditions.append(f"{table_prefix}order_status = %s")
            params.append(status)

        if talent_id:
            talent_id = talent_id.strip()
            conditions.append(f"{table_prefix}promoter_id = %s")
            params.append(talent_id)

        if talent_name:
            talent_name = talent_name.strip()
            conditions.append(f"{table_prefix}promoter_name LIKE %s")
            params.append(f"%{talent_name}%")

        if team_leader_id:
            team_leader_id = team_leader_id.strip()
            conditions.append(f"{table_prefix}promotion_id = %s")
            params.append(team_leader_id)

        if order_channel:
            conditions.append(f"{table_prefix}order_channel = %s")
            params.append(order_channel)

        if is_shipped is not None:
            conditions.append(f"{table_prefix}is_shipped = %s")
            params.append(int(is_shipped))

        if start_date and end_date:
            conditions.append(f"{table_prefix}order_time BETWEEN %s AND %s")
            params.append(f"{start_date} 00:00:00")
            params.append(f"{end_date} 23:59:59")

        # 构建SQL语句
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        if int(fund_type) == 1:  # 收入订单使用JOIN
            count_query = f"SELECT COUNT(*) as total FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {where_clause}"
        else:  # 支出订单不使用JOIN，但需要别名以匹配WHERE条件
            count_query = f"SELECT COUNT(*) as total FROM `order` o WHERE {where_clause}"

        # 计算总数
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']

        # 分页查询
        offset = (page - 1) * page_size
        if int(fund_type) == 1:  # 收入订单返回团长返佣费率
            query = f"""
                SELECT o.*, p.team_leader_commission_rate FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        else:  # 支出订单保持原有逻辑
            query = f"""
                SELECT o.* FROM `order` o
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        cursor.execute(query, params + [page_size, offset])
        orders = cursor.fetchall()

        # 处理订单状态码和日期格式
        for order in orders:
            if order['order_status'] == 30:
                order['status_text'] = '已付款'
            elif order['order_status'] == 50:
                order['status_text'] = '已收货'
            elif order['order_status'] == 60:
                order['status_text'] = '已结算'
            elif order['order_status'] == 80:
                order['status_text'] = '已失效'
            else:
                order['status_text'] = '未知'

            # 格式化日期时间
            if 'order_time' in order and order['order_time']:
                order['order_time'] = format_datetime(order['order_time'])

            # 添加资金类型描述
            order['fund_type_text'] = '收入' if order.get('fund_type', 1) == 1 else '支出'

            # 添加推广类型描述
            if order.get('promotion_type') == 1:
                order['promotion_type_text'] = '达人'
            elif order.get('promotion_type') == 2:
                order['promotion_type_text'] = '团长'
            else:
                order['promotion_type_text'] = '未知'

            # 为收入订单计算团长返佣相关字段
            if int(fund_type) == 1 and order.get('team_leader_commission_rate'):
                payment_amount = float(order.get('payment_amount', 0))
                commission_rate = float(order.get('team_leader_commission_rate', 0))

                # 计算预估团长返佣（非已失效订单）
                if order.get('order_status') != 80:
                    order['estimated_team_leader_commission'] = payment_amount * commission_rate / 100
                else:
                    order['estimated_team_leader_commission'] = 0

                # 计算结算团长返佣（已结算订单）
                if order.get('order_status') == 60:
                    order['settled_team_leader_commission'] = payment_amount * commission_rate / 100
                else:
                    order['settled_team_leader_commission'] = 0

        # 计算业绩数据统计
        stats = {}

        # 构建统计查询条件
        stats_where_clause = where_clause

        # 计算订单量
        stats_query = f"SELECT COUNT(*) as order_count FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['order_count'] = cursor.fetchone()['order_count']

        # 计算支付金额
        stats_query = f"SELECT SUM(o.payment_amount) as total_payment FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['total_payment'] = float(cursor.fetchone()['total_payment'] or 0)

        if int(fund_type) == 1:  # 收入订单
            # 计算预估团长返佣 - 非已失效订单
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.team_leader_commission_rate / 100 * 0.85) as estimated_team_leader_commission
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status != 80
            """
            cursor.execute(stats_query, params)
            stats['estimated_team_leader_commission'] = float(cursor.fetchone()['estimated_team_leader_commission'] or 0)

            # 计算结算团长返佣 - 已结算订单
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.team_leader_commission_rate / 100  * 0.85) as settled_team_leader_commission
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status = 60
            """
            cursor.execute(stats_query, params)
            stats['settled_team_leader_commission'] = float(cursor.fetchone()['settled_team_leader_commission'] or 0)

        return jsonify({
            'code': 0,
            'message': '获取团长返佣订单列表成功',
            'data': {
                'orders': orders,
                'total': total,
                'page': page,
                'pageSize': page_size,
                'stats': stats
            }
        })
    except Exception as e:
        logging.error(f"获取团长返佣订单列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取团长返佣订单列表失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 获取商务用户列表（用于订单编辑）
@order_bp.route('/business-users', methods=['GET'])
def get_business_users_for_order():
    """获取商务用户列表，用于订单编辑"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 查询角色为商务的用户
        query = """
            SELECT id, name, username
            FROM business_user
            WHERE role = 'business'
            ORDER BY name
        """
        cursor.execute(query)
        business_users = cursor.fetchall()

        return jsonify({
            'code': 0,
            'message': '获取商务用户列表成功',
            'data': business_users
        })
    except Exception as e:
        print(f"获取商务用户列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取商务用户列表失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 批量编辑订单商务
@order_bp.route('/batch-edit-business', methods=['POST'])
def batch_edit_order_business():
    """批量编辑订单的对接商务"""
    connection = None
    cursor = None
    try:
        data = request.get_json()
        order_ids = data.get('orderIds', [])
        business_name = data.get('businessName', '')

        if not order_ids:
            return jsonify({
                'code': 400,
                'message': '请选择要编辑的订单',
                'data': None
            }), 400

        if not business_name:
            return jsonify({
                'code': 400,
                'message': '请选择商务人员',
                'data': None
            }), 400

        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建批量更新SQL
        placeholders = ', '.join(['%s'] * len(order_ids))
        update_query = f"""
            UPDATE `order`
            SET talent_business = %s
            WHERE order_id IN ({placeholders})
        """

        # 执行更新
        params = [business_name] + order_ids
        cursor.execute(update_query, params)
        affected_rows = cursor.rowcount

        connection.commit()

        return jsonify({
            'code': 0,
            'message': f'成功更新 {affected_rows} 条订单的商务信息',
            'data': {
                'affected_rows': affected_rows
            }
        })
    except Exception as e:
        print(f"批量编辑订单商务失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'批量编辑订单商务失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


# 违规承担订单接口
@order_bp.route('/violation-fee-orders', methods=['GET'])
def get_violation_fee_orders():
    """获取违规承担订单列表"""
    connection = None
    cursor = None
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        order_id = request.args.get('orderId')
        product_id = request.args.get('productId')
        merchant_id = request.args.get('merchantId')
        status = request.args.get('status')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        fund_type = request.args.get('fundType', '1')  # 默认查询收入订单
        talent_id = request.args.get('talentId')
        talent_name = request.args.get('talentName')
        team_leader_id = request.args.get('teamLeaderId')
        is_shipped = request.args.get('isShipped')
        order_channel = request.args.get('orderChannel')
        promotion_type = request.args.get('promoterType')
        business_name = request.args.get('businessName')  # 商务参数

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 构建查询条件
        conditions = []
        params = []

        # 根据资金类型决定是否使用表别名
        table_prefix = "o." if int(fund_type) == 1 else ""

        # 资金类型过滤（收入/支出）
        conditions.append(f"{table_prefix}fund_type = %s")
        params.append(int(fund_type))

        # 违规承担订单过滤：violation_fee_rate > 0
        if int(fund_type) == 1:
            conditions.append("p.violation_fee_rate > 0")
        else:
            # 支出订单也需要过滤违规承担订单，但需要子查询
            conditions.append("o.product_id IN (SELECT product_id FROM product WHERE violation_fee_rate > 0)")

        # 商务参数过滤：如果提供了商务名称，只查看该商务的订单
        if business_name:
            conditions.append(f"{table_prefix}talent_business = %s")
            params.append(business_name)

        if order_id:
            order_id = order_id.strip()
            conditions.append(f"{table_prefix}order_id = %s")
            params.append(order_id)

        if promotion_type:
            conditions.append(f"{table_prefix}promotion_type = %s")
            params.append(promotion_type)

        if product_id:
            product_id = product_id.strip()
            conditions.append(f"{table_prefix}product_id = %s")
            params.append(product_id)

        if merchant_id:
            merchant_id = merchant_id.strip()
            conditions.append(f"{table_prefix}shop_id = %s")
            params.append(merchant_id)

        if status:
            conditions.append(f"{table_prefix}order_status = %s")
            params.append(status)

        if talent_id:
            talent_id = talent_id.strip()
            conditions.append(f"{table_prefix}promoter_id = %s")
            params.append(talent_id)

        if talent_name:
            talent_name = talent_name.strip()
            conditions.append(f"{table_prefix}promoter_name LIKE %s")
            params.append(f"%{talent_name}%")

        if team_leader_id:
            team_leader_id = team_leader_id.strip()
            conditions.append(f"{table_prefix}promotion_id = %s")
            params.append(team_leader_id)

        if order_channel:
            conditions.append(f"{table_prefix}order_channel = %s")
            params.append(order_channel)

        if is_shipped is not None and is_shipped != '':
            conditions.append(f"{table_prefix}is_shipped = %s")
            params.append(int(is_shipped))

        if start_date and end_date:
            conditions.append(f"{table_prefix}order_time BETWEEN %s AND %s")
            params.append(f"{start_date} 00:00:00")
            params.append(f"{end_date} 23:59:59")

        # 构建SQL语句
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        if int(fund_type) == 1:  # 收入订单使用JOIN
            count_query = f"SELECT COUNT(*) as total FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {where_clause}"
        else:  # 支出订单不使用JOIN，但需要别名以匹配WHERE条件
            count_query = f"SELECT COUNT(*) as total FROM `order` o WHERE {where_clause}"

        # 计算总数
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']

        # 分页查询
        offset = (page - 1) * page_size
        if int(fund_type) == 1:  # 收入订单返回违规承担费率
            query = f"""
                SELECT o.*, p.violation_fee_rate FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        else:  # 支出订单保持原有逻辑
            query = f"""
                SELECT o.* FROM `order` o
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT %s OFFSET %s
            """
        cursor.execute(query, params + [page_size, offset])
        orders = cursor.fetchall()

        # 处理订单状态码和日期格式
        for order in orders:
            if order['order_status'] == 30:
                order['status_text'] = '已付款'
            elif order['order_status'] == 50:
                order['status_text'] = '已收货'
            elif order['order_status'] == 60:
                order['status_text'] = '已结算'
            elif order['order_status'] == 80:
                order['status_text'] = '已失效'
            else:
                order['status_text'] = '未知'

            # 格式化日期时间
            if 'order_time' in order and order['order_time']:
                order['order_time'] = format_datetime(order['order_time'])

            # 添加资金类型描述
            order['fund_type_text'] = '收入' if order.get('fund_type', 1) == 1 else '支出'

            # 添加推广类型描述
            if order.get('promotion_type') == 1:
                order['promotion_type_text'] = '达人'
            elif order.get('promotion_type') == 2:
                order['promotion_type_text'] = '团长'
            else:
                order['promotion_type_text'] = '未知'

            # 为收入订单计算违规承担费相关字段
            if int(fund_type) == 1 and order.get('violation_fee_rate'):
                payment_amount = float(order.get('payment_amount', 0))
                violation_rate = float(order.get('violation_fee_rate', 0))

                # 计算预估违规承担费（非已失效订单）
                if order.get('order_status') != 80:
                    order['estimated_violation_fee'] = payment_amount * violation_rate / 100
                else:
                    order['estimated_violation_fee'] = 0

                # 计算结算违规承担费（已结算订单）
                if order.get('order_status') == 60:
                    order['settled_violation_fee'] = payment_amount * violation_rate / 100
                else:
                    order['settled_violation_fee'] = 0

        # 计算业绩数据统计
        stats = {}

        # 构建统计查询条件
        stats_where_clause = where_clause

        # 计算订单量
        stats_query = f"SELECT COUNT(*) as order_count FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['order_count'] = cursor.fetchone()['order_count']

        # 计算支付金额
        stats_query = f"SELECT SUM(o.payment_amount) as total_payment FROM `order` o LEFT JOIN product p ON o.product_id = p.product_id WHERE {stats_where_clause}"
        cursor.execute(stats_query, params)
        stats['total_payment'] = float(cursor.fetchone()['total_payment'] or 0)

        if int(fund_type) == 1:  # 收入订单
            # 计算预估违规承担费 - 非已失效订单
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.violation_fee_rate / 100 * 0.85) as estimated_violation_fee
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status != 80
            """
            cursor.execute(stats_query, params)
            stats['estimated_violation_fee'] = float(cursor.fetchone()['estimated_violation_fee'] or 0)

            # 计算结算违规承担费 - 已结算订单
            stats_query = f"""
                SELECT SUM(o.payment_amount * p.violation_fee_rate / 100 * 0.85) as settled_violation_fee
                FROM `order` o
                LEFT JOIN product p ON o.product_id = p.product_id
                WHERE {stats_where_clause}
                AND o.order_status = 60
            """
            cursor.execute(stats_query, params)
            stats['settled_violation_fee'] = float(cursor.fetchone()['settled_violation_fee'] or 0)

        return jsonify({
            'code': 0,
            'message': '获取违规承担订单列表成功',
            'data': {
                'orders': orders,
                'total': total,
                'page': page,
                'pageSize': page_size,
                'stats': stats
            }
        })
    except Exception as e:
        logging.error(f"获取违规承担订单列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f"获取违规承担订单列表失败: {str(e)}",
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()




