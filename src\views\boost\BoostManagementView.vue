<template>
  <div class="boost-management-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>助播管理</h3>
        </div>
      </template>

      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="达人名称">
            <el-input v-model="searchForm.talent_search" placeholder="请输入达人名称" clearable />
          </el-form-item>
          <el-form-item label="商品名称">
            <el-input v-model="searchForm.product_search" placeholder="请输入商品名称" clearable />
          </el-form-item>
          <el-form-item label="商务名称" v-if="userRole !== 'business'">
            <el-input v-model="searchForm.business_search" placeholder="请输入商务名称" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="待审核" value="0" />
              <el-option label="已通过" value="1" />
              <el-option label="已拒绝" value="2" />
              <el-option label="已完成" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        :data="tableData"
        style="width: 100%"
        border
        v-loading="loading"
        size="default"
        class="boost-table"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: '500' }"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />

        <!-- 商品信息 -->
        <el-table-column label="商品信息" min-width="250">
          <template #default="scope">
            <div class="product-info">
              <el-image
                :src="scope.row.product_image"
                fit="cover"
                class="product-image"
                :preview-src-list="[scope.row.product_image]"
              >
                <template #error>
                  <div class="image-slot">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="product-details">
                <div class="product-name">{{ scope.row.product_name }}</div>
                <div class="product-meta">
                  <span class="product-id">ID: {{ scope.row.product_id }}</span>
                  <span class="product-price" v-if="scope.row.product_price"
                    >¥{{ scope.row.product_price }}</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 达人信息 -->
        <el-table-column label="达人信息" min-width="200">
          <template #default="scope">
            <div class="talent-info">
              <el-avatar :src="scope.row.talent_avatar" :size="45" class="talent-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="talent-details">
                <div class="talent-name">{{ scope.row.talent_name }}</div>
                <div class="talent-meta">
                  <span class="talent-id">ID: {{ scope.row.talent_id }}</span>
                  <span class="talent-fans" v-if="scope.row.talent_fans"
                    >{{ scope.row.talent_fans }}粉丝</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 申请商务 -->
        <el-table-column
          prop="business_contact"
          label="申请商务"
          width="120"
          align="center"
          v-if="userRole !== 'business'"
        />

        <!-- 费用信息 -->
        <el-table-column label="费用信息" min-width="180">
          <template #default="scope">
            <div class="fee-info">
              <div v-if="scope.row.target_gmv_rate" class="fee-item">
                <el-tag size="small" type="info">GMV返点: {{ scope.row.target_gmv_rate }}%</el-tag>
              </div>
              <div v-if="scope.row.fixed_amount" class="fee-item">
                <el-tag size="small" type="warning">固定: ¥{{ scope.row.fixed_amount }}</el-tag>
              </div>
              <div v-if="scope.row.actual_gmv" class="fee-item">
                <el-tag size="small" type="success">实际GMV: ¥{{ scope.row.actual_gmv }}</el-tag>
              </div>
              <div v-if="scope.row.actual_fee" class="fee-item">
                <el-tag size="small" type="success">实际费用: ¥{{ scope.row.actual_fee }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 时间信息 -->
        <el-table-column label="时间信息" min-width="180">
          <template #default="scope">
            <div class="time-info">
              <div v-if="scope.row.start_time" class="time-item">
                <span class="time-label">开始:</span>
                <span class="time-value">{{ formatDateTime(scope.row.start_time) }}</span>
              </div>
              <div v-if="scope.row.end_time" class="time-item">
                <span class="time-label">结束:</span>
                <span class="time-value">{{ formatDateTime(scope.row.end_time) }}</span>
              </div>
              <div class="time-item create-time">
                <span class="time-label">申请:</span>
                <span class="time-value">{{ formatDateTime(scope.row.create_time) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 状态 -->
        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="default">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 结算状态 -->
        <el-table-column prop="settlement_status" label="结算状态" width="120" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.settlement_status === 1 ? 'success' : 'info'" size="default">
              {{ scope.row.settlement_status === 1 ? '已结算' : '未结算' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column
          label="操作"
          fixed="right"
          width="220"
          align="center"
          v-if="userRole !== 'business'"
        >
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="info" size="small" @click="handleViewDetail(scope.row)"
                >详情</el-button
              >
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
                v-if="userRole === 'admin'"
                >删除</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑助播申请" width="600px">
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="状态">
          <el-select v-model="editForm.status">
            <el-option label="待审核" :value="0" />
            <el-option label="已通过" :value="1" />
            <el-option label="已拒绝" :value="2" />
            <el-option label="已完成" :value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="GMV返点费率">
          <el-input-number v-model="editForm.target_gmv_rate" :min="0" :max="100" :precision="2" />
          <span style="margin-left: 10px">%</span>
        </el-form-item>

        <el-form-item label="固定费用">
          <el-input-number v-model="editForm.fixed_amount" :min="0" :precision="2" />
          <span style="margin-left: 10px">元</span>
        </el-form-item>

        <el-form-item label="开始时间">
          <el-date-picker v-model="editForm.start_time" type="datetime" style="width: 100%" />
        </el-form-item>

        <el-form-item label="结束时间">
          <el-date-picker v-model="editForm.end_time" type="datetime" style="width: 100%" />
        </el-form-item>

        <el-form-item label="对接群名">
          <el-input v-model="editForm.live_content" />
        </el-form-item>

        <el-form-item label="峰值在线">
          <el-input v-model="editForm.live_address" placeholder="请输入峰值在线人数" />
        </el-form-item>

        <el-form-item label="实际GMV">
          <el-input-number v-model="editForm.actual_gmv" :min="0" :precision="2" />
          <span style="margin-left: 10px">元</span>
        </el-form-item>

        <el-form-item label="实际费用">
          <el-input-number v-model="editForm.actual_fee" :min="0" :precision="2" />
          <span style="margin-left: 10px">元</span>
        </el-form-item>

        <el-form-item label="结算状态">
          <el-select v-model="editForm.settlement_status">
            <el-option label="未结算" :value="0" />
            <el-option label="已结算" :value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label="运营备注">
          <el-input v-model="editForm.operation_notes" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="助播申请详情" width="800px">
      <div v-if="currentDetail" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请ID">{{ currentDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="申请商务">{{
            currentDetail.business_contact
          }}</el-descriptions-item>
          <el-descriptions-item label="商品名称">{{
            currentDetail.product_name
          }}</el-descriptions-item>
          <el-descriptions-item label="商品ID">{{ currentDetail.product_id }}</el-descriptions-item>
          <el-descriptions-item label="达人名称">{{
            currentDetail.talent_name
          }}</el-descriptions-item>
          <el-descriptions-item label="达人ID">{{ currentDetail.talent_id }}</el-descriptions-item>
          <el-descriptions-item label="GMV返点费率"
            >{{ currentDetail.target_gmv_rate }}%</el-descriptions-item
          >
          <el-descriptions-item label="固定费用"
            >¥{{ currentDetail.fixed_amount }}</el-descriptions-item
          >
          <el-descriptions-item label="开始时间">{{
            formatDateTime(currentDetail.start_time || '')
          }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{
            formatDateTime(currentDetail.end_time || '')
          }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{
            formatDateTime(currentDetail.create_time)
          }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(currentDetail.status)">
              {{ getStatusText(currentDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="实际GMV" v-if="currentDetail.actual_gmv"
            >¥{{ currentDetail.actual_gmv }}</el-descriptions-item
          >
          <el-descriptions-item label="实际费用" v-if="currentDetail.actual_fee"
            >¥{{ currentDetail.actual_fee }}</el-descriptions-item
          >
          <el-descriptions-item label="结算状态">
            <el-tag :type="currentDetail.settlement_status === 1 ? 'success' : 'info'">
              {{ currentDetail.settlement_status === 1 ? '已结算' : '未结算' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="currentDetail.live_content" style="margin-top: 20px">
          <h4>对接群名</h4>
          <p>{{ currentDetail.live_content }}</p>
        </div>

        <div v-if="currentDetail.live_address" style="margin-top: 20px">
          <h4>峰值在线</h4>
          <p>{{ currentDetail.live_address }}</p>
        </div>

        <div v-if="currentDetail.operation_notes" style="margin-top: 20px">
          <h4>运营备注</h4>
          <p>{{ currentDetail.operation_notes }}</p>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture, User } from '@element-plus/icons-vue'
import axios from 'axios'

// 获取用户角色
const userRole = computed(() => {
  const userInfo = localStorage.getItem('userInfo')
  if (userInfo) {
    const user = JSON.parse(userInfo)
    return user.role || 'business'
  }
  return 'business'
})

// 助播申请接口
interface BoostApplication {
  id: number
  product_id: string
  product_name: string
  product_image?: string
  product_price?: number
  talent_id: string
  talent_name: string
  talent_avatar?: string
  talent_fans?: string
  business_contact: string
  target_gmv_rate?: number
  fixed_amount?: number
  start_time?: string
  end_time?: string
  live_content?: string
  live_address?: string
  status: number
  operation_notes?: string
  actual_gmv?: number
  actual_fee?: number
  settlement_status: number
  create_time: string
  update_time?: string
  approve_time?: string
  approve_user?: string
}

// 搜索表单
const searchForm = reactive({
  talent_search: '',
  product_search: '',
  business_search: '',
  status: '',
})

// 表格数据
const tableData = ref<BoostApplication[]>([])
const loading = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 编辑对话框
const editDialogVisible = ref(false)
const editForm = reactive({
  id: 0,
  status: 0,
  operation_notes: '',
  actual_gmv: null as number | null,
  actual_fee: null as number | null,
  settlement_status: 0,
  target_gmv_rate: null as number | null,
  fixed_amount: null as number | null,
  start_time: null as Date | null,
  end_time: null as Date | null,
  live_content: '',
  live_address: '',
})

// 详情对话框
const detailDialogVisible = ref(false)
const currentDetail = ref<BoostApplication | null>(null)

// 工具方法
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getStatusText = (status: number) => {
  const statusMap = {
    0: '待审核',
    1: '已通过',
    2: '已拒绝',
    3: '已完成',
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

const getStatusTagType = (status: number) => {
  const typeMap = {
    0: 'warning',
    1: 'success',
    2: 'danger',
    3: 'info',
  }
  return typeMap[status as keyof typeof typeMap] || ''
}

// 搜索
const handleSearch = async () => {
  try {
    loading.value = true

    const token = localStorage.getItem('token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      status: searchForm.status,
      talent_search: searchForm.talent_search,
      product_search: searchForm.product_search,
      business_search: searchForm.business_search,
    }

    const response = await axios.get('/api/boost/list', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params,
    })

    if (response.data.code === 0) {
      tableData.value = response.data.data.list
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取助播列表失败:', error)
    ElMessage.error('获取助播列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.talent_search = ''
  searchForm.product_search = ''
  searchForm.business_search = ''
  searchForm.status = ''
  currentPage.value = 1
  handleSearch()
}

// 编辑助播申请
const handleEdit = async (row: BoostApplication) => {
  // 填充编辑表单
  Object.assign(editForm, {
    id: row.id,
    status: row.status,
    operation_notes: row.operation_notes || '',
    actual_gmv: row.actual_gmv,
    actual_fee: row.actual_fee,
    settlement_status: row.settlement_status,
    target_gmv_rate: row.target_gmv_rate,
    fixed_amount: row.fixed_amount,
    start_time: row.start_time ? new Date(row.start_time) : null,
    end_time: row.end_time ? new Date(row.end_time) : null,
    live_content: row.live_content || '',
    live_address: row.live_address || '',
  })

  editDialogVisible.value = true
}

// 查看详情
const handleViewDetail = (row: BoostApplication) => {
  currentDetail.value = row
  detailDialogVisible.value = true
}

// 删除助播申请
const handleDelete = async (row: BoostApplication) => {
  try {
    await ElMessageBox.confirm('确定要删除该助播申请吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const token = localStorage.getItem('token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    const response = await axios.delete(`/api/boost/delete/${row.id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (response.data.code === 0) {
      ElMessage.success('删除成功')
      handleSearch()
    } else {
      ElMessage.error(response.data.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除助播申请失败:', error)
      ElMessage.error('删除助播申请失败')
    }
  }
}

// 保存编辑
const handleSaveEdit = async () => {
  try {
    const token = localStorage.getItem('token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    const submitData = {
      status: editForm.status,
      operation_notes: editForm.operation_notes,
      actual_gmv: editForm.actual_gmv,
      actual_fee: editForm.actual_fee,
      settlement_status: editForm.settlement_status,
      target_gmv_rate: editForm.target_gmv_rate,
      fixed_amount: editForm.fixed_amount,
      start_time: editForm.start_time,
      end_time: editForm.end_time,
      live_content: editForm.live_content,
      live_address: editForm.live_address,
    }

    const response = await axios.put(`/api/boost/update/${editForm.id}`, submitData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })

    if (response.data.code === 0) {
      ElMessage.success('更新成功')
      editDialogVisible.value = false
      handleSearch()
    } else {
      ElMessage.error(response.data.message || '更新失败')
    }
  } catch (error) {
    console.error('更新助播申请失败:', error)
    ElMessage.error('更新助播申请失败')
  }
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}

onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.boost-management-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.boost-table {
  margin-bottom: 20px;
}

:deep(.boost-table .el-table__header-wrapper) {
  background: #f8f9fa;
}

:deep(.boost-table .el-table__row) {
  transition: all 0.3s ease;
}

:deep(.boost-table .el-table__row:hover) {
  background-color: #f5f7fa;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

/* 商品信息样式 */
.product-info {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.product-image {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  margin-right: 12px;
  flex-shrink: 0;
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  margin-bottom: 6px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-id,
.product-price {
  font-size: 12px;
  color: #909399;
}

.product-price {
  color: #f56c6c;
  font-weight: 500;
}

/* 达人信息样式 */
.talent-info {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.talent-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.talent-details {
  flex: 1;
  min-width: 0;
}

.talent-name {
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.talent-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.talent-id,
.talent-fans {
  font-size: 12px;
  color: #909399;
}

/* 费用信息样式 */
.fee-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px 0;
}

.fee-item {
  display: flex;
  justify-content: center;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px 0;
}

.time-item {
  display: flex;
  flex-direction: column;
  font-size: 12px;
}

.time-label {
  color: #909399;
  font-weight: 500;
  margin-bottom: 2px;
}

.time-value {
  color: #606266;
}

.create-time .time-value {
  color: #909399;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
}

.detail-content h4 {
  margin: 0 0 10px 0;
  color: #303133;
}
</style>
