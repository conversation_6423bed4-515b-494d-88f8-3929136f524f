-- 角色模板表
CREATE TABLE `role_templates` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '角色描述',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统角色(0:否,1:是)，系统角色不可删除',
  `permissions` json NOT NULL COMMENT '权限模板配置，JSON格式存储各模块权限',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色权限模板表' ROW_FORMAT = Dynamic;

-- 插入系统默认角色模板
INSERT INTO `role_templates` (`name`, `description`, `is_system`, `permissions`) VALUES
('管理员', '系统管理员，拥有所有权限', 1, '{
  "perm_talent": 1,
  "perm_team_leader": 1,
  "perm_product": 1,
  "perm_order": 1,
  "perm_boost": 1,
  "perm_performance_overview": 1,
  "perm_performance_statistics": 1,
  "perm_sample_management": 1,
  "perm_hide_product": 1,
  "perm_manage_tags": 1,
  "perm_merchant_commission_orders": 1,
  "perm_reserved_service_fee_orders": 1,
  "perm_team_leader_commission_orders": 1,
  "permissions": {
    "talent": {"data": ["all_data"], "operations": ["view", "add", "edit", "assign"]},
    "team_leader": {"data": ["all_data"], "operations": ["view", "add", "edit", "assign"]},
    "product": {"data": [], "operations": ["view", "add", "edit", "promote"]},
    "order": {"data": ["all_data"], "operations": [], "modules": ["merchant_commission_orders", "reserved_service_fee_orders", "team_leader_commission_orders"]},
    "boost": {"data": [], "operations": ["view", "add", "edit"]},
    "performance": {"data": ["all_data"], "operations": [], "modules": ["performance_overview", "performance_statistics"]},
    "sample": {"data": [], "operations": ["view", "add", "edit"]}
  }
}'),

('运营', '运营人员，拥有大部分权限', 1, '{
  "perm_talent": 1,
  "perm_team_leader": 1,
  "perm_product": 1,
  "perm_order": 1,
  "perm_boost": 1,
  "perm_performance_overview": 1,
  "perm_performance_statistics": 1,
  "perm_sample_management": 1,
  "perm_hide_product": 0,
  "perm_manage_tags": 0,
  "perm_merchant_commission_orders": 1,
  "perm_reserved_service_fee_orders": 1,
  "perm_team_leader_commission_orders": 1,
  "permissions": {
    "talent": {"data": ["all_data"], "operations": ["view", "add", "edit", "assign"]},
    "team_leader": {"data": ["all_data"], "operations": ["view", "add", "edit", "assign"]},
    "product": {"data": [], "operations": ["view", "add", "edit", "promote"]},
    "order": {"data": ["all_data"], "operations": [], "modules": ["merchant_commission_orders", "reserved_service_fee_orders", "team_leader_commission_orders"]},
    "boost": {"data": [], "operations": ["view", "add", "edit"]},
    "performance": {"data": ["all_data"], "operations": [], "modules": ["performance_overview", "performance_statistics"]},
    "sample": {"data": [], "operations": ["view", "add", "edit"]}
  }
}'),

('商务', '商务人员，基础权限', 1, '{
  "perm_talent": 1,
  "perm_team_leader": 1,
  "perm_product": 1,
  "perm_order": 1,
  "perm_boost": 0,
  "perm_performance_overview": 1,
  "perm_performance_statistics": 0,
  "perm_sample_management": 1,
  "perm_hide_product": 0,
  "perm_manage_tags": 0,
  "perm_merchant_commission_orders": 1,
  "perm_reserved_service_fee_orders": 1,
  "perm_team_leader_commission_orders": 1,
  "permissions": {
    "talent": {"data": ["own_only"], "operations": ["view", "add", "edit"]},
    "team_leader": {"data": ["own_only"], "operations": ["view", "add", "edit"]},
    "product": {"data": [], "operations": ["view", "add", "edit"]},
    "order": {"data": ["own_only"], "operations": [], "modules": ["merchant_commission_orders", "reserved_service_fee_orders", "team_leader_commission_orders"]},
    "boost": {"data": [], "operations": []},
    "performance": {"data": ["own_only"], "operations": [], "modules": ["performance_overview"]},
    "sample": {"data": [], "operations": ["view", "add", "edit"]}
  }
}');

-- 为business_user表添加role_template_id字段
ALTER TABLE `business_user` ADD COLUMN `role_template_id` bigint NULL COMMENT '角色模板ID，关联role_templates表' AFTER `role`;
ALTER TABLE `business_user` ADD INDEX `idx_role_template_id`(`role_template_id` ASC) USING BTREE;
