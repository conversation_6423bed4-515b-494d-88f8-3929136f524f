<template>
  <div class="hidden-product-container">
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="商品名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入商品名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="商品ID">
          <el-input
            v-model="searchForm.id"
            placeholder="请输入商品ID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="商家名称">
          <el-input
            v-model="searchForm.merchantName"
            placeholder="请输入商家名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="product-list">
      <el-table
        v-loading="loading"
        :data="productList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="商品ID" width="120" />
        <el-table-column label="商品图片" width="100">
          <template #default="{ row }">
            <el-image
              :src="row.image_url"
              :preview-src-list="[row.image_url]"
              fit="cover"
              style="width: 60px; height: 60px"
              :hide-on-click-modal="true"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="商品名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="price" label="价格" width="100">
          <template #default="{ row }">
            ¥{{ row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="merchant_name" label="商家名称" width="150" show-overflow-tooltip />
        <el-table-column prop="create_time" label="创建时间" width="180" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="success"
              size="small"
              @click="handleShowProduct(row)"
            >
              显示商品
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedProducts.length > 0" class="batch-actions">
      <el-button type="success" @click="handleBatchShow">
        批量显示 ({{ selectedProducts.length }})
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

// 定义商品类型接口
interface Product {
  id: string
  name: string
  price: number
  merchant_name: string
  create_time: string
  image_url: string
  is_hidden?: boolean
}

// 响应式数据
const loading = ref(false)
const productList = ref<Product[]>([])
const selectedProducts = ref<Product[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 搜索表单
const searchForm = reactive({
  name: '',
  id: '',
  merchantName: '',
})

// 获取隐藏商品列表
const fetchHiddenProducts = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      name: searchForm.name,
      id: searchForm.id,
      merchant_name: searchForm.merchantName,
      hidden_status: 'hidden', // 只获取隐藏的商品
    }

    const response = await axios.get('/api/product/list', { params })
    if (response.data.code === 0) {
      productList.value = response.data.data.products
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.message || '获取隐藏商品列表失败')
    }
  } catch (error) {
    console.error('获取隐藏商品列表出错:', error)
    ElMessage.error('获取隐藏商品列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchHiddenProducts()
}

// 重置搜索
const resetSearch = () => {
  searchForm.name = ''
  searchForm.id = ''
  searchForm.merchantName = ''
  currentPage.value = 1
  fetchHiddenProducts()
}

// 显示商品
const handleShowProduct = async (product: Product) => {
  try {
    await ElMessageBox.confirm(
      `确定要显示商品 "${product.name}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await axios.put(`/api/product/toggle-hidden/${product.id}`, {
      is_hidden: false
    })

    if (response.data.code === 0) {
      ElMessage.success('显示商品成功')
      fetchHiddenProducts() // 重新获取列表
    } else {
      ElMessage.error(response.data.message || '显示商品失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('显示商品失败:', error)
      ElMessage.error('操作失败，请检查网络连接')
    }
  }
}

// 批量显示商品
const handleBatchShow = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要显示选中的 ${selectedProducts.value.length} 个商品吗？`,
      '确认批量操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const promises = selectedProducts.value.map(product =>
      axios.put(`/api/product/toggle-hidden/${product.id}`, {
        is_hidden: false
      })
    )

    await Promise.all(promises)
    ElMessage.success('批量显示商品成功')
    fetchHiddenProducts() // 重新获取列表
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量显示商品失败:', error)
      ElMessage.error('批量操作失败，请检查网络连接')
    }
  }
}

// 选择变化
const handleSelectionChange = (selection: Product[]) => {
  selectedProducts.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchHiddenProducts()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchHiddenProducts()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchHiddenProducts()
})
</script>

<style scoped>
.hidden-product-container {
  padding: 20px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-list {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: #fff;
  padding: 10px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}
</style>
