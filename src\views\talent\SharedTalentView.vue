<template>
  <div class="shared-talent-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>共享达人</h3>
          <div class="header-operations">
            <!-- 移除添加按钮 -->
          </div>
        </div>
      </template>

      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="达人名称">
            <el-input v-model="searchForm.talent_name" placeholder="请输入达人名称" />
          </el-form-item>
          <el-form-item label="达人ID">
            <el-input v-model="searchForm.talent_id" placeholder="请输入达人ID" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="tableData" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />
        <!-- 合并达人基本信息列 -->
        <el-table-column label="达人信息" min-width="300">
          <template #default="scope">
            <div class="talent-info-card">
              <div class="talent-avatar">
                <el-avatar :size="50" :src="scope.row.avatar_url" fit="cover">
                  <el-icon><Picture /></el-icon>
                </el-avatar>
              </div>
              <div class="talent-details">
                <div class="talent-name-row">
                  <span class="talent-name">{{ scope.row.talent_name }}</span>
                  <el-tag size="small" type="info" class="fans-tag">
                    {{ formatFansCount(scope.row.fans_count) }}粉丝
                  </el-tag>
                </div>
                <div class="talent-id-row">
                  <span class="talent-id-label">ID:</span>
                  <span class="talent-id">{{ scope.row.talent_id }}</span>
                </div>
                <div class="talent-contact-row" v-if="scope.row.contact_name || scope.row.wechat">
                  <span v-if="scope.row.contact_name" class="talent-contact"
                    >联系人: {{ scope.row.contact_name }}</span
                  >
                  <span v-if="scope.row.wechat" class="talent-contact"
                    >微信: {{ scope.row.wechat }}</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 合并数据统计列 -->
        <el-table-column label="数据统计" min-width="200">
          <template #default="scope">
            <div class="talent-stats">
              <div class="stat-item">
                <span class="stat-label">寄样数:</span>
                <span class="stat-value">{{ scope.row.sample_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">开播数:</span>
                <span class="stat-value">{{ scope.row.live_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">累计单量:</span>
                <span class="stat-value">{{ scope.row.order_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">累计GMV:</span>
                <span class="stat-value">{{ scope.row.total_gmv || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 标签列 -->
        <el-table-column label="标签" min-width="150">
          <template #default="scope">
            <div class="talent-tags">
              <template v-if="scope.row.tags">
                <el-tag
                  v-for="(tag, index) in scope.row.tags.split(',')"
                  :key="index"
                  size="small"
                  class="tag-item"
                >
                  {{ tag.trim() }}
                </el-tag>
              </template>
              <span v-else class="no-tags">无标签</span>
            </div>
          </template>
        </el-table-column>

        <!-- 共享商务列 - 只对管理员和运营显示 -->
        <el-table-column v-if="showBusinessColumn" label="共享商务" min-width="200">
          <template #default="scope">
            <div class="shared-businesses">
              <template v-if="scope.row.shared_businesses">
                <div class="business-list clickable" @click="handleEditBusiness(scope.row)">
                  <el-tag
                    v-for="(business, index) in scope.row.shared_businesses.split(',').slice(0, 2)"
                    :key="index"
                    size="small"
                    class="business-tag"
                    type="success"
                  >
                    {{ business.trim() }}
                  </el-tag>
                  <el-tag
                    v-if="scope.row.shared_businesses.split(',').length > 2"
                    size="small"
                    class="business-tag"
                    type="info"
                  >
                    +{{ scope.row.shared_businesses.split(',').length - 2 }}
                  </el-tag>
                </div>
              </template>
              <span v-else class="no-businesses clickable" @click="handleEditBusiness(scope.row)">
                点击设置
              </span>
            </div>
          </template>
        </el-table-column>

        <!-- 备注列 -->
        <el-table-column prop="remarks" label="备注" min-width="150">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.remarks"
              :content="scope.row.remarks"
              placement="top"
              :show-after="500"
            >
              <div class="remarks-text">{{ scope.row.remarks }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 更新时间列 -->
        <el-table-column label="更新时间" min-width="120">
          <template #default="scope">
            {{ formatDateTime(scope.row.update_time, 'short') }}
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" fixed="right" width="250">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="info" size="small" @click="handleViewDetail(scope.row)"
              >详情</el-button
            >
            <el-button type="danger" size="small" @click="handleRemove(scope.row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="达人详情" width="55%" :top="'5vh'">
      <div v-if="detailData" class="talent-detail-container">
        <!-- 头部卡片 -->
        <el-card class="detail-header-card" shadow="hover">
          <div class="detail-header">
            <el-avatar :size="50" :src="detailData.avatar_url" fit="cover">
              <el-icon><Picture /></el-icon>
            </el-avatar>
            <div class="detail-header-info">
              <h2 class="talent-detail-name">{{ detailData.talent_name || '未设置' }}</h2>
              <div class="talent-detail-id">ID: {{ detailData.talent_id || '未设置' }}</div>
              <el-tag size="small" type="info" class="fans-tag">
                {{ detailData.fans_count ? formatFansCount(detailData.fans_count) : '0' }}粉丝
              </el-tag>
            </div>
          </div>
        </el-card>

        <div class="detail-cards-container">
          <!-- 左侧卡片 -->
          <div class="detail-column">
            <!-- 基本信息卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>基本信息</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">联系人姓名:</span>
                  <span class="value">{{ detailData.contact_name || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">联系人电话:</span>
                  <span class="value">{{ detailData.contact_phone || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">合作微信:</span>
                  <span class="value">{{ detailData.wechat || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">寄样地址:</span>
                  <span class="value">{{ detailData.shipping_address || '未设置' }}</span>
                </div>
              </div>
            </el-card>

            <!-- 数据统计卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>数据统计</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">寄样数:</span>
                  <span class="value">{{ detailData.sample_count || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">开播数:</span>
                  <span class="value">{{ detailData.live_count || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">累计单量:</span>
                  <span class="value">{{ detailData.order_count || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">累计GMV:</span>
                  <span class="value">{{ detailData.total_gmv || '0' }}</span>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 右侧卡片 -->
          <div class="detail-column">
            <!-- 其他信息卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>其他信息</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">标签:</span>
                  <span class="value">
                    <template v-if="detailData.tags">
                      <el-tag
                        v-for="(tag, index) in detailData.tags.split(',')"
                        :key="index"
                        size="small"
                        class="tag-item"
                      >
                        {{ tag.trim() }}
                      </el-tag>
                    </template>
                    <template v-else>未设置</template>
                  </span>
                </div>
                <div class="detail-item">
                  <span class="label">达人分类:</span>
                  <span class="value">{{ detailData.talent_category || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">对接商务:</span>
                  <span class="value">
                    <template v-if="detailData.talent_category === 'shared'">
                      {{ formatBusinessContact(detailData.shared_businesses) }}（共享）
                    </template>
                    <template v-else>
                      {{ detailData.business_contact || '未设置' }}
                    </template>
                  </span>
                </div>
                <div class="detail-item">
                  <span class="label">更新时间:</span>
                  <span class="value">{{
                    detailData.update_time ? formatDateTime(detailData.update_time) : '未设置'
                  }}</span>
                </div>
              </div>
            </el-card>

            <!-- 备注卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>备注</span>
                </div>
              </template>
              <div class="remarks-content">
                {{ detailData.remarks || '暂无备注' }}
              </div>
            </el-card>
          </div>
        </div>
      </div>
      <div v-else class="loading-container">
        <el-empty description="暂无数据" />
      </div>
    </el-dialog>

    <!-- 编辑达人对话框 -->
    <el-dialog v-model="editTalentDialogVisible" title="编辑达人信息" width="50%">
      <el-form :model="editForm" label-width="100px" :rules="editRules" ref="editFormRef">
        <div class="edit-talent-header">
          <el-avatar :size="60" :src="editForm.avatar_url" fit="cover">
            <el-icon><Picture /></el-icon>
          </el-avatar>
          <div class="talent-basic-info">
            <h3>{{ editForm.talent_name }}</h3>
            <p>ID: {{ editForm.talent_id }}</p>
          </div>
        </div>

        <el-divider content-position="left">详细信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="contact_name">
              <el-input v-model="editForm.contact_name" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="contact_phone">
              <el-input v-model="editForm.contact_phone" placeholder="请输入联系人电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合作微信" prop="wechat">
              <el-input v-model="editForm.wechat" placeholder="请输入合作微信" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签" prop="tags">
              <el-input v-model="editForm.tags" placeholder="请输入标签，多个标签用逗号分隔" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="寄样地址" prop="shipping_address">
          <el-input v-model="editForm.shipping_address" placeholder="请输入寄样地址" />
        </el-form-item>

        <!-- 共享商务字段 - 只有管理员和运营可编辑 -->
        <el-form-item v-if="canEditBusiness" label="共享商务" prop="shared_businesses">
          <BusinessUserSelector
            v-model="editForm.shared_businesses_array"
            :multiple="true"
            :multiple-limit="10"
            placeholder="请选择共享商务（最多10个）"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input v-model="editForm.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editTalentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEditForm" :loading="submitting">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 共享商务详情对话框 -->
    <el-dialog v-model="businessesDialogVisible" title="共享商务详情" width="20%">
      <div v-if="selectedBusinesses.length > 0" class="businesses-dialog-content">
        <el-table :data="selectedBusinesses" style="width: 100%" border>
          <el-table-column prop="business_name" label="商务名称" min-width="120">
            <template #default="scope">
              {{ scope.row.business_name || scope.row.business_contact }}
            </template>
          </el-table-column>
          <el-table-column prop="assign_time" label="分配时间" min-width="150">
            <template #default="scope">
              {{ formatDateTime(scope.row.assign_time) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="loading-container">
        <el-empty description="暂无数据" />
      </div>
    </el-dialog>

    <!-- 编辑商务对话框 -->
    <el-dialog v-model="editBusinessDialogVisible" title="编辑商务信息" width="450px">
      <el-form :model="businessForm" label-width="100px" ref="businessFormRef">
        <div class="business-edit-header">
          <el-avatar :size="50" :src="businessForm.avatar_url" fit="cover">
            <el-icon><Picture /></el-icon>
          </el-avatar>
          <div class="talent-basic-info">
            <h4>{{ businessForm.talent_name }}</h4>
            <p>ID: {{ businessForm.talent_id }}</p>
          </div>
        </div>

        <el-form-item label="达人类型" prop="talent_category">
          <el-select
            v-model="businessForm.talent_category"
            placeholder="请选择达人类型"
            style="width: 100%"
          >
            <el-option label="公海" value="public" />
            <el-option label="专属" value="exclusive" />
            <el-option label="专享" value="special" />
            <el-option label="共享" value="shared" />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="businessForm.talent_category === 'shared'"
          label="共享商务"
          prop="shared_businesses"
        >
          <BusinessUserSelector
            v-model="businessForm.shared_businesses_array"
            :multiple="true"
            :multiple-limit="10"
            placeholder="请选择共享商务（最多10个）"
          />
        </el-form-item>

        <el-form-item
          v-else-if="businessForm.talent_category !== 'public'"
          label="对接商务"
          prop="business_contact"
        >
          <BusinessUserSelector
            v-model="businessForm.business_contact"
            :multiple="false"
            placeholder="请选择对接商务"
          />
        </el-form-item>

        <el-form-item v-else label="商务信息">
          <el-text type="info">公海达人无需设置商务信息</el-text>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editBusinessDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitBusinessForm" :loading="businessSubmitting"
            >保存</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import BusinessUserSelector from '@/components/BusinessUserSelector.vue'

// 定义接口
interface TalentItem {
  id: number
  talent_name: string
  talent_id: string
  fans_count: number
  avatar_url: string
  contact_name?: string
  contact_phone?: string
  wechat: string
  shipping_address: string
  tags: string
  sample_count: number
  live_count: number
  order_count: number
  total_gmv: number
  talent_category: string
  business_contact: string
  remarks: string
  update_time: string
  shared_businesses?: string
  [key: string]: any
}

// 搜索表单
const searchForm = reactive({
  talent_name: '',
  talent_id: '',
})

// 表格数据
const tableData = ref<TalentItem[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 当前商务
const currentUser = ref<any>(null)
const currentBusiness = ref('')

// 用户权限
const userPermissions = ref<any>({
  talent: {
    data: [],
    operations: [],
  },
})

// 权限检查方法
const hasDataPermission = (permission: string) => {
  return userPermissions.value.talent.data.includes(permission)
}

const hasOperationPermission = (permission: string) => {
  return userPermissions.value.talent.operations.includes(permission)
}

// 便捷权限检查
const canViewOwnDataOnly = computed(() => hasDataPermission('own_only'))
const canViewAllData = computed(() => hasDataPermission('all_data'))
const canAddTalent = computed(() => hasOperationPermission('add_talent'))
const canEditTalentInfo = computed(() => hasOperationPermission('edit_talent_info'))
const canEditTalentType = computed(() => hasOperationPermission('edit_talent_type'))

// 计算属性：是否显示对接商务列
const showBusinessColumn = computed(() => {
  return (
    currentUser.value &&
    (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
  )
})

// 获取当前登录用户信息
const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      currentUser.value = JSON.parse(userStr)
      // 使用用户的name作为商务名称
      currentBusiness.value = currentUser.value.name

      // 解析权限信息
      if (currentUser.value.permissions) {
        try {
          const permissions =
            typeof currentUser.value.permissions === 'string'
              ? JSON.parse(currentUser.value.permissions)
              : currentUser.value.permissions
          userPermissions.value = permissions
        } catch (permError) {
          console.error('解析权限信息失败:', permError)
          // 使用默认权限
          userPermissions.value = {
            talent: { data: [], operations: [] },
          }
        }
      }

      console.log('当前登录商务:', currentBusiness.value)
      console.log('用户权限:', userPermissions.value)
    } else {
      console.error('未找到登录用户信息')
      ElMessage.warning('未找到登录用户信息，请重新登录')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请重新登录')
  }
}

// 查看详情对话框
const detailDialogVisible = ref(false)
const detailData = ref<TalentItem | null>(null)

// 共享商务详情对话框
const businessesDialogVisible = ref(false)
const selectedBusinesses = ref<
  Array<{
    business_name: string
    business_contact: string
    assign_time: string
  }>
>([])

// 编辑达人对话框
const editTalentDialogVisible = ref(false)
const editFormRef = ref<FormInstance>()
const editForm = reactive<TalentItem & { shared_businesses_array: string[] }>({
  id: 0,
  talent_name: '',
  talent_id: '',
  fans_count: 0,
  avatar_url: '',
  contact_name: '',
  contact_phone: '',
  wechat: '',
  shipping_address: '',
  tags: '',
  sample_count: 0,
  live_count: 0,
  order_count: 0,
  total_gmv: 0,
  talent_category: '',
  business_contact: '',
  shared_businesses: '',
  shared_businesses_array: [],
  remarks: '',
  update_time: '',
})
const submitting = ref(false)

// 商务编辑对话框
const editBusinessDialogVisible = ref(false)
const businessFormRef = ref<FormInstance>()
const businessForm = reactive({
  id: 0,
  talent_name: '',
  talent_id: '',
  avatar_url: '',
  talent_category: '',
  business_contact: '',
  shared_businesses_array: [] as string[],
})
const businessSubmitting = ref(false)

// 表单验证规则
const editRules = {
  contact_name: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  contact_phone: [{ required: true, message: '请输入联系人电话', trigger: 'blur' }],
}

// 计算属性：是否可以编辑商务信息
const canEditBusiness = computed(() => {
  return (
    currentUser.value &&
    (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
  )
})

// 获取达人列表
const fetchTalentList = async () => {
  loading.value = true
  try {
    // 根据用户角色选择不同的接口
    const isAdminOrOperation =
      currentUser.value &&
      (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
    const apiUrl = isAdminOrOperation ? '/api/talent/all-list' : '/api/talent/list'

    const params: any = {
      talent_name: searchForm.talent_name,
      talent_id: searchForm.talent_id,
      talent_type: 'shared',
      page: currentPage.value,
      page_size: pageSize.value,
    }

    // 商务用户需要传递business_contact参数
    if (!isAdminOrOperation) {
      params.business_contact = currentBusiness.value
    }

    const response = await axios.get(apiUrl, { params })

    if (response.data.code === 0) {
      tableData.value = response.data.data.list || []
      total.value = response.data.data.total || 0
    } else {
      ElMessage.error(response.data.message || '获取达人列表失败')
    }
  } catch (error) {
    console.error('获取达人列表失败:', error)
    ElMessage.error('获取达人列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 格式化粉丝数
const formatFansCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + '万'
  }
  return count.toString()
}

// 格式化时间
const formatDateTime = (dateTimeStr: string, format: 'full' | 'short' = 'full') => {
  if (!dateTimeStr) return ''

  try {
    const date = new Date(dateTimeStr)

    if (format === 'short') {
      // 简短格式：YYYY-MM-DD
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }

    // 完整格式：YYYY-MM-DD HH:MM:SS
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    console.error('日期格式化错误:', error)
    return dateTimeStr
  }
}

// 格式化商务联系人信息
const formatBusinessContact = (businessContact: string | undefined) => {
  if (!businessContact) return '未设置'

  // 直接尝试解析JSON字符串
  try {
    // 如果是字符串形式的JSON
    if (typeof businessContact === 'string') {
      // 检查是否是JSON格式
      if (businessContact.trim().startsWith('[') || businessContact.trim().startsWith('{')) {
        const data = JSON.parse(businessContact)

        // 如果是数组
        if (Array.isArray(data)) {
          return data
            .map((item) => {
              const name = item.business_contact || ''
              const date = item.assign_time ? formatDateTime(item.assign_time, 'short') : ''
              return `${name}${date ? ' (' + date + ')' : ''}`
            })
            .join('、')
        }
      }
    }

    // 如果是数组对象
    if (Array.isArray(businessContact)) {
      return businessContact
        .map((item) => {
          const name = item.business_contact || ''
          const date = item.assign_time ? formatDateTime(item.assign_time, 'short') : ''
          return `${name}${date ? ' (' + date + ')' : ''}`
        })
        .join('、')
    }

    // 如果是普通字符串
    return businessContact
  } catch (error) {
    console.error('格式化商务联系人信息失败:', error)
    // 如果解析失败，尝试提取字符串中的商务名称
    if (typeof businessContact === 'string') {
      // 尝试用正则表达式提取商务名称
      const matches = businessContact.match(/"business_contact":\s*"([^"]+)"/g)
      if (matches && matches.length > 0) {
        return matches
          .map((m) => {
            const match = m.match(/"business_contact":\s*"([^"]+)"/)
            return match ? match[1] : ''
          })
          .filter((name) => name)
          .join('、')
      }
    }

    // 最后的备选方案
    return '未能正确解析商务信息'
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchTalentList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.talent_name = ''
  searchForm.talent_id = ''
  handleSearch()
}

// 查看详情
const handleViewDetail = async (row: TalentItem) => {
  try {
    const response = await axios.get(`/api/talent/detail/${row.talent_id}`)

    if (response.data.code === 0) {
      detailData.value = response.data.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.data.message || '获取达人详情失败')
    }
  } catch (error) {
    console.error('获取达人详情失败:', error)
    ElMessage.error('获取达人详情失败')
  }
}

// 移除
const handleRemove = async (row: TalentItem) => {
  // 显示确认弹窗
  ElMessageBox.confirm(`确定要移除达人"${row.talent_name}"吗？`, '移除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const response = await axios.post('/api/talent/remove_from_business', {
          talent_id: row.talent_id,
          business_contact: currentBusiness.value,
        })

        if (response.data.code === 0) {
          ElMessage.success('成功移除达人关联')
          fetchTalentList() // 刷新列表
        } else {
          ElMessage.error(response.data.message || '操作失败')
        }
      } catch (error) {
        console.error('移除操作失败:', error)
        ElMessage.error('移除操作失败')
      }
    })
    .catch(() => {
      // 用户取消操作，不做任何处理
    })
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchTalentList()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchTalentList()
}

// 显示共享商务详情
const showBusinessesDetail = async (talentId: string) => {
  try {
    console.log('获取商务关联信息，达人ID:', talentId)
    const response = await axios.get(`/api/talent/business_relations/${talentId}`)
    console.log('商务关联信息响应:', response.data)

    if (response.data.code === 0) {
      selectedBusinesses.value = response.data.data.relations || []
      // 确保即使没有关联信息也能显示弹窗
      businessesDialogVisible.value = true

      if (selectedBusinesses.value.length === 0) {
        ElMessage.warning('该达人暂无商务关联信息')
      }
    } else {
      ElMessage.error(response.data.message || '获取商务关联信息失败')
    }
  } catch (error) {
    console.error('获取商务关联信息失败:', error)
    ElMessage.error('获取商务关联信息失败，请检查网络连接或后端接口')
    // 即使出错也显示弹窗，但内容为空
    selectedBusinesses.value = []
    businessesDialogVisible.value = true
  }
}

// 编辑达人
const handleEdit = (row: TalentItem) => {
  // 复制数据到编辑表单
  Object.assign(editForm, row)

  // 处理共享商务数组
  if (row.shared_businesses) {
    editForm.shared_businesses_array = row.shared_businesses.split(',').map((b) => b.trim())
  } else {
    editForm.shared_businesses_array = []
  }

  editTalentDialogVisible.value = true
}

// 编辑商务 - 打开专门的商务编辑对话框
const handleEditBusiness = (row: TalentItem) => {
  // 复制数据到商务编辑表单
  Object.assign(businessForm, {
    id: row.id,
    talent_name: row.talent_name,
    talent_id: row.talent_id,
    avatar_url: row.avatar_url,
    talent_category: row.talent_category || 'shared',
    business_contact: row.business_contact || '',
  })

  // 处理共享商务数组
  if (row.shared_businesses) {
    businessForm.shared_businesses_array = row.shared_businesses.split(',').map((b) => b.trim())
  } else {
    businessForm.shared_businesses_array = []
  }

  editBusinessDialogVisible.value = true
}

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const updateData: any = {
          talent_id: editForm.talent_id,
          contact_name: editForm.contact_name,
          contact_phone: editForm.contact_phone,
          wechat: editForm.wechat,
          shipping_address: editForm.shipping_address,
          tags: editForm.tags,
          remarks: editForm.remarks,
        }

        // 只有管理员和运营可以更新商务信息
        if (canEditBusiness.value) {
          // 将共享商务数组转换为逗号分隔的字符串
          updateData.shared_businesses = editForm.shared_businesses_array.join(',')
        }

        const response = await axios.post('/api/talent/update', updateData)

        if (response.data.code === 0) {
          ElMessage.success('更新达人信息成功')
          editTalentDialogVisible.value = false
          fetchTalentList() // 刷新列表
        } else {
          ElMessage.error(response.data.message || '更新达人信息失败')
        }
      } catch (error) {
        console.error('更新达人信息失败:', error)
        ElMessage.error('更新达人信息失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 提交商务编辑表单
const submitBusinessForm = async () => {
  businessSubmitting.value = true
  try {
    const updateData: any = {
      talent_id: businessForm.talent_id,
      talent_category: businessForm.talent_category,
    }

    // 根据达人类型设置不同的商务字段
    if (businessForm.talent_category === 'shared') {
      updateData.shared_businesses = businessForm.shared_businesses_array.join(',')
    } else if (businessForm.talent_category === 'public') {
      // 公海达人清空商务字段
      updateData.business_contact = ''
      updateData.shared_businesses = ''
    } else {
      updateData.business_contact = businessForm.business_contact
    }

    const response = await axios.post('/api/talent/update-business', updateData)

    if (response.data.code === 0) {
      ElMessage.success('更新商务信息成功')
      editBusinessDialogVisible.value = false
      fetchTalentList() // 刷新列表
    } else {
      ElMessage.error(response.data.message || '更新商务信息失败')
    }
  } catch (error) {
    console.error('更新商务信息失败:', error)
    ElMessage.error('更新商务信息失败')
  } finally {
    businessSubmitting.value = false
  }
}

onMounted(() => {
  getCurrentUser() // 获取当前登录用户信息
  fetchTalentList()
})
</script>

<style scoped>
.shared-talent-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
}

/* 达人信息卡片样式 */
.talent-info-card {
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.talent-avatar {
  margin-right: 15px;
}

.talent-details {
  flex: 1;
}

.talent-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.talent-name {
  font-weight: bold;
  font-size: 15px;
  margin-right: 10px;
}

.fans-tag {
  font-size: 12px;
}

.talent-id-row {
  color: #606266;
  font-size: 13px;
}

.talent-id-label {
  color: #909399;
  margin-right: 5px;
}

.talent-contact-row {
  margin-top: 5px;
  color: #909399;
  font-size: 13px;
}

.talent-contact {
  margin-right: 10px;
}

/* 详情对话框样式 */
.talent-detail-container {
  padding: 10px;
}

.detail-header-card {
  margin-bottom: 15px;
}

.detail-header {
  display: flex;
  align-items: center;
}

.detail-header-info {
  margin-left: 15px;
}

.talent-detail-name {
  margin: 0 0 3px 0;
  font-size: 16px;
  font-weight: bold;
}

.talent-detail-id {
  margin: 0 0 3px 0;
  color: #909399;
  font-size: 13px;
}

.detail-cards-container {
  display: flex;
  gap: 15px;
}

.detail-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.detail-card {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  padding: 8px 0;
}

.detail-list {
  padding: 0;
}

.detail-item {
  margin-bottom: 8px;
  padding: 5px;
  border-radius: 4px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  color: #606266;
  min-width: 90px;
  font-size: 13px;
}

.value {
  margin-left: 5px;
  color: #303133;
  font-size: 13px;
}

.tag-item {
  margin-right: 4px;
  margin-bottom: 4px;
}

.remarks-content {
  padding: 5px;
  font-size: 13px;
  min-height: 50px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* 数据统计样式 */
.talent-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
}

.stat-label {
  color: #606266;
  margin-right: 4px;
}

.stat-value {
  color: #303133;
  font-weight: 500;
}

/* 标签样式 */
.talent-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag-item {
  margin: 0;
}

.no-tags {
  color: #909399;
  font-size: 13px;
}

/* 共享商务样式 */
.shared-businesses {
  display: flex;
  align-items: center;
  gap: 8px;
}

.business-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.business-tag {
  font-size: 12px;
}

.no-businesses {
  color: #909399;
  font-size: 13px;
}

/* 备注文本样式 */
.remarks-text {
  max-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  font-size: 13px;
  color: #606266;
}

/* 对接商务列样式 */
.business-contact {
  color: #409eff;
  font-weight: 500;
}

.no-business {
  color: #c0c4cc;
  font-style: italic;
}

/* 编辑对话框样式 */
.edit-talent-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.talent-basic-info h3 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 18px;
}

.talent-basic-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

/* 商务编辑对话框样式 */
.business-edit-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}
</style>
