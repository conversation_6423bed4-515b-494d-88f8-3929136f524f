<template>
  <div class="product-list-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>
            商品管理
            <el-tag v-if="isAdminRole" type="danger" size="small" style="margin-left: 10px">
              管理员模式
            </el-tag>
          </h3>
          <div class="header-operations">
            <el-button
              v-if="isOperationRole || isAdminRole"
              type="info"
              :loading="syncLoading"
              @click="handleSyncProducts"
              >同步数据</el-button
            >
          </div>
        </div>
      </template>

      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="商品名称">
            <el-input v-model="searchForm.name" placeholder="请输入商品名称" clearable />
          </el-form-item>
          <el-form-item label="商品ID">
            <el-input v-model="searchForm.id" placeholder="请输入商品ID" clearable />
          </el-form-item>
          <el-form-item label="商家ID">
            <el-input v-model="searchForm.merchantId" placeholder="请输入商家ID" clearable />
          </el-form-item>
          <el-form-item label="商家名称">
            <el-input v-model="searchForm.merchantName" placeholder="请输入商家名称" clearable />
          </el-form-item>
          <el-form-item label="价格区间">
            <el-input
              v-model="searchForm.minPrice"
              placeholder="最低价"
              style="width: 100px"
              clearable
            />
            <span class="price-separator">-</span>
            <el-input
              v-model="searchForm.maxPrice"
              placeholder="最高价"
              style="width: 100px"
              clearable
            />
          </el-form-item>
          <el-form-item label="分类">
            <el-input v-model="searchForm.category" placeholder="请输入分类" clearable />
          </el-form-item>
          <el-form-item label="标签">
            <el-select v-model="searchForm.tag" placeholder="请选择标签" clearable>
              <el-option label="非独家" value="非独家" />
              <el-option label="独家" value="独家" />
            </el-select>
          </el-form-item>
          <el-form-item label="商品状态">
            <el-select v-model="searchForm.status" placeholder="请选择商品状态" clearable>
              <el-option label="在售" :value="1" />
              <el-option label="已下架" :value="0" />
            </el-select>
          </el-form-item>
          <!-- 隐藏状态过滤 - 只有有权限的用户才能看到 -->
          <el-form-item v-if="canHideProduct" label="显示状态">
            <el-select v-model="searchForm.hiddenStatus" placeholder="请选择显示状态" clearable>
              <el-option label="显示商品" value="visible" />
              <el-option label="隐藏商品" value="hidden" />
            </el-select>
          </el-form-item>
          <!-- 自定义标签过滤 - 有标签管理权限的用户可见 -->
          <el-form-item v-if="canManageTags" label="自定义标签">
            <el-select
              v-model="searchForm.customTags"
              placeholder="请选择标签"
              multiple
              clearable
              collapse-tags
              collapse-tags-tooltip
              style="width: 200px"
            >
              <el-option
                v-for="tag in availableTags"
                :key="tag.id"
                :label="tag.name"
                :value="tag.name"
              >
                <span class="tag-option" :style="getTagStyle(tag.color)">{{ tag.name }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="排序">
            <el-select v-model="searchForm.sortBy" placeholder="排序字段" style="width: 120px">
              <el-option label="默认" value="id" />
              <el-option label="商品ID" value="product_id" />
              <el-option label="商品名称" value="product_name" />
              <el-option label="价格" value="price" />
              <el-option label="创建时间" value="create_time" />
              <el-option label="库存" value="stock" />
              <el-option label="销量" value="sales" />
            </el-select>
            <el-select
              v-model="searchForm.sortOrder"
              placeholder="排序方向"
              style="width: 100px; margin-left: 5px"
            >
              <el-option label="降序" value="desc" />
              <el-option label="升序" value="asc" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
            <el-button @click="toggleAdvancedSearch">{{
              showAdvancedSearch ? '收起' : '高级搜索'
            }}</el-button>
          </el-form-item>
        </el-form>

        <!-- 高级搜索区域 -->
        <div v-show="showAdvancedSearch" class="advanced-search">
          <el-form :inline="true" :model="advancedSearchForm">
            <el-form-item label="库存范围">
              <el-input
                v-model="advancedSearchForm.minStock"
                placeholder="最小库存"
                style="width: 100px"
                clearable
              />
              <span class="price-separator">-</span>
              <el-input
                v-model="advancedSearchForm.maxStock"
                placeholder="最大库存"
                style="width: 100px"
                clearable
              />
            </el-form-item>
            <el-form-item label="销量范围">
              <el-input
                v-model="advancedSearchForm.minSales"
                placeholder="最小销量"
                style="width: 100px"
                clearable
              />
              <span class="price-separator">-</span>
              <el-input
                v-model="advancedSearchForm.maxSales"
                placeholder="最大销量"
                style="width: 100px"
                clearable
              />
            </el-form-item>
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="advancedSearchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 240px"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 全选区域 -->
      <div
        v-if="productList.length > 0 && (isOperationRole || isAdminRole)"
        class="select-all-area"
      >
        <el-checkbox
          v-model="selectAllProducts"
          @change="handleSelectAllProducts"
          :indeterminate="isIndeterminateProducts"
        >
          全选 ({{ selectedProductIds.length }}/{{ productList.length }})
        </el-checkbox>
        <div class="batch-actions">
          <el-button
            v-if="selectedProductIds.length > 0"
            type="warning"
            size="small"
            @click="handleBatchHideProducts"
          >
            批量隐藏商品 ({{ selectedProductIds.length }})
          </el-button>
          <el-button
            v-if="selectedProductIds.length > 0"
            type="primary"
            size="small"
            @click="handleBatchEditTags"
          >
            批量编辑标签 ({{ selectedProductIds.length }})
          </el-button>
          <el-button
            v-if="selectedProductIds.length > 0"
            type="success"
            size="small"
            @click="handleBatchEditScores"
          >
            批量编辑评分 ({{ selectedProductIds.length }})
          </el-button>
        </div>
      </div>

      <!-- 卡片式商品列表 -->
      <div v-loading="loading" class="product-cards-container">
        <el-empty v-if="productList.length === 0" description="暂无商品数据" />
        <div v-else class="product-cards">
          <el-card
            v-for="product in productList"
            :key="product.id"
            class="product-card clickable-card"
            shadow="hover"
            @click="handleCardClick(product, $event)"
          >
            <div class="product-card-content">
              <!-- 商品选择复选框 -->
              <div class="product-checkbox">
                <el-checkbox
                  :model-value="selectedProductIds.includes(product.id)"
                  @change="(checked: any) => handleSelectProduct(product.id, checked)"
                />
              </div>
              <div class="product-image">
                <el-image
                  :src="product.image_url"
                  fit="cover"
                  @click="openImageInNewTab(product.image_url)"
                  class="clickable-image"
                >
                  <template #error>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              <div class="product-info">
                <div class="product-title" :title="product.name">{{ product.name }}</div>
                <div class="product-price">¥{{ product.price.toFixed(2) }}</div>
                <div class="product-meta">
                  <div class="meta-item">
                    <span class="meta-label">商品ID:</span>
                    <span class="meta-value">{{ product.id }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">库存:</span>
                    <span class="meta-value">{{ product.stock }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">销量:</span>
                    <span class="meta-value">{{ product.sales || 0 }}</span>
                  </div>
                </div>
                <div class="product-meta">
                  <div class="meta-item">
                    <span class="meta-label">佣金率:</span>
                    <span class="meta-value">{{ product.merchant_commission }}%</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">评分:</span>
                    <span class="meta-value">{{ formatScore(product.store_score) || '暂无' }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">状态:</span>
                    <el-tag
                      :type="getStatusTagType(product.status)"
                      size="small"
                      class="status-tag"
                    >
                      {{ product.status_text || getStatusText(product.status) }}
                    </el-tag>
                  </div>
                </div>
                <!-- 评分信息 -->
                <div v-if="hasAnyScore(product)" class="product-scores">
                  <div class="score-item" v-if="product.cost_performance_score">
                    <span class="score-label">性价比:</span>
                    <el-rate
                      :model-value="parseFloat(product.cost_performance_score.toFixed(2))"
                      :max="10"
                      disabled
                      size="small"
                    />
                  </div>
                  <div class="score-item" v-if="product.routine_score">
                    <span class="score-label">套路:</span>
                    <el-rate
                      :model-value="parseFloat(product.routine_score.toFixed(2))"
                      :max="10"
                      disabled
                      size="small"
                    />
                  </div>
                  <div class="score-item" v-if="product.assist_broadcast_score">
                    <span class="score-label">助播能力:</span>
                    <el-rate
                      :model-value="parseFloat(product.assist_broadcast_score.toFixed(2))"
                      :max="10"
                      disabled
                      size="small"
                    />
                  </div>
                </div>

                <div class="product-category" :title="product.category">
                  <el-tag size="small" type="info">{{ product.category }}</el-tag>
                </div>
                <div v-if="product.tag" class="product-tag-display">
                  <el-tag :type="product.tag === '独家品' ? 'danger' : 'warning'" size="small">
                    {{ product.tag }}
                  </el-tag>
                </div>
                <!-- 自定义标签显示 -->
                <div
                  v-if="product.custom_tags && product.custom_tags.length > 0"
                  class="custom-tags-display"
                >
                  <el-tag
                    v-for="tag in product.custom_tags"
                    :key="tag.id"
                    :style="getTagStyle(tag.color)"
                    size="small"
                    class="custom-tag"
                  >
                    {{ tag.name }}
                  </el-tag>
                </div>
              </div>
            </div>
            <div class="product-actions">
              <!-- 运营用户和管理员可以看到编辑评分按钮 -->
              <el-button
                v-if="isOperationRole || isAdminRole"
                type="primary"
                size="small"
                @click="handleEditScores(product)"
                >编辑评分</el-button
              >

              <!-- 运营用户和管理员可以看到编辑费率按钮 -->
              <el-button
                v-if="isOperationRole || isAdminRole"
                type="success"
                size="small"
                @click="openRateDialog(product)"
                >编辑费率</el-button
              >

              <!-- 编辑推广按钮 -->
              <el-button type="info" size="small" @click="handleEditPromotion(product)"
                >编辑推广</el-button
              >

              <!-- 运营用户和管理员可以看到隐藏/显示商品按钮 -->
              <el-button
                v-if="canHideProduct"
                :type="product.is_hidden ? 'warning' : 'danger'"
                size="small"
                @click="handleToggleHidden(product)"
                >{{ product.is_hidden ? '显示商品' : '隐藏商品' }}</el-button
              >

              <!-- 运营用户和管理员可以看到标签管理按钮 -->
              <el-button
                v-if="canManageTags"
                type="info"
                size="small"
                @click="handleManageTags(product)"
                >管理标签</el-button
              >

              <!-- 商务用户和管理员可以看到快速申样按钮 -->
              <el-button
                v-if="isBusinessRole || isAdminRole"
                type="primary"
                size="small"
                @click="handleQuickSample(product)"
                >快速申样</el-button
              >

              <!-- 商务用户和管理员可以看到添加推广按钮 -->
              <el-button
                v-if="isBusinessRole || isAdminRole"
                type="success"
                size="small"
                @click="openPromotionDialog(product)"
                >添加推广</el-button
              >
            </div>
          </el-card>
        </div>
      </div>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 36, 48]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 商品详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="商品详情" width="70%">
      <div v-if="currentProduct" class="product-detail">
        <div class="product-detail-content">
          <!-- 左侧商品信息 -->
          <div class="product-detail-left">
            <div class="product-detail-header">
              <div class="product-detail-image">
                <el-image
                  :src="currentProduct.image_url"
                  fit="cover"
                  @click="openImageInNewTab(currentProduct.image_url)"
                  class="clickable-image"
                >
                  <template #error>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              <div class="product-detail-info">
                <h2>{{ currentProduct.name }}</h2>
                <div class="detail-price">¥{{ currentProduct.price.toFixed(2) }}</div>
                <div class="detail-item">
                  <span class="detail-label">商品ID:</span>
                  <span>{{ currentProduct.id }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">商家:</span>
                  <span
                    >{{ currentProduct.merchant_name }} (ID: {{ currentProduct.merchant_id }})</span
                  >
                </div>
                <div class="detail-item">
                  <span class="detail-label">库存:</span>
                  <span>{{ currentProduct.stock }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">销量:</span>
                  <span>{{ currentProduct.sales || 0 }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">商家佣金率:</span>
                  <span>{{ currentProduct?.merchant_commission || '0%' }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">商家返佣率:</span>
                  <span>{{ formatPercentage(currentProduct?.merchant_commission_rate) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">服务费率:</span>
                  <span>{{ currentProduct?.investment_commission || '0%' }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">系统服务费率:</span>
                  <span>{{ currentProduct?.service_fee_rate || '0%' }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">团长返佣率:</span>
                  <span>{{ formatPercentage(currentProduct?.team_leader_commission_rate) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">创建时间:</span>
                  <span>{{ currentProduct.create_time }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">分类:</span>
                  <span>{{ currentProduct.category }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">状态:</span>
                  <el-tag :type="getStatusTagType(currentProduct.status)">{{
                    getStatusText(currentProduct.status)
                  }}</el-tag>
                </div>
                <div class="detail-item">
                  <span class="detail-label">标签:</span>
                  <el-tag
                    v-if="currentProduct.tag"
                    :type="currentProduct.tag === '独家品' ? 'danger' : 'warning'"
                  >
                    {{ currentProduct.tag }}
                  </el-tag>
                  <span v-else>无</span>
                </div>

                <div class="detail-item">
                  <span class="detail-label">对接商务:</span>
                  <span>{{ currentProduct.business_contact || '未分配' }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">对接运营:</span>
                  <span>{{ currentProduct.operation_contact || '未分配' }}</span>
                  <el-button
                    v-if="isOperationRole || isAdminRole"
                    type="primary"
                    size="small"
                    style="margin-left: 10px"
                    @click="handleAssignOperation"
                  >
                    分配运营
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧评分图表 -->
          <div class="product-detail-right">
            <div class="score-chart-container">
              <h3>商品评分分析</h3>
              <div class="score-chart" ref="scoreChartRef"></div>
              <div class="score-actions">
                <el-button
                  v-if="isOperationRole || isAdminRole"
                  type="primary"
                  @click="handleEditScores()"
                  >编辑评分</el-button
                >
                <el-button
                  v-if="isOperationRole || isAdminRole"
                  type="success"
                  @click="handleEditRemark()"
                  >编辑备注</el-button
                >
              </div>
            </div>

            <!-- 备注显示区域 -->
            <div class="remark-container">
              <h3>商品备注</h3>
              <div class="remark-display">
                <div
                  v-if="currentProduct?.remark"
                  class="remark-content"
                  v-html="formatRemarkDisplay(currentProduct.remark)"
                ></div>
                <div v-else class="no-remark">暂无备注</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 同步数据对话框 -->
    <el-dialog v-model="syncDialogVisible" title="同步商品数据" width="50%">
      <div>
        <p>请选择要同步的活动：</p>
        <el-empty
          v-if="
            activityListType1.length === 0 && activityListType2.length === 0 && !activitiesLoading
          "
          description="暂无可用活动"
        />
        <el-tabs v-else v-model="activeTab" class="activity-tabs">
          <el-tab-pane label="普通招商" name="type1">
            <div class="activity-header">
              <el-checkbox
                v-model="selectAllType1"
                @change="handleSelectAllType1"
                :indeterminate="isIndeterminateType1"
              >
                全选
              </el-checkbox>
            </div>
            <el-checkbox-group v-model="selectedActivityIdsType1" class="activity-list">
              <el-checkbox
                v-for="activity in activityListType1"
                :key="activity.activityId"
                :label="activity.activityId"
                class="activity-item"
              >
                <div class="activity-info">
                  <div class="activity-name">{{ activity.activityTitle }}</div>
                  <div class="activity-meta">
                    <span>活动ID: {{ activity.activityId }}</span>
                    <span>开始时间: {{ formatTime(activity.activityBeginTime) }}</span>
                    <span>结束时间: {{ formatTime(activity.activityEndTime) }}</span>
                  </div>
                </div>
              </el-checkbox>
            </el-checkbox-group>
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPageType1"
                v-model:page-size="pageSizeType1"
                :page-sizes="[10, 20, 30, 40]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalType1"
                @size-change="handleSizeChangeType1"
                @current-change="handleCurrentChangeType1"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane label="专属招商" name="type2">
            <div class="activity-header">
              <el-checkbox
                v-model="selectAllType2"
                @change="handleSelectAllType2"
                :indeterminate="isIndeterminateType2"
              >
                全选
              </el-checkbox>
            </div>
            <el-checkbox-group v-model="selectedActivityIdsType2" class="activity-list">
              <el-checkbox
                v-for="activity in activityListType2"
                :key="activity.activityId"
                :label="activity.activityId"
                class="activity-item"
              >
                <div class="activity-info">
                  <div class="activity-name">{{ activity.activityTitle }}</div>
                  <div class="activity-meta">
                    <span>活动ID: {{ activity.activityId }}</span>
                    <span>开始时间: {{ formatTime(activity.activityBeginTime) }}</span>
                    <span>结束时间: {{ formatTime(activity.activityEndTime) }}</span>
                  </div>
                </div>
              </el-checkbox>
            </el-checkbox-group>
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPageType2"
                v-model:page-size="pageSizeType2"
                :page-sizes="[10, 20, 30, 40]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalType2"
                @size-change="handleSizeChangeType2"
                @current-change="handleCurrentChangeType2"
              />
            </div>
          </el-tab-pane>
        </el-tabs>

        <div class="sync-options">
          <el-checkbox v-model="syncAllPages">同步所有页面数据</el-checkbox>
          <el-input-number
            v-if="!syncAllPages"
            v-model="maxSyncPages"
            :min="1"
            :max="50"
            placeholder="最大页数"
            class="max-pages-input"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="syncDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="syncLoading" @click="confirmSync">开始同步</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 自定义属性设置 -->
    <el-dialog v-model="attributeDialogVisible" title="自定义商品属性" width="50%">
      <el-form :model="attributeForm" label-width="120px">
        <el-form-item label="属性名称">
          <el-input v-model="attributeForm.name" placeholder="请输入属性名称" />
        </el-form-item>
        <el-form-item label="属性值">
          <el-input v-model="attributeForm.value" placeholder="请输入属性值" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="attributeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAttribute">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑评分对话框 -->
    <el-dialog v-model="scoreDialogVisible" title="编辑商品评分" width="40%">
      <el-form :model="scoreForm" label-width="120px">
        <el-form-item label="控佣评分">
          <el-rate v-model="scoreForm.control_commission_score" :max="10" show-score />
        </el-form-item>
        <el-form-item label="配合度评分">
          <el-rate v-model="scoreForm.cooperation_score" :max="10" show-score />
        </el-form-item>
        <el-form-item label="卖货口评分">
          <el-rate v-model="scoreForm.trust_score" :max="10" show-score />
        </el-form-item>
        <el-form-item label="性价比评分">
          <el-rate v-model="scoreForm.cost_performance_score" :max="10" show-score />
        </el-form-item>
        <el-form-item label="套路评分">
          <el-rate v-model="scoreForm.routine_score" :max="10" show-score />
        </el-form-item>
        <el-form-item label="助播能力评分">
          <el-rate v-model="scoreForm.assist_broadcast_score" :max="10" show-score />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="scoreDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveScores">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑备注对话框 -->
    <el-dialog v-model="remarkDialogVisible" title="编辑商品备注" width="70%">
      <div class="remark-editor">
        <!-- 编辑区域 -->
        <div class="editor-section">
          <h4>编辑备注内容</h4>
          <el-input
            v-model="remarkForm.remark"
            type="textarea"
            :rows="6"
            placeholder="请输入商品备注，可以输入文字和上传图片..."
            class="remark-textarea"
          />

          <!-- 同步选项 -->
          <div class="sync-options">
            <el-checkbox v-model="remarkForm.syncByName">
              同步到相同商品名称的所有商品
            </el-checkbox>
            <el-tooltip content="勾选后，会将备注同步到所有相同商品名称的商品" placement="top">
              <el-icon class="info-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>

          <!-- 图片上传按钮 -->
          <div class="upload-section">
            <el-upload
              :action="uploadAction"
              :headers="uploadHeaders"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              :on-success="handleRemarkImageUploadSuccess"
              :on-error="handleImageUploadError"
              accept="image/*"
              class="image-uploader"
            >
              <el-button type="primary" plain>
                <el-icon><Plus /></el-icon>
                插入图片
              </el-button>
            </el-upload>
            <span class="upload-tip">支持 jpg、png、gif 格式，单个文件不超过 5MB</span>
          </div>
        </div>

        <!-- 预览区域 -->
        <div class="preview-section">
          <h4>预览效果</h4>
          <div class="preview-content">
            <div
              v-if="remarkForm.remark"
              class="remark-content"
              v-html="formatRemarkDisplay(remarkForm.remark)"
            ></div>
            <div v-else class="no-content">暂无内容</div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="remarkDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRemark" :loading="remarkSaving">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量编辑标签对话框 -->
    <el-dialog v-model="tagDialogVisible" title="批量设置独家标签" width="35%">
      <el-form :model="tagForm" label-width="100px">
        <el-form-item label="独家标签">
          <el-radio-group v-model="tagForm.isExclusive">
            <el-radio :label="true">设为独家</el-radio>
            <el-radio :label="false">取消独家</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选中商品">
          <span>{{ selectedProductIds.length }} 个商品</span>
        </el-form-item>
        <el-form-item label="同步规则">
          <el-checkbox v-model="tagForm.syncByName"> 同时更新相同商品名称的所有商品 </el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="tagDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveTags">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量编辑评分对话框 -->
    <el-dialog v-model="batchScoreDialogVisible" title="批量编辑评分" width="50%">
      <el-form :model="batchScoreForm" label-width="120px">
        <el-form-item label="控佣评分">
          <el-rate v-model="batchScoreForm.control_commission_score" :max="10" show-score />
        </el-form-item>
        <el-form-item label="配合度评分">
          <el-rate v-model="batchScoreForm.cooperation_score" :max="10" show-score />
        </el-form-item>
        <el-form-item label="卖货口评分">
          <el-rate v-model="batchScoreForm.trust_score" :max="10" show-score />
        </el-form-item>
        <el-form-item label="性价比评分">
          <el-rate v-model="batchScoreForm.cost_performance_score" :max="10" show-score />
        </el-form-item>
        <el-form-item label="套路评分">
          <el-rate v-model="batchScoreForm.routine_score" :max="10" show-score />
        </el-form-item>
        <el-form-item label="助播能力评分">
          <el-rate v-model="batchScoreForm.assist_broadcast_score" :max="10" show-score />
        </el-form-item>
        <el-form-item label="选中商品">
          <span>{{ selectedProductIds.length }} 个商品</span>
        </el-form-item>
        <el-form-item label="同步规则">
          <el-checkbox v-model="batchScoreForm.syncByName">
            同时更新相同商品名称的所有商品
          </el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchScoreDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveBatchScores">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加推广对话框 -->
    <el-dialog v-model="promotionDialogVisible" title="添加推广" width="60%">
      <div v-if="currentProduct" class="promotion-dialog">
        <div class="product-info">
          <h4>商品信息</h4>
          <p><strong>商品名称：</strong>{{ currentProduct.name }}</p>
          <p><strong>商品ID：</strong>{{ currentProduct.id }}</p>
          <p><strong>价格：</strong>¥{{ currentProduct.price?.toFixed(2) }}</p>
        </div>

        <div class="talent-selection">
          <h4>选择达人</h4>
          <el-form :model="promotionForm" label-width="120px">
            <el-form-item label="达人类型">
              <el-select v-model="promotionForm.talentType" @change="handleTalentTypeChange">
                <el-option label="专属达人" value="exclusive" />
                <el-option label="专享达人" value="special" />
                <el-option label="共享达人" value="shared" />
              </el-select>
            </el-form-item>
            <el-form-item label="搜索达人">
              <el-input
                v-model="promotionForm.searchKeyword"
                placeholder="请输入达人名称或ID搜索"
                @input="handleTalentSearch"
              />
            </el-form-item>
          </el-form>

          <div class="talent-list" v-loading="talentLoading">
            <el-table :data="talentList" style="width: 100%" @row-click="handleTalentSelect">
              <el-table-column prop="talent_id" label="达人ID" width="120" />
              <el-table-column prop="talent_name" label="达人名称" width="150" />
              <el-table-column prop="fans_count" label="粉丝数" width="120" />
              <el-table-column prop="talent_category" label="达人类型" width="100">
                <template #default="scope">
                  <el-tag :type="getTalentCategoryType(scope.row.talent_category)">
                    {{ getTalentCategoryText(scope.row.talent_category) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="business_contact" label="对接商务" width="120" />
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button
                    type="primary"
                    size="small"
                    @click.stop="handleAddPromotion(scope.row)"
                  >
                    添加推广
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <div class="pagination-container">
              <el-pagination
                v-model:current-page="talentCurrentPage"
                v-model:page-size="talentPageSize"
                :page-sizes="[10, 20, 50]"
                layout="total, sizes, prev, pager, next"
                :total="talentTotal"
                @size-change="handleTalentSizeChange"
                @current-change="handleTalentCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加编辑费率对话框 -->
    <el-dialog
      v-model="rateDialogVisible"
      title="编辑费率"
      width="600px"
      destroy-on-close
      @closed="resetRateForm"
    >
      <el-form
        ref="rateFormRef"
        :model="rateForm"
        :rules="rateRules"
        label-width="140px"
        label-position="left"
      >
        <el-form-item label="商品名称">
          <el-input :value="currentProduct?.name || ''" disabled />
        </el-form-item>
        <el-form-item label="商品ID">
          <el-input :value="currentProduct?.id || ''" disabled />
        </el-form-item>

        <!-- 系统服务费率（只读显示） -->
        <el-form-item label="系统服务费率">
          <el-input
            :value="currentProduct?.service_fee_rate || '0%'"
            disabled
            class="readonly-input"
          >
            <template #suffix>
              <el-tooltip content="从API同步的原始服务费率，不可编辑" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </template>
          </el-input>
          <span class="form-tip">API同步的原始费率，用于对比</span>
        </el-form-item>

        <!-- 商务服务费率（可编辑） -->
        <el-form-item label="商务服务费率" prop="serviceFeeRate">
          <el-input-number
            v-model="rateForm.serviceFeeRate"
            :min="0"
            :max="100"
            :step="1"
            :precision="0"
          >
            <template #suffix>%</template>
          </el-input-number>
          <span class="form-tip">当前值: {{ currentProduct?.investment_commission || '0%' }}</span>
        </el-form-item>
        <el-form-item label="预留服务费率" prop="reservedServiceFeeRate">
          <el-input-number
            v-model="rateForm.reservedServiceFeeRate"
            :min="0"
            :max="100"
            :step="1"
            :precision="0"
          >
            <template #suffix>%</template>
          </el-input-number>
          <span class="form-tip">预留给平台的服务费率</span>
        </el-form-item>
        <el-form-item label="商家返佣率" prop="merchantCommissionRate">
          <el-input-number
            v-model="rateForm.merchantCommissionRate"
            :min="0"
            :max="100"
            :step="1"
            :precision="0"
          >
            <template #suffix>%</template>
          </el-input-number>
          <span class="form-tip">商家返佣率</span>
        </el-form-item>
        <el-form-item label="团长返佣率" prop="teamLeaderCommissionRate">
          <el-input-number
            v-model="rateForm.teamLeaderCommissionRate"
            :min="0"
            :max="100"
            :step="1"
            :precision="0"
          >
            <template #suffix>%</template>
          </el-input-number>
          <span class="form-tip">团长推广获得的佣金比例</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRateForm" :loading="submittingRate"
            >确认</el-button
          >
        </span>
      </template>
    </el-dialog>

    <!-- 添加快速申样对话框 -->
    <el-dialog
      v-model="sampleDialogVisible"
      title="快速申样"
      width="500px"
      destroy-on-close
      @closed="resetSampleForm"
    >
      <div class="sample-product-info" v-if="currentProduct">
        <div class="sample-product-header">
          <el-image
            :src="currentProduct.image_url"
            fit="cover"
            style="width: 80px; height: 80px"
            @click="openImageInNewTab(currentProduct.image_url)"
            class="clickable-image"
          >
            <template #error>
              <div class="image-placeholder">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div class="sample-product-detail">
            <h4>{{ currentProduct.name }}</h4>
            <p class="sample-product-id">商品编号：{{ currentProduct.id }}</p>
            <p class="sample-product-price">¥{{ currentProduct.price?.toFixed(2) }}</p>
          </div>
        </div>
      </div>

      <el-form
        ref="sampleFormRef"
        :model="sampleForm"
        :rules="sampleRules"
        label-width="100px"
        class="sample-form"
      >
        <el-form-item label="收货人" prop="receiverName">
          <el-input v-model="sampleForm.receiverName" placeholder="请输入收货人姓名" />
        </el-form-item>
        <el-form-item label="收货数量" prop="sampleCount">
          <el-input-number v-model="sampleForm.sampleCount" :min="1" :max="10" />
        </el-form-item>
        <el-form-item label="收货人手机" prop="receiverPhone">
          <el-input v-model="sampleForm.receiverPhone" placeholder="请输入收货人手机号" />
        </el-form-item>
        <el-form-item label="收货人微信" prop="receiverWechat">
          <el-input v-model="sampleForm.receiverWechat" placeholder="请输入收货人微信号" />
        </el-form-item>
        <el-form-item label="收货地址" prop="receiverAddress">
          <el-input
            v-model="sampleForm.receiverAddress"
            type="textarea"
            placeholder="请输入详细收货地址"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="sampleForm.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="sampleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitSampleForm" :loading="submittingSample"
            >确认申请</el-button
          >
        </span>
      </template>
    </el-dialog>

    <!-- 编辑推广对话框 -->
    <el-dialog v-model="promotionManageDialogVisible" title="编辑推广" width="70%">
      <div class="promotion-manage-content">
        <div class="promotion-header">
          <h4>商品ID: {{ currentProduct?.id }}</h4>
          <h4>商品名称: {{ currentProduct?.name }}</h4>
          <p>共绑定 {{ talentBindings.length }} 个达人</p>
        </div>
        <el-table :data="talentBindings" style="width: 100%">
          <el-table-column prop="talent_id" label="达人ID" width="120" />
          <el-table-column prop="talent_name" label="达人名称" width="150" />
          <el-table-column prop="business_name" label="对接商务" width="120">
            <template #default="scope">
              <el-input
                v-if="scope.row.editing"
                v-model="scope.row.editBusinessName"
                size="small"
                @blur="handleSaveBusinessName(scope.row)"
                @keyup.enter="handleSaveBusinessName(scope.row)"
              />
              <span v-else>{{ scope.row.business_name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="binding_time" label="绑定时间" width="180" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button
                v-if="!scope.row.editing"
                type="primary"
                size="small"
                @click="handleEditBusinessName(scope.row)"
              >
                编辑商务
              </el-button>
              <el-button
                v-if="scope.row.editing"
                type="success"
                size="small"
                @click="handleSaveBusinessName(scope.row)"
              >
                保存
              </el-button>
              <el-button
                v-if="scope.row.editing"
                type="info"
                size="small"
                @click="handleCancelEditBusinessName(scope.row)"
              >
                取消
              </el-button>
              <el-button type="danger" size="small" @click="handleDeletePromotion(scope.row)">
                删除绑定
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 标签管理对话框 -->
    <el-dialog
      v-model="tagManageDialogVisible"
      title="管理商品标签"
      width="600px"
      @close="resetTagManageDialog"
    >
      <div v-if="currentProduct">
        <h4>商品：{{ currentProduct.name }}</h4>

        <!-- 当前标签 -->
        <div class="current-tags-section">
          <h5>当前标签：</h5>
          <div v-if="currentProductTags.length > 0" class="tags-container">
            <el-tag
              v-for="tag in currentProductTags"
              :key="tag.id"
              :style="getTagStyle(tag.color)"
              closable
              @close="removeProductTag(tag.id)"
              class="custom-tag closable-tag"
            >
              {{ tag.name }}
            </el-tag>
          </div>
          <div v-else class="no-tags">暂无标签</div>
        </div>

        <!-- 添加标签 -->
        <div class="add-tags-section">
          <h5>添加标签：</h5>
          <el-select
            v-model="selectedTagId"
            placeholder="选择要添加的标签"
            style="width: 200px; margin-right: 10px"
          >
            <el-option
              v-for="tag in availableTagsForAdd"
              :key="tag.id"
              :label="tag.name"
              :value="tag.id"
            >
              <span class="tag-option" :style="getTagStyle(tag.color)">{{ tag.name }}</span>
            </el-option>
          </el-select>
          <el-button type="primary" @click="addProductTag" :disabled="!selectedTagId">
            添加标签
          </el-button>
        </div>

        <!-- 创建新标签 -->
        <div class="create-tag-section">
          <h5>创建新标签：</h5>
          <el-form :model="newTagForm" inline>
            <el-form-item label="标签名称">
              <el-input
                v-model="newTagForm.name"
                placeholder="请输入标签名称"
                style="width: 150px"
              />
            </el-form-item>
            <el-form-item label="颜色">
              <el-color-picker v-model="newTagForm.color" />
            </el-form-item>
            <el-form-item>
              <el-button type="success" @click="createTag" :disabled="!newTagForm.name.trim()">
                创建标签
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 管理所有标签 -->
        <div class="manage-all-tags-section">
          <h5>管理所有标签：</h5>
          <div class="all-tags-container">
            <div v-for="tag in allTags" :key="tag.id" class="tag-item">
              <el-tag :style="getTagStyle(tag.color)" class="custom-tag">
                {{ tag.name }}
              </el-tag>
              <el-button
                type="danger"
                size="small"
                @click="deleteTag(tag.id, tag.name)"
                style="margin-left: 8px"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="tagManageDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import { Picture, InfoFilled, Plus, Close } from '@element-plus/icons-vue'
import axios from 'axios'
import * as echarts from 'echarts'
import { useUserStore } from '@/stores/user'
import { usePermissionStore } from '@/stores/permission'
import type { FormInstance } from 'element-plus'

// 定义商品类型接口
interface Product {
  id: string
  name: string
  price: number
  merchant_commission: string
  talent_commission: string
  investment_commission: string
  merchant_id: string
  merchant_name: string
  create_time: string
  image_url: string
  category: string
  stock: number
  sales: number
  status: number
  status_text?: string // 状态文本
  store_score: string
  activity_id: string
  control_commission_score?: number
  cooperation_score?: number
  trust_score?: number
  cost_performance_score?: number
  routine_score?: number
  assist_broadcast_score?: number
  remark?: string
  tag?: string
  original_data?: any
  service_fee_rate?: number
  reserved_service_fee_rate?: number
  merchant_commission_rate?: number
  team_leader_commission_rate?: number
  business_contact?: string
  operation_contact?: string
  is_hidden?: boolean // 是否隐藏
  custom_tags?: Array<{
    id: number
    name: string
    color: string
  }> // 自定义标签
}

// 定义活动类型接口
interface Activity {
  activityId: number
  activityTitle: string
  activityBeginTime: number
  activityEndTime: number
  activityType: number
  activityStatus: number
}

// 定义达人接口
interface Talent {
  talent_id: string
  talent_name: string
  talent_avatar?: string
  fans_count?: number
  talent_category?: string
  business_contact?: string
  [key: string]: any
}

// 定义标签接口
interface Tag {
  id: number
  name: string
  color: string
  description?: string
}

// 定义搜索表单接口
interface SearchForm {
  name: string
  id: string
  merchantId: string
  merchantName: string
  minPrice: string
  maxPrice: string
  category: string
  tag: string
  status: string
  hiddenStatus: string
  customTags: string[]
  sortBy: string
  sortOrder: string
}

// 定义高级搜索表单接口
interface AdvancedSearchForm {
  minStock: string
  maxStock: string
  minSales: string
  maxSales: string
  dateRange: string[]
}

// 路由实例
const route = useRoute()

// 搜索表单
const searchForm = reactive<SearchForm>({
  name: '',
  id: '',
  merchantId: '',
  merchantName: '',
  minPrice: '',
  maxPrice: '',
  category: '',
  tag: '',
  status: '',
  hiddenStatus: '', // 隐藏状态过滤
  customTags: [], // 自定义标签过滤
  sortBy: 'id',
  sortOrder: 'desc',
})

// 高级搜索表单
const advancedSearchForm = reactive<AdvancedSearchForm>({
  minStock: '',
  maxStock: '',
  minSales: '',
  maxSales: '',
  dateRange: [],
})

// 高级搜索显示状态
const showAdvancedSearch = ref(false)

// 商品列表数据
const productList = ref<Product[]>([])
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)
const loading = ref(false)
const syncLoading = ref(false)
const currentProduct = ref<Product | null>(null)

// 活动列表数据
const activityList = ref<Activity[]>([])
const activityListType1 = ref<Activity[]>([])
const activityListType2 = ref<Activity[]>([])
const activitiesLoading = ref(false)
const selectedActivityIdsType1 = ref<number[]>([])
const selectedActivityIdsType2 = ref<number[]>([])
const activeTab = ref('type1')
const syncAllPages = ref(true)
const maxSyncPages = ref(5)
const isActivitiesLoaded = ref(false)
const currentPageType1 = ref(1)
const pageSizeType1 = ref(10)
const totalType1 = ref(0)
const currentPageType2 = ref(1)
const pageSizeType2 = ref(10)
const totalType2 = ref(0)
const selectAllType1 = ref(false)
const selectAllType2 = ref(false)
const isIndeterminateType1 = ref(false)
const isIndeterminateType2 = ref(false)

// 对话框控制
const attributeDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const syncDialogVisible = ref(false)
const scoreDialogVisible = ref(false)
const remarkDialogVisible = ref(false)
const tagDialogVisible = ref(false)
const batchScoreDialogVisible = ref(false)
const promotionDialogVisible = ref(false)
const talentBindingsDialogVisible = ref(false)
const promotionManageDialogVisible = ref(false)

// 属性表单
const attributeForm = reactive({
  name: '',
  value: '',
})

// 评分表单
const scoreForm = reactive({
  control_commission_score: 0,
  cooperation_score: 0,
  trust_score: 0,
  cost_performance_score: 0,
  routine_score: 0,
  assist_broadcast_score: 0,
})

// 批量评分表单
const batchScoreForm = reactive({
  control_commission_score: 0,
  cooperation_score: 0,
  trust_score: 0,
  cost_performance_score: 0,
  routine_score: 0,
  assist_broadcast_score: 0,
  syncByName: false, // 是否按商品名称同步
})

// 推广表单
const promotionForm = reactive({
  talentType: 'exclusive',
  searchKeyword: '',
})

// 绑定达人数据
const talentBindings = ref<any[]>([])

// 绑定达人接口
interface TalentBinding {
  talent_id: string
  talent_name: string
  business_name: string
  binding_time: string
}

// 达人相关
const talentLoading = ref(false)
const talentList = ref<Talent[]>([])
const talentCurrentPage = ref(1)
const talentPageSize = ref(10)
const talentTotal = ref(0)

// 当前商务
const currentUser = ref<any>(null)
const currentBusiness = ref('')

// 用户权限
const userPermissions = ref<any>({
  product: {
    data: [],
    operations: [],
  },
})

// 权限检查方法
const hasOperationPermission = (permission: string) => {
  return userPermissions.value.product.operations.includes(permission)
}

// 便捷权限检查
const canAddPromotion = computed(() => hasOperationPermission('add_promotion'))
const canQuickSample = computed(() => hasOperationPermission('quick_sample'))
const canEditRating = computed(() => hasOperationPermission('edit_rating'))
const canEditRate = computed(() => hasOperationPermission('edit_rate'))
const canViewPromotionInfo = computed(() => hasOperationPermission('view_promotion_info'))
const canSyncData = computed(() => hasOperationPermission('sync_data'))

// 获取当前登录用户信息
const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      currentUser.value = JSON.parse(userStr)
      // 使用用户的name作为商务名称
      currentBusiness.value = currentUser.value.name

      // 解析权限信息
      if (currentUser.value.permissions) {
        try {
          const permissions =
            typeof currentUser.value.permissions === 'string'
              ? JSON.parse(currentUser.value.permissions)
              : currentUser.value.permissions
          userPermissions.value = permissions
        } catch (permError) {
          console.error('解析权限信息失败:', permError)
          // 使用默认权限
          userPermissions.value = {
            product: { data: [], operations: [] },
          }
        }
      }

      console.log('当前登录商务:', currentBusiness.value)
      console.log('商品模块权限:', userPermissions.value.product)
    } else {
      console.error('未找到登录用户信息')
      ElMessage.warning('未找到登录用户信息，请重新登录')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请重新登录')
  }
}

// 备注表单
const remarkForm = reactive({
  remark: '',
  images: [] as string[], // 图片URL数组
  syncByName: false, // 是否同步到相同商品名称
})

// 上传相关变量
const remarkSaving = ref(false)
const uploadAction = ref('/api/auth/upload-remark-image') // 使用类似头像上传的接口
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${localStorage.getItem('token')}`,
}))

// 移除不再需要的图片数组变量

// 新的备注编辑相关变量
const currentRemarkValue = ref('')
const originalRemarkValue = ref('')

// 计算属性：备注是否已更改
const hasRemarkChanged = computed(() => {
  return currentRemarkValue.value !== originalRemarkValue.value
})

// 标签表单
const tagForm = reactive({
  isExclusive: false, // 是否设为独家
  syncByName: false, // 是否按商品名称同步
})

// 选中的商品ID列表
const selectedProductIds = ref<string[]>([])

// 全选相关
const selectAllProducts = ref(false)
const isIndeterminateProducts = ref(false)

// 评分图表引用
const scoreChartRef = ref<HTMLElement>()
let scoreChart: echarts.ECharts | null = null

// 格式化时间
const formatTime = (timestamp: number): string => {
  if (!timestamp || timestamp <= 0) return '未知'
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN')
}

// 格式化评分，保留两位小数
const formatScore = (score: number | string): string => {
  if (!score || score === 0) return '0.00'
  const numScore = typeof score === 'string' ? parseFloat(score) : score
  return numScore.toFixed(2)
}

// 全选处理
const handleSelectAllType1 = (val: boolean) => {
  if (val) {
    selectedActivityIdsType1.value = activityListType1.value.map((item) => item.activityId)
  } else {
    selectedActivityIdsType1.value = []
  }
  isIndeterminateType1.value = false
}

const handleSelectAllType2 = (val: boolean) => {
  if (val) {
    selectedActivityIdsType2.value = activityListType2.value.map((item) => item.activityId)
  } else {
    selectedActivityIdsType2.value = []
  }
  isIndeterminateType2.value = false
}

// 监听选择变化
const updateSelectAllType1 = () => {
  const checkedCount = selectedActivityIdsType1.value.length
  const totalCount = activityListType1.value.length
  selectAllType1.value = checkedCount === totalCount && totalCount > 0
  isIndeterminateType1.value = checkedCount > 0 && checkedCount < totalCount
}

const updateSelectAllType2 = () => {
  const checkedCount = selectedActivityIdsType2.value.length
  const totalCount = activityListType2.value.length
  selectAllType2.value = checkedCount === totalCount && totalCount > 0
  isIndeterminateType2.value = checkedCount > 0 && checkedCount < totalCount
}

// 监听活动列表变化
watch(activityListType1, updateSelectAllType1, { deep: true })
watch(activityListType2, updateSelectAllType2, { deep: true })
watch(selectedActivityIdsType1, updateSelectAllType1)
watch(selectedActivityIdsType2, updateSelectAllType2)

// 切换高级搜索显示状态
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

// 获取商品列表
const fetchProducts = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      name: searchForm.name,
      id: searchForm.id,
      merchant_id: searchForm.merchantId,
      merchant_name: searchForm.merchantName,
      min_price: searchForm.minPrice,
      max_price: searchForm.maxPrice,
      category: searchForm.category,
      tag: searchForm.tag,
      status: searchForm.status,
      hidden_status: searchForm.hiddenStatus, // 添加隐藏状态参数
      custom_tags: searchForm.customTags.join(','), // 添加自定义标签参数
      sort_by: searchForm.sortBy,
      sort_order: searchForm.sortOrder,
    }

    const response = await axios.get('/api/product/list', { params })
    if (response.data.code === 0) {
      productList.value = response.data.data.products
      total.value = response.data.data.total
      // 重置选择状态
      selectedProductIds.value = []
      updateSelectAllStatus()
    } else {
      ElMessage.error(response.data.message || '获取商品列表失败')
    }
  } catch (error) {
    console.error('获取商品列表出错:', error)
    ElMessage.error('获取商品列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 获取活动列表
const fetchActivities = async (type = 'both', page1 = 1, page2 = 1, size1 = 10, size2 = 10) => {
  if (isActivitiesLoaded.value && type === 'both') {
    return
  }
  activitiesLoading.value = true
  try {
    const params = {
      page_type1: page1,
      page_size_type1: size1,
      page_type2: page2,
      page_size_type2: size2,
      type: type,
    }
    console.log('请求参数:', params)
    const response = await axios.get('/api/activity/list', { params })
    console.log('响应数据:', response.data)

    if (response.data.code === 0) {
      if (type === 'both' || type === 'type1') {
        activityListType1.value = response.data.data.type1.activities || []
        totalType1.value = response.data.data.type1.total || 0
        currentPageType1.value = response.data.data.type1.page || 1
        pageSizeType1.value = response.data.data.type1.page_size || 10
        console.log('type1数据:', activityListType1.value)
      }
      if (type === 'both' || type === 'type2') {
        activityListType2.value = response.data.data.type2.activities || []
        totalType2.value = response.data.data.type2.total || 0
        currentPageType2.value = response.data.data.type2.page || 1
        pageSizeType2.value = response.data.data.type2.page_size || 10
        console.log('type2数据:', activityListType2.value)
      }
      if (type === 'both') {
        isActivitiesLoaded.value = true
      }
    } else {
      ElMessage.error(response.data.message || '获取活动列表失败')
    }
  } catch (error) {
    console.error('获取活动列表出错:', error)
    ElMessage.error('获取活动列表失败，请检查网络连接')
  } finally {
    activitiesLoading.value = false
  }
}

// 同步商品数据
const handleSyncProducts = async () => {
  try {
    // 打开同步对话框
    syncDialogVisible.value = true

    // 获取活动列表
    await fetchActivities(
      'both',
      currentPageType1.value,
      currentPageType2.value,
      pageSizeType1.value,
      pageSizeType2.value,
    )
  } catch (error) {
    console.error('准备同步商品数据出错:', error)
    ElMessage.error('准备同步商品数据失败，请检查网络连接')
  }
}

// 确认同步
const confirmSync = async () => {
  try {
    if (
      selectedActivityIdsType1.value.length === 0 &&
      selectedActivityIdsType2.value.length === 0
    ) {
      ElMessage.warning('请选择要同步的活动')
      return
    }

    syncLoading.value = true

    // 调用同步接口，支持多个活动ID
    const response = await axios.post('/api/product/sync', {
      activity_ids: [...selectedActivityIdsType1.value, ...selectedActivityIdsType2.value],
      max_pages: syncAllPages.value ? null : maxSyncPages.value,
    })

    if (response.data.code === 0) {
      const result = response.data.data
      ElMessage.success(
        `同步成功！共处理${result.total}条数据，新增${result.success_count}条，更新${result.update_count}条，失败${result.failed_count}条。`,
      )

      // 关闭对话框
      syncDialogVisible.value = false

      // 重新加载商品列表
      fetchProducts()
    } else {
      ElMessage.error(response.data.message || '同步商品数据失败')
    }
  } catch (error: any) {
    console.error('同步商品数据出错:', error)
    ElMessage.error('同步商品数据失败，请检查网络连接')
  } finally {
    syncLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchProducts()
}

// 重置搜索
const resetSearch = () => {
  searchForm.name = ''
  searchForm.id = ''
  searchForm.merchantId = ''
  searchForm.merchantName = ''
  searchForm.minPrice = ''
  searchForm.maxPrice = ''
  searchForm.category = ''
  searchForm.tag = ''
  searchForm.status = ''
  searchForm.hiddenStatus = ''
  searchForm.customTags = []
  searchForm.sortBy = 'id'
  searchForm.sortOrder = 'desc'

  // 重置高级搜索
  advancedSearchForm.minStock = ''
  advancedSearchForm.maxStock = ''
  advancedSearchForm.minSales = ''
  advancedSearchForm.maxSales = ''
  advancedSearchForm.dateRange = []

  currentPage.value = 1
  handleSearch()
}

// 编辑
const handleEdit = (product: Product) => {
  console.log('编辑', product)
  ElMessage({
    message: '编辑功能开发中...',
    type: 'info',
  })
}

// 添加推广（在达人列表中的按钮）
const handleAddPromotion = (talent: Talent) => {
  handleAddPromotionToTalent(talent)
}

// 处理卡片点击事件
const handleCardClick = (product: Product, event: Event) => {
  // 如果点击的是按钮或复选框，不触发卡片点击
  const target = event.target as HTMLElement
  if (target.closest('.product-actions') || target.closest('.product-checkbox')) {
    return
  }

  // 触发查看详情
  handleViewDetail(product)
}

// 查看详情
const handleViewDetail = (product: Product) => {
  currentProduct.value = product
  detailDialogVisible.value = true

  // 初始化备注值
  initRemarkValue()

  // 移除图片初始化代码

  // 等待DOM更新后绘制图表
  nextTick(() => {
    drawScoreChart(product)
  })
}

// 隐藏/显示商品
const handleToggleHidden = async (product: Product) => {
  try {
    const action = product.is_hidden ? '显示' : '隐藏'
    await ElMessageBox.confirm(`确定要${action}商品 "${product.name}" 吗？`, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await axios.put(`/api/product/toggle-hidden/${product.id}`, {
      is_hidden: !product.is_hidden,
    })

    if (response.data.code === 0) {
      ElMessage.success(`${action}商品成功`)
      // 更新本地数据
      product.is_hidden = !product.is_hidden
    } else {
      ElMessage.error(response.data.message || `${action}商品失败`)
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('切换商品隐藏状态失败:', error)
      ElMessage.error('操作失败，请检查网络连接')
    }
  }
}

// 保存属性
const saveAttribute = () => {
  console.log('保存属性', attributeForm)
  ElMessage({
    message: '属性保存成功！',
    type: 'success',
  })
  attributeDialogVisible.value = false
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchProducts()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchProducts()
}

// 获取状态类型
const getStatusType = (status: number): string => {
  const statusMap: Record<number, string> = {
    1: 'success', // 在售 - 绿色
    0: 'danger', // 已下架 - 红色
  }
  return statusMap[status] || 'info' // 未知 - 灰色
}

// 获取状态文本
const getStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    1: '在售',
    0: '已下架',
  }
  return statusMap[status] || '未知'
}

// 分页大小改变
const handleSizeChangeType1 = (val: number) => {
  pageSizeType1.value = val
  currentPageType1.value = 1
  selectedActivityIdsType1.value = []
  fetchActivities(
    'type1',
    currentPageType1.value,
    currentPageType2.value,
    pageSizeType1.value,
    pageSizeType2.value,
  )
}

const handleSizeChangeType2 = (val: number) => {
  pageSizeType2.value = val
  currentPageType2.value = 1
  selectedActivityIdsType2.value = []
  fetchActivities(
    'type2',
    currentPageType1.value,
    currentPageType2.value,
    pageSizeType1.value,
    pageSizeType2.value,
  )
}

// 页码改变
const handleCurrentChangeType1 = (val: number) => {
  currentPageType1.value = val
  selectedActivityIdsType1.value = []
  fetchActivities(
    'type1',
    currentPageType1.value,
    currentPageType2.value,
    pageSizeType1.value,
    pageSizeType2.value,
  )
}

const handleCurrentChangeType2 = (val: number) => {
  currentPageType2.value = val
  selectedActivityIdsType2.value = []
  fetchActivities(
    'type2',
    currentPageType1.value,
    currentPageType2.value,
    pageSizeType1.value,
    pageSizeType2.value,
  )
}

// 编辑评分
const handleEditScores = (product?: Product) => {
  const targetProduct = product || currentProduct.value
  if (!targetProduct) return

  // 填充表单数据
  scoreForm.control_commission_score = targetProduct.control_commission_score || 0
  scoreForm.cooperation_score = targetProduct.cooperation_score || 0
  scoreForm.trust_score = targetProduct.trust_score || 0
  scoreForm.cost_performance_score = targetProduct.cost_performance_score || 0
  scoreForm.routine_score = targetProduct.routine_score || 0
  scoreForm.assist_broadcast_score = targetProduct.assist_broadcast_score || 0

  scoreDialogVisible.value = true
}

// 保存评分
const saveScores = async () => {
  const targetProduct = currentProduct.value
  if (!targetProduct) return

  try {
    const response = await axios.put(`/api/product/update-scores/${targetProduct.id}`, scoreForm)

    if (response.data.code === 0) {
      ElMessage.success('评分更新成功')
      scoreDialogVisible.value = false

      // 更新当前商品数据
      Object.assign(targetProduct, scoreForm)

      // 重新绘制图表
      drawScoreChart(targetProduct)

      // 重新获取商品列表
      fetchProducts()
    } else {
      ElMessage.error(response.data.message || '评分更新失败')
    }
  } catch (error) {
    console.error('更新评分失败:', error)
    ElMessage.error('更新评分失败，请检查网络连接')
  }
}

// 图片上传相关函数
const beforeImageUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].includes(file.type)
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isValidType) {
    ElMessage.error('只能上传 JPG、PNG、GIF 格式的图片!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const handleImageUploadSuccess = (response: any) => {
  if (response.code === 0) {
    remarkForm.images.push(response.data.image_url)
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

// 备注对话框图片上传成功处理
const handleRemarkImageUploadSuccess = (response: any) => {
  if (response.code === 0) {
    // 将图片链接插入到备注文本中
    const imageUrl = response.data.image_url
    const imageTag = `\n![图片](${imageUrl})\n`
    remarkForm.remark += imageTag
    ElMessage.success('图片上传成功，已插入到备注中')
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

const handleImageUploadError = () => {
  ElMessage.error('图片上传失败，请重试')
}

const removeImage = (index: number) => {
  remarkForm.images.splice(index, 1)
}

// 移除不再需要的函数

const previewImage = (imageUrl: string) => {
  window.open(imageUrl, '_blank')
}

// 格式化百分比显示
const formatPercentage = (value: any) => {
  if (!value) return '0%'
  const numValue = parseFloat(value.toString())
  return isNaN(numValue) ? '0%' : `${numValue}%`
}

// 格式化备注显示，像头像一样正确显示图片
const formatRemarkDisplay = (remark: string) => {
  if (!remark) return ''

  // 将备注按行分割
  const lines = remark.split('\n')
  let result = ''

  for (const line of lines) {
    const trimmedLine = line.trim()

    // 检查是否是图片链接格式 ![图片](url)
    const imageMatch = trimmedLine.match(/^!\[([^\]]*)\]\(([^)]+)\)$/)

    if (imageMatch) {
      // 是图片，用div包装，像头像一样处理
      const [, alt, url] = imageMatch
      result += `<div class="remark-image-wrapper" onclick="window.open('${url}', '_blank')">
        <img src="${url}" alt="${alt}" />
      </div>`
    } else if (trimmedLine) {
      // 是文本，显示为文本
      result += `<div class="remark-text">${trimmedLine}</div>`
    }
  }

  return result
}

// 编辑备注
const handleEditRemark = () => {
  if (!currentProduct.value) return

  remarkForm.remark = currentProduct.value.remark || ''
  remarkForm.syncByName = false // 重置同步选项
  remarkDialogVisible.value = true
}

// 保存备注
const saveRemark = async () => {
  if (!currentProduct.value) return

  try {
    remarkSaving.value = true
    const response = await axios.put(`/api/product/update-remark/${currentProduct.value.id}`, {
      remark: remarkForm.remark, // 只保存文本内容，图片链接已经在文本中
      sync_by_name: remarkForm.syncByName, // 是否同步到相同商品名称
    })

    if (response.data.code === 0) {
      const message = remarkForm.syncByName
        ? `备注更新成功，已同步到 ${response.data.data?.affected_count || 1} 个相同名称的商品`
        : '备注更新成功'
      ElMessage.success(message)
      remarkDialogVisible.value = false

      // 更新当前商品数据
      currentProduct.value.remark = remarkForm.remark

      // 如果是同步更新，需要更新本地商品列表中所有相同名称的商品
      if (remarkForm.syncByName && currentProduct.value.name) {
        productList.value.forEach((product) => {
          if (product.name === currentProduct.value?.name) {
            product.remark = remarkForm.remark
          }
        })
      }

      // 重新获取商品列表
      fetchProducts()
    } else {
      ElMessage.error(response.data.message || '备注更新失败')
    }
  } catch (error) {
    console.error('更新备注失败:', error)
    ElMessage.error('更新备注失败，请检查网络连接')
  } finally {
    remarkSaving.value = false
  }
}

// 新的备注处理方法
// 初始化备注值
const initRemarkValue = () => {
  if (currentProduct.value) {
    currentRemarkValue.value = currentProduct.value.remark || ''
    originalRemarkValue.value = currentProduct.value.remark || ''
  }
}

// 备注失去焦点时自动保存
const handleRemarkBlur = () => {
  if (hasRemarkChanged.value) {
    handleRemarkSave()
  }
}

// 重置备注
const handleRemarkReset = () => {
  currentRemarkValue.value = originalRemarkValue.value
}

// 保存备注（新方法）
const handleRemarkSave = async () => {
  if (!currentProduct.value) return

  remarkSaving.value = true
  try {
    const response = await axios.put(`/api/product/update-remark/${currentProduct.value.id}`, {
      remark: currentRemarkValue.value, // 只保存文本内容，图片链接已经在文本中
    })

    if (response.data.code === 0) {
      ElMessage.success('备注更新成功')

      // 更新当前商品数据
      currentProduct.value.remark = currentRemarkValue.value
      originalRemarkValue.value = currentRemarkValue.value

      // 重新获取商品列表
      fetchProducts()
    } else {
      ElMessage.error(response.data.message || '备注更新失败')
    }
  } catch (error) {
    console.error('更新备注失败:', error)
    ElMessage.error('更新备注失败，请检查网络连接')
  } finally {
    remarkSaving.value = false
  }
}

// 批量隐藏商品
const handleBatchHideProducts = async () => {
  if (selectedProductIds.value.length === 0) {
    ElMessage.warning('请先选择要隐藏的商品')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要隐藏选中的 ${selectedProductIds.value.length} 个商品吗？`,
      '批量隐藏商品',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 调用批量隐藏接口
    const response = await axios.put('/api/product/batch-hide', {
      product_ids: selectedProductIds.value,
      is_hidden: true,
    })

    if (response.data.code === 0) {
      // 更新本地商品列表状态
      productList.value.forEach((product) => {
        if (selectedProductIds.value.includes(product.id)) {
          product.is_hidden = true
        }
      })

      // 清空选择
      selectedProductIds.value = []
      updateSelectAllStatus()

      ElMessage.success(`成功隐藏 ${response.data.data.affected_count} 个商品`)
    } else {
      ElMessage.error(response.data.message || '批量隐藏商品失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量隐藏商品失败:', error)
      ElMessage.error('批量隐藏商品失败，请检查网络连接')
    }
  }
}

// 批量编辑标签
const handleBatchEditTags = () => {
  tagForm.isExclusive = false
  tagForm.syncByName = false
  tagDialogVisible.value = true
}

// 批量编辑评分
const handleBatchEditScores = () => {
  // 重置表单
  batchScoreForm.control_commission_score = 0
  batchScoreForm.cooperation_score = 0
  batchScoreForm.trust_score = 0
  batchScoreForm.cost_performance_score = 0
  batchScoreForm.routine_score = 0
  batchScoreForm.assist_broadcast_score = 0
  batchScoreForm.syncByName = false

  batchScoreDialogVisible.value = true
}

// 保存标签
const saveTags = async () => {
  try {
    const response = await axios.put('/api/product/batch-update-tags', {
      product_ids: selectedProductIds.value,
      is_exclusive: tagForm.isExclusive,
      sync_by_name: tagForm.syncByName,
    })

    if (response.data.code === 0) {
      ElMessage.success(`标签更新成功，共更新${response.data.data.affected_count}条记录`)
      tagDialogVisible.value = false
      selectedProductIds.value = []

      // 重新获取商品列表
      fetchProducts()
    } else {
      ElMessage.error(response.data.message || '标签更新失败')
    }
  } catch (error) {
    console.error('更新标签失败:', error)
    ElMessage.error('更新标签失败，请检查网络连接')
  }
}

// 保存批量评分
const saveBatchScores = async () => {
  try {
    // 检查是否有评分数据
    const scoreData = {
      control_commission_score: batchScoreForm.control_commission_score,
      cooperation_score: batchScoreForm.cooperation_score,
      trust_score: batchScoreForm.trust_score,
      cost_performance_score: batchScoreForm.cost_performance_score,
      routine_score: batchScoreForm.routine_score,
      assist_broadcast_score: batchScoreForm.assist_broadcast_score,
    }

    const hasScore = Object.values(scoreData).some((score) => score > 0)
    if (!hasScore) {
      ElMessage.warning('请至少设置一个评分')
      return
    }

    // 使用新的批量更新接口
    const response = await axios.put('/api/product/batch-update-scores', {
      product_ids: selectedProductIds.value,
      scores: scoreData,
      sync_by_name: batchScoreForm.syncByName,
    })

    if (response.data.code === 0) {
      ElMessage.success(`批量评分更新成功，共更新${response.data.data.affected_count}条记录`)
      batchScoreDialogVisible.value = false
      selectedProductIds.value = []

      // 重新获取商品列表
      fetchProducts()
    } else {
      ElMessage.error(response.data.message || '批量评分更新失败')
    }
  } catch (error) {
    console.error('批量更新评分失败:', error)
    ElMessage.error('批量更新评分失败，请检查网络连接')
  }
}

// 查看绑定达人
const handleViewTalentBindings = async (product: Product) => {
  try {
    currentProduct.value = product
    const response = await axios.get(`/api/product/talent-bindings/${product.id}`)

    if (response.data.code === 0) {
      talentBindings.value = response.data.data.bindings
      talentBindingsDialogVisible.value = true
    } else {
      ElMessage.error(response.data.message || '获取绑定达人信息失败')
    }
  } catch (error) {
    console.error('获取绑定达人信息失败:', error)
    ElMessage.error('获取绑定达人信息失败，请检查网络连接')
  }
}

// 编辑推广
const handleEditPromotion = async (product: Product) => {
  try {
    currentProduct.value = product
    const response = await axios.get(`/api/product/talent-bindings/${product.id}`)

    if (response.data.code === 0) {
      // 为每个绑定记录添加编辑状态
      talentBindings.value = response.data.data.bindings.map((binding: any) => ({
        ...binding,
        editing: false,
        editBusinessName: binding.business_name,
      }))
      promotionManageDialogVisible.value = true
    } else {
      ElMessage.error(response.data.message || '获取推广信息失败')
    }
  } catch (error) {
    console.error('获取推广信息失败:', error)
    ElMessage.error('获取推广信息失败，请检查网络连接')
  }
}

// 编辑商务名称
const handleEditBusinessName = (binding: any) => {
  binding.editing = true
  binding.editBusinessName = binding.business_name
}

// 取消编辑商务名称
const handleCancelEditBusinessName = (binding: any) => {
  binding.editing = false
  binding.editBusinessName = binding.business_name
}

// 保存商务名称
const handleSaveBusinessName = async (binding: any) => {
  if (!binding.editBusinessName.trim()) {
    ElMessage.error('商务名称不能为空')
    return
  }

  try {
    const response = await axios.put('/api/product/update-promotion', {
      product_id: currentProduct.value?.id,
      talent_id: binding.talent_id,
      talent_name: binding.talent_name,
      business_name: binding.editBusinessName,
    })

    if (response.data.code === 0) {
      binding.business_name = binding.editBusinessName
      binding.editing = false
      ElMessage.success('商务名称更新成功')
    } else {
      ElMessage.error(response.data.message || '更新商务名称失败')
    }
  } catch (error) {
    console.error('更新商务名称失败:', error)
    ElMessage.error('更新商务名称失败，请检查网络连接')
  }
}

// 删除推广绑定
const handleDeletePromotion = async (binding: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除达人 "${binding.talent_name}" 的推广绑定吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const response = await axios.delete('/api/product/delete-promotion-binding', {
      data: {
        product_id: currentProduct.value?.id,
        talent_id: binding.talent_id,
        talent_name: binding.talent_name,
        business_name: binding.business_name,
      },
    })

    if (response.data.code === 0) {
      // 从列表中移除该绑定
      const index = talentBindings.value.findIndex(
        (item: any) =>
          item.talent_id === binding.talent_id && item.talent_name === binding.talent_name,
      )
      if (index > -1) {
        talentBindings.value.splice(index, 1)
      }
      ElMessage.success('删除推广绑定成功')
    } else {
      ElMessage.error(response.data.message || '删除推广绑定失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除推广绑定失败:', error)
      ElMessage.error('删除推广绑定失败，请检查网络连接')
    }
  }
}

// 打开添加推广对话框
const openPromotionDialog = (product: Product) => {
  currentProduct.value = product
  promotionDialogVisible.value = true
  fetchTalentList()
}

// 获取达人列表
const fetchTalentList = async () => {
  try {
    talentLoading.value = true

    // 获取token
    const token = localStorage.getItem('token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    const params = {
      page: talentCurrentPage.value,
      page_size: talentPageSize.value,
      talent_type: promotionForm.talentType,
      business_contact: currentBusiness.value, // 添加当前商务参数
    }

    if (promotionForm.searchKeyword) {
      ;(params as any).talent_name = promotionForm.searchKeyword
    }

    const response = await axios.get('/api/talent/list', {
      params,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (response.data.code === 0) {
      talentList.value = response.data.data.list
      talentTotal.value = response.data.data.total
    } else {
      ElMessage.error(response.data.message || '获取达人列表失败')
    }
  } catch (error) {
    console.error('获取达人列表失败:', error)
    ElMessage.error('获取达人列表失败，请检查网络连接')
  } finally {
    talentLoading.value = false
  }
}

// 达人类型改变
const handleTalentTypeChange = () => {
  talentCurrentPage.value = 1
  fetchTalentList()
}

// 达人搜索
const handleTalentSearch = () => {
  talentCurrentPage.value = 1
  fetchTalentList()
}

// 选择达人
const handleTalentSelect = (row: Talent) => {
  console.log('选择达人:', row)
}

// 添加推广
const handleAddPromotionToTalent = async (talent: Talent) => {
  try {
    // 获取token
    const token = localStorage.getItem('token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    // 显示确认对话框
    await ElMessageBox.confirm(
      `确认将商品 "${currentProduct.value?.name}" 添加给达人 "${talent.talent_name}" 推广吗？`,
      '添加推广',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const response = await axios.post(
      '/api/product/add-promotion',
      {
        product_id: currentProduct.value?.id,
        talent_id: talent.talent_id,
        talent_name: talent.talent_name,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
    console.log(response.data.code)
    if (response.data.code === 0) {
      ElMessage.success('添加推广成功')

      // 更新本地商品数据
      if (currentProduct.value && response.data.data.business_contact) {
        currentProduct.value.business_contact = response.data.data.business_contact

        // 更新商品列表中的对应商品
        const index = productList.value.findIndex((p) => p.id === currentProduct.value?.id)
        if (index !== -1) {
          productList.value[index].business_contact = response.data.data.business_contact
        }
      }

      promotionDialogVisible.value = false
    } else {
      ElMessage.error(response.data.message || '添加推广失败')
    }
  } catch (error: any) {}
}

// 达人类型标签
const getTalentCategoryType = (category: string) => {
  switch (category) {
    case 'public':
      return 'info'
    case 'exclusive':
      return 'danger'
    case 'special':
      return 'warning'
    case 'shared':
      return 'success'
    default:
      return 'info'
  }
}

const getTalentCategoryText = (category: string) => {
  switch (category) {
    case 'public':
      return '公海'
    case 'exclusive':
      return '专属'
    case 'special':
      return '专享'
    case 'shared':
      return '共享'
    default:
      return '未知'
  }
}

// 达人分页
const handleTalentSizeChange = (val: number) => {
  talentPageSize.value = val
  fetchTalentList()
}

const handleTalentCurrentChange = (val: number) => {
  talentCurrentPage.value = val
  fetchTalentList()
}

// 选择商品
const handleSelectProduct = (productId: string, checked: boolean) => {
  if (checked) {
    if (!selectedProductIds.value.includes(productId)) {
      selectedProductIds.value.push(productId)
    }
  } else {
    const index = selectedProductIds.value.indexOf(productId)
    if (index > -1) {
      selectedProductIds.value.splice(index, 1)
    }
  }
  updateSelectAllStatus()
}

// 全选商品
const handleSelectAllProducts = (checked: boolean) => {
  if (checked) {
    selectedProductIds.value = productList.value.map((product) => product.id)
  } else {
    selectedProductIds.value = []
  }
  isIndeterminateProducts.value = false
}

// 更新全选状态
const updateSelectAllStatus = () => {
  const checkedCount = selectedProductIds.value.length
  const totalCount = productList.value.length
  selectAllProducts.value = checkedCount === totalCount && totalCount > 0
  isIndeterminateProducts.value = checkedCount > 0 && checkedCount < totalCount
}

// 检查商品是否有评分
const hasAnyScore = (product: Product): boolean => {
  return !!(
    product.cost_performance_score ||
    product.routine_score ||
    product.assist_broadcast_score
  )
}

// 绘制评分雷达图
const drawScoreChart = (product: Product) => {
  if (!scoreChartRef.value) return

  // 销毁之前的图表
  if (scoreChart) {
    scoreChart.dispose()
  }

  // 创建新图表
  scoreChart = echarts.init(scoreChartRef.value)

  const option = {
    title: {
      text: '商品评分分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    radar: {
      indicator: [
        { name: '控佣', max: 10 },
        { name: '配合度', max: 10 },
        { name: '卖货口', max: 10 },
        { name: '性价比', max: 10 },
        { name: '套路', max: 10 },
        { name: '助播能力', max: 10 },
      ],
      radius: '60%',
      center: ['50%', '60%'],
      splitNumber: 5,
      axisName: {
        color: '#333',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)'],
        },
      },
    },
    series: [
      {
        name: '评分',
        type: 'radar',
        data: [
          {
            value: [
              parseFloat((product.control_commission_score || 0).toFixed(2)),
              parseFloat((product.cooperation_score || 0).toFixed(2)),
              parseFloat((product.trust_score || 0).toFixed(2)),
              parseFloat((product.cost_performance_score || 0).toFixed(2)),
              parseFloat((product.routine_score || 0).toFixed(2)),
              parseFloat((product.assist_broadcast_score || 0).toFixed(2)),
            ],
            name: '当前评分',
            itemStyle: {
              color: '#5470c6',
            },
            areaStyle: {
              color: 'rgba(84, 112, 198, 0.3)',
            },
            lineStyle: {
              width: 2,
            },
          },
        ],
      },
    ],
  }

  scoreChart.setOption(option)
}

// 权限store
const permissionStore = usePermissionStore()

// 标签相关数据
const availableTags = ref<Tag[]>([])
const allTags = ref<Tag[]>([])
const tagManageDialogVisible = ref(false)
const currentProductTags = ref<Tag[]>([])
const selectedTagId = ref('')
const newTagForm = reactive({
  name: '',
  color: '#409EFF',
  description: '',
})

// 角色判断
const isOperationRole = computed(() => {
  return currentUser.value?.role === 'operation'
})

const isBusinessRole = computed(() => {
  return currentUser.value?.role === 'business'
})

const isAdminRole = computed(() => {
  return currentUser.value?.role === 'admin'
})

// 隐藏商品权限检查
const canHideProduct = computed(() => {
  return isAdminRole.value || hasOperationPermission('hide_product')
})

// 标签管理权限检查
const canManageTags = computed(() => {
  return isAdminRole.value || hasOperationPermission('manage_tags')
})

// 可添加的标签（排除当前商品已有的标签）
const availableTagsForAdd = computed(() => {
  return availableTags.value.filter(
    (tag) => !currentProductTags.value.some((currentTag) => currentTag.id === tag.id),
  )
})

// 费率对话框相关
const rateDialogVisible = ref(false)
const submittingRate = ref(false)
const rateFormRef = ref<FormInstance | null>(null)

const rateForm = reactive({
  serviceFeeRate: 0,
  reservedServiceFeeRate: 0,
  merchantCommissionRate: 0,
  teamLeaderCommissionRate: 0,
})

const rateRules = {
  serviceFeeRate: [
    { required: true, message: '请输入商务服务费率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '费率必须在0-100之间', trigger: 'blur' },
  ],
  reservedServiceFeeRate: [
    { required: true, message: '请输入预留服务费率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '费率必须在0-100之间', trigger: 'blur' },
  ],
  merchantCommissionRate: [
    { required: true, message: '请输入商家返佣率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '费率必须在0-100之间', trigger: 'blur' },
  ],
  teamLeaderCommissionRate: [
    { required: true, message: '请输入团长返佣率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '费率必须在0-100之间', trigger: 'blur' },
  ],
}

// 打开编辑费率对话框
const openRateDialog = (product: Product) => {
  currentProduct.value = product

  // 从百分比字符串中提取数值
  const extractRate = (rateStr: string | undefined) => {
    if (!rateStr) return 0
    return parseInt(rateStr.replace('%', '')) || 0
  }

  // 使用 investment_commission 作为服务费率
  rateForm.serviceFeeRate = extractRate(product.investment_commission)

  // 使用 merchant_commission_rate 作为商家返佣率（这是整数值，不需要转换）
  rateForm.merchantCommissionRate = product.merchant_commission_rate || 0

  // 直接使用数值字段
  rateForm.reservedServiceFeeRate = product.reserved_service_fee_rate || 0
  rateForm.teamLeaderCommissionRate = product.team_leader_commission_rate || 0

  rateDialogVisible.value = true
}

// 重置费率表单
const resetRateForm = () => {
  if (rateFormRef.value) {
    rateFormRef.value.resetFields()
  }
}

// 提交费率表单
const submitRateForm = async () => {
  if (!rateFormRef.value) return

  await rateFormRef.value.validate(async (valid: boolean) => {
    if (valid && currentProduct.value) {
      submittingRate.value = true
      try {
        const response = await axios.put(`/api/product/update-rates/${currentProduct.value.id}`, {
          serviceFeeRate: rateForm.serviceFeeRate,
          reservedServiceFeeRate: rateForm.reservedServiceFeeRate,
          merchantCommissionRate: rateForm.merchantCommissionRate,
          teamLeaderCommissionRate: rateForm.teamLeaderCommissionRate,
        })

        if (response.data.code === 0) {
          ElMessage.success('费率更新成功')

          // 更新本地数据
          const index = productList.value.findIndex((p) => p.id === currentProduct.value?.id)
          if (index !== -1) {
            // 使用返回的数据更新
            const updatedData = response.data.data
            productList.value[index].investment_commission = updatedData.investment_commission
            productList.value[index].merchant_commission = updatedData.merchant_commission
            productList.value[index].service_fee_rate = updatedData.service_fee_rate
            productList.value[index].reserved_service_fee_rate =
              updatedData.reserved_service_fee_rate
            productList.value[index].merchant_commission_rate = updatedData.merchant_commission_rate
            productList.value[index].team_leader_commission_rate =
              updatedData.team_leader_commission_rate
          }

          rateDialogVisible.value = false
        } else {
          ElMessage.error(response.data.message || '更新费率失败')
        }
      } catch (error) {
        console.error('更新费率失败:', error)
        ElMessage.error('更新费率失败，请检查网络连接')
      } finally {
        submittingRate.value = false
      }
    }
  })
}

// 添加样品申请相关变量
const sampleDialogVisible = ref(false)
const submittingSample = ref(false)
const sampleFormRef = ref<FormInstance | null>(null)

const sampleForm = reactive({
  receiverName: '',
  sampleCount: 1,
  receiverPhone: '',
  receiverWechat: '',
  receiverAddress: '',
  remark: '',
})

const sampleRules = {
  receiverName: [
    { required: true, message: '请输入收货人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  sampleCount: [
    { required: true, message: '请输入样品数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 10, message: '数量范围 1-10', trigger: 'blur' },
  ],
  receiverPhone: [
    { required: true, message: '请输入收货人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  receiverAddress: [
    { required: true, message: '请输入收货地址', trigger: 'blur' },
    { min: 5, max: 200, message: '地址长度在 5 到 200 个字符', trigger: 'blur' },
  ],
}

// 打开快速申样对话框
const handleQuickSample = (product: Product) => {
  currentProduct.value = product
  sampleDialogVisible.value = true
}

// 重置申样表单
const resetSampleForm = () => {
  if (sampleFormRef.value) {
    sampleFormRef.value.resetFields()
  }
}

// 提交申样表单
const submitSampleForm = async () => {
  if (!sampleFormRef.value || !currentProduct.value) return

  await sampleFormRef.value.validate(async (valid: boolean) => {
    if (valid && currentProduct.value) {
      submittingSample.value = true
      try {
        const response = await axios.post('/api/product/apply-sample', {
          product_id: currentProduct.value.id,
          receiver_name: sampleForm.receiverName,
          sample_count: sampleForm.sampleCount,
          receiver_phone: sampleForm.receiverPhone,
          receiver_wechat: sampleForm.receiverWechat,
          receiver_address: sampleForm.receiverAddress,
          remark: sampleForm.remark,
        })

        if (response.data.code === 0) {
          ElMessage.success('样品申请提交成功')
          sampleDialogVisible.value = false
        } else {
          ElMessage.error(response.data.message || '样品申请提交失败')
        }
      } catch (error) {
        console.error('样品申请提交失败:', error)
        ElMessage.error('样品申请提交失败，请检查网络连接')
      } finally {
        submittingSample.value = false
      }
    }
  })
}

// 获取标签列表
const fetchTags = async () => {
  if (!canManageTags.value) return

  try {
    const response = await axios.get('/api/tags/list')
    if (response.data.code === 0) {
      availableTags.value = response.data.data
      allTags.value = response.data.data // 同时设置所有标签列表
    } else {
      console.error('获取标签列表失败:', response.data.message)
    }
  } catch (error) {
    console.error('获取标签列表出错:', error)
  }
}

// 管理商品标签
const handleManageTags = async (product: Product) => {
  currentProduct.value = product
  await fetchProductTags(product.id)
  tagManageDialogVisible.value = true
}

// 获取商品标签
const fetchProductTags = async (productId: string) => {
  try {
    const response = await axios.get(`/api/tags/product/${productId}`)
    if (response.data.code === 0) {
      currentProductTags.value = response.data.data
    } else {
      console.error('获取商品标签失败:', response.data.message)
    }
  } catch (error) {
    console.error('获取商品标签出错:', error)
  }
}

// 为商品添加标签
const addProductTag = async () => {
  if (!selectedTagId.value || !currentProduct.value) return

  try {
    const response = await axios.post(`/api/tags/product/${currentProduct.value.id}/add`, {
      tag_id: selectedTagId.value,
    })

    if (response.data.code === 0) {
      ElMessage.success('添加标签成功')
      await fetchProductTags(currentProduct.value.id)
      await fetchProducts() // 刷新商品列表
      selectedTagId.value = ''
    } else {
      ElMessage.error(response.data.message || '添加标签失败')
    }
  } catch (error) {
    console.error('添加标签出错:', error)
    ElMessage.error('添加标签失败，请检查网络连接')
  }
}

// 移除商品标签
const removeProductTag = async (tagId: number) => {
  if (!currentProduct.value) return

  try {
    const response = await axios.delete(
      `/api/tags/product/${currentProduct.value.id}/remove/${tagId}`,
    )

    if (response.data.code === 0) {
      ElMessage.success('移除标签成功')
      await fetchProductTags(currentProduct.value.id)
      await fetchProducts() // 刷新商品列表
    } else {
      ElMessage.error(response.data.message || '移除标签失败')
    }
  } catch (error) {
    console.error('移除标签出错:', error)
    ElMessage.error('移除标签失败，请检查网络连接')
  }
}

// 创建新标签
const createTag = async () => {
  if (!newTagForm.name.trim()) return

  try {
    const response = await axios.post('/api/tags/create', {
      name: newTagForm.name.trim(),
      color: newTagForm.color,
      description: newTagForm.description,
    })

    if (response.data.code === 0) {
      ElMessage.success('创建标签成功')
      await fetchTags() // 刷新标签列表
      // 重置表单
      newTagForm.name = ''
      newTagForm.color = '#409EFF'
      newTagForm.description = ''
    } else {
      ElMessage.error(response.data.message || '创建标签失败')
    }
  } catch (error) {
    console.error('创建标签出错:', error)
    ElMessage.error('创建标签失败，请检查网络连接')
  }
}

// 删除标签
const deleteTag = async (tagId: number, tagName: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除标签 "${tagName}" 吗？如果有商品正在使用此标签，将无法删除。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const response = await axios.delete(`/api/tags/delete/${tagId}`)

    if (response.data.code === 0) {
      ElMessage.success('删除标签成功')
      await fetchTags() // 刷新标签列表
    } else {
      ElMessage.error(response.data.message || '删除标签失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除标签出错:', error)
      ElMessage.error('删除标签失败，请检查网络连接')
    }
  }
}

// 重置标签管理对话框
const resetTagManageDialog = () => {
  currentProductTags.value = []
  selectedTagId.value = ''
}

// 计算标签样式，确保文字在任何背景色下都清晰可见
const getTagStyle = (backgroundColor: string) => {
  // 计算背景色的亮度
  const getLuminance = (color: string) => {
    // 移除#号
    const hex = color.replace('#', '')

    // 转换为RGB
    const r = parseInt(hex.substring(0, 2), 16)
    const g = parseInt(hex.substring(2, 4), 16)
    const b = parseInt(hex.substring(4, 6), 16)

    // 计算相对亮度
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
    return luminance
  }

  const luminance = getLuminance(backgroundColor)
  const textColor = luminance > 0.5 ? '#000000' : '#ffffff'

  return {
    backgroundColor: backgroundColor,
    color: textColor,
    border: `1px solid ${backgroundColor}`,
    marginRight: '4px',
    marginBottom: '2px',
  }
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  const typeMap: Record<number, string> = {
    1: 'success', // 在售 - 绿色
    0: 'danger', // 已下架 - 红色
  }
  return typeMap[status] || 'info' // 未知 - 灰色
}

onMounted(() => {
  getCurrentUser() // 获取当前登录用户信息
  fetchProducts()
  fetchTags() // 获取标签列表

  // 处理URL参数，如果有productId或productName参数，则搜索对应商品
  if (route.query.productId) {
    searchForm.id = route.query.productId as string
    // 延迟执行搜索，确保页面加载完成
    nextTick(() => {
      fetchProducts()
    })
  } else if (route.query.productName) {
    searchForm.name = route.query.productName as string
    // 延迟执行搜索，确保页面加载完成
    nextTick(() => {
      fetchProducts()
    })
  }

  // 监听窗口大小变化，重绘图表
  window.addEventListener('resize', () => {
    if (scoreChart) {
      scoreChart.resize()
    }
  })
})

// 分配运营
const handleAssignOperation = () => {
  if (!currentProduct.value) return

  ElMessageBox.prompt('请输入运营人员姓名', '分配运营', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputValue: currentProduct.value.operation_contact || '',
    inputValidator: (value) => {
      return value.trim() !== '' || '运营人员姓名不能为空'
    },
  })
    .then(async ({ value }) => {
      try {
        const response = await axios.put(
          `/api/product/assign-operation/${currentProduct.value?.id}`,
          {
            operation_contact: value.trim(),
          },
        )

        if (response.data.code === 0) {
          ElMessage.success('分配运营成功')

          // 更新本地数据
          if (currentProduct.value) {
            currentProduct.value.operation_contact = value.trim()

            // 更新商品列表中的对应商品
            const index = productList.value.findIndex((p) => p.id === currentProduct.value?.id)
            if (index !== -1) {
              productList.value[index].operation_contact = value.trim()
            }
          }
        } else {
          ElMessage.error(response.data.message || '分配运营失败')
        }
      } catch (error) {
        console.error('分配运营失败:', error)
        ElMessage.error('分配运营失败，请检查网络连接')
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 在 ProductListView.vue 中添加一个打开图片的方法

// 1. 添加一个新的方法用于在新标签页打开图片
const openImageInNewTab = (imageUrl: string) => {
  if (imageUrl) {
    window.open(imageUrl, '_blank')
  }
}
</script>

<style scoped>
.product-list-container {
  padding: 20px;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-operations {
  display: flex;
  gap: 10px;
}

.search-area {
  margin-bottom: 20px;
  width: 100%;
}

.advanced-search {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.price-separator {
  margin: 0 5px;
  color: #909399;
}

.select-all-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.batch-actions {
  display: flex;
  gap: 10px;
}

.price-separator {
  margin: 0 5px;
}

.product-cards-container {
  min-height: 400px;
}

.product-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.product-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s;
  position: relative;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-card-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-image {
  height: 200px;
  width: 100%;
  overflow: hidden;
}

.product-image .el-image {
  width: 100%;
  height: 100%;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
}

.image-placeholder .el-icon {
  font-size: 40px;
  color: #c0c4cc;
}

.product-info {
  padding: 15px 0;
  flex: 1;
}

.product-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  height: 48px;
}

.product-price {
  font-size: 18px;
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 10px;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.meta-item {
  display: flex;
}

.meta-label {
  color: #909399;
  margin-right: 4px;
}

.meta-value {
  color: #606266;
}

.product-category {
  margin-top: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-tag-display {
  margin-top: 8px;
}

.product-scores {
  margin-top: 10px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.score-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 12px;
}

.score-item:last-child {
  margin-bottom: 0;
}

.score-label {
  width: 40px;
  color: #606266;
  margin-right: 8px;
}

.product-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.product-actions .el-button {
  display: flex;
  align-items: center;
}

.product-actions .el-icon {
  margin-right: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 商品详情样式 */
.product-detail {
  padding: 20px;
}

.product-detail-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.product-detail-left {
  flex: 1;
}

.product-detail-right {
  width: 320px;
  flex-shrink: 0;
}

.product-detail-header {
  display: flex;
  gap: 20px;
}

.product-detail-image {
  width: 280px;
  height: 280px;
  flex-shrink: 0;
}

.product-detail-image .el-image {
  width: 100%;
  height: 100%;
}

.product-detail-info {
  flex: 1;
}

.detail-price {
  font-size: 24px;
  color: #ff6b6b;
  font-weight: bold;
  margin: 15px 0;
}

.detail-item {
  margin-bottom: 10px;
  line-height: 24px;
}

.detail-label {
  display: inline-block;
  width: 100px;
  color: #909399;
}

/* 活动列表样式 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: 20px 0;
  width: 100%;
}

.activity-item {
  width: 100%;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-right: 0;
  height: auto;
}

.activity-item:hover {
  background-color: #f5f7fa;
}

.activity-info {
  margin-left: 30px;
  width: 100%;
}

.activity-name {
  font-weight: bold;
  margin-bottom: 8px;
}

.activity-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  color: #909399;
  font-size: 13px;
}

.activity-header {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.sync-options {
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.max-pages-input {
  width: 120px;
}

:deep(.el-card) {
  width: 100%;
}

:deep(.el-table) {
  width: 100% !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.score-chart-container {
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.score-chart {
  width: 100%;
  height: 280px;
  margin: 10px 0;
  border-radius: 4px;
  background-color: #ffffff;
}

.form-tip {
  margin-left: 10px;
  font-size: 12px;
  color: #909399;
}

.sample-product-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.sample-product-header {
  display: flex;
  gap: 15px;
}

.sample-product-detail {
  flex: 1;
}

.sample-product-detail h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.sample-product-id {
  margin: 0 0 5px 0;
  font-size: 13px;
  color: #909399;
}

.sample-product-price {
  margin: 0;
  font-size: 16px;
  color: #ff6b6b;
  font-weight: 600;
}

.clickable-image {
  cursor: pointer;
  transition: opacity 0.2s;
}

.clickable-image:hover {
  opacity: 0.9;
}

/* 标签相关样式 */
.custom-tags-display {
  margin-top: 4px;
}

.current-tags-section,
.add-tags-section,
.create-tag-section {
  margin-bottom: 20px;
}

.current-tags-section h5,
.add-tags-section h5,
.create-tag-section h5 {
  margin-bottom: 10px;
  color: #333;
  font-weight: 600;
}

.tags-container {
  min-height: 32px;
  padding: 8px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
}

.no-tags {
  color: #999;
  font-style: italic;
  padding: 8px;
  text-align: center;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
}

/* 自定义标签样式 */
.custom-tag {
  font-weight: 500;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
  line-height: 1.5;
}

.closable-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.tag-option {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 500;
}

/* 商品状态标签样式 */
.status-tag {
  font-weight: 500;
  font-size: 12px;
}

/* 商品选择复选框样式 */
.product-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px;
}

/* 备注容器样式 */
.remark-container {
  margin-top: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafbfc;
}

.remark-container h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.remark-input {
  margin-bottom: 12px;
}

.remark-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-bottom: 8px;
}

.remark-tip {
  font-size: 12px;
  color: #909399;
  text-align: right;
}

.remark-readonly {
  padding: 12px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  color: #606266;
  min-height: 60px;
  line-height: 1.5;
}

.remark-container.readonly {
  background-color: #f9f9f9;
}

.remark-container.readonly h3 {
  color: #606266;
}

/* 编辑费率对话框样式 */
.readonly-input {
  background-color: #f5f7fa;
}

.readonly-input .el-input__inner {
  background-color: #f5f7fa;
  color: #606266;
}

.info-icon {
  color: #909399;
  cursor: help;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

/* 可点击卡片样式 */
.clickable-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.product-actions {
  pointer-events: auto;
}

.product-checkbox {
  pointer-events: auto;
}

/* 图片上传相关样式 */
.image-upload-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.image-uploader {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.image-uploader:hover {
  border-color: #409eff;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #8c939d;
}

.upload-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.image-item {
  position: relative;
  width: 100px;
  height: 100px;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  cursor: pointer;
}

.remove-image {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 10;
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

/* 图片上传区域样式 */
.image-upload-area {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.inline-image-uploader {
  display: inline-block;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
}

/* 备注预览样式 */
.remark-preview {
  margin-top: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.remark-preview-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.remark-content {
  line-height: 1.6;
  color: #303133;
}

/* 备注显示样式 */
.remark-display {
  line-height: 1.6;
  color: #303133;
  padding: 8px;
  width: 100%;
  box-sizing: border-box;
  height: 280px;
  overflow: hidden;
}

/* 备注图片包装器 - 像头像一样 */
.remark-image-wrapper {
  width: 100%;
  height: 260px;
  margin: 8px 0;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
  position: relative;
}

.remark-image-wrapper:hover {
  transform: scale(1.02);
}

.remark-image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.no-remark {
  color: #909399;
  font-style: italic;
}

/* 评分操作按钮样式 */
.score-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

/* 编辑备注对话框样式 */
.remark-editor {
  display: flex;
  gap: 24px;
}

.sync-options {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 12px 0;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.info-icon {
  color: #909399;
  cursor: help;
}

.editor-section {
  flex: 1;
}

.preview-section {
  flex: 1;
  border-left: 1px solid #e4e7ed;
  padding-left: 24px;
}

.editor-section h4,
.preview-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.remark-textarea {
  margin-bottom: 16px;
}

.upload-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
}

.preview-content {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  min-height: 300px;
  background-color: #fafafa;
  width: 100%;
}

.no-content {
  color: #909399;
  font-style: italic;
  text-align: center;
  margin-top: 50px;
}

/* 备注内容显示样式 */
.remark-content {
  width: 100%;
}

.remark-content .remark-text {
  margin: 8px 0;
  padding: 8px 12px;
  background-color: #e7f3ff;
  border-radius: 8px;
  display: inline-block;
  max-width: 80%;
  word-wrap: break-word;
  flex-shrink: 0;
}

/* 标签管理样式 */
.manage-all-tags-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.all-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.tag-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.talent-bindings-content {
  padding: 10px 0;
}

.bindings-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.bindings-header h4 {
  margin: 5px 0;
  color: #303133;
}

.bindings-header p {
  margin: 10px 0 0 0;
  color: #606266;
  font-size: 14px;
}
</style>
