<template>
  <div class="home-container">
    <!-- 欢迎卡片 -->
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card class="welcome-card" shadow="hover">
          <div class="welcome-header">
            <div class="welcome-info">
              <el-avatar :size="64" :src="userInfo.avatar || '/assets/default-avatar.png'" />
              <div class="welcome-text">
                <h2>欢迎回来，{{ userInfo.name }}</h2>
                <p>{{ currentTime }} · 运营管理</p>
              </div>
            </div>
            <div class="welcome-stats">
              <div class="stat-item">
                <div class="stat-value">{{ systemStats.orderCount }}</div>
                <div class="stat-label">本月总订单量</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">¥{{ systemStats.totalGmv }}</div>
                <div class="stat-label">本月总GMV</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">¥{{ systemStats.totalServiceFee }}</div>
                <div class="stat-label">本月总服务费</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="quick-actions" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>快捷操作</h3>
            </div>
          </template>
          <div class="action-buttons">
            <el-button type="primary" @click="navigateTo('/product/list')">
              <el-icon><Edit /></el-icon>编辑费率
            </el-button>
            <el-button type="success" @click="navigateTo('/sample/management')">
              <el-icon><Box /></el-icon>寄样管理
            </el-button>
            <el-button type="warning" @click="navigateTo('/order/search')">
              <el-icon><Document /></el-icon>同步订单
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据概览区域 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="8">
        <el-card class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>商品数据</h3>
              <el-tag size="small" type="primary">本月</el-tag>
            </div>
          </template>
          <div v-if="loading.product" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>
          <div v-else class="data-list">
            <div class="data-item">
              <div class="data-label">总商品数</div>
              <div class="data-value">{{ productStats.totalCount }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">新增商品</div>
              <div class="data-value">{{ productStats.newCount }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">动销商品</div>
              <div class="data-value">{{ productStats.activeCount }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">平均服务费率</div>
              <div class="data-value">{{ productStats.avgServiceFeeRate }}%</div>
            </div>
            <div class="data-item">
              <div class="data-label">平均商家返佣率</div>
              <div class="data-value">{{ productStats.avgMerchantRate }}%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>订单数据</h3>
              <el-tag size="small" type="success">本月</el-tag>
            </div>
          </template>
          <div v-if="loading.order" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>
          <div v-else class="data-list">
            <div class="data-item">
              <div class="data-label">已付款订单</div>
              <div class="data-value">{{ orderStats.paidCount }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">已发货订单</div>
              <div class="data-value">{{ orderStats.shippedCount }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">已结算订单</div>
              <div class="data-value">{{ orderStats.settledCount }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">已失效订单</div>
              <div class="data-value">{{ orderStats.invalidCount }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">平均订单金额</div>
              <div class="data-value">¥{{ orderStats.avgOrderAmount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>用户数据</h3>
              <el-tag size="small" type="warning">本月</el-tag>
            </div>
          </template>
          <div v-if="loading.user" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>
          <div v-else class="data-list">
            <div class="data-item">
              <div class="data-label">商务用户数</div>
              <div class="data-value">{{ userStats.businessCount }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">运营用户数</div>
              <div class="data-value">{{ userStats.operationCount }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">达人总数</div>
              <div class="data-value">{{ userStats.talentCount }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">团长总数</div>
              <div class="data-value">{{ userStats.teamLeaderCount }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">寄样申请数</div>
              <div class="data-value">{{ userStats.sampleCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表和公告区域 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="16">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>平台数据趋势</h3>
              <el-radio-group v-model="chartType" size="small">
                <el-radio-button label="gmv">GMV</el-radio-button>
                <el-radio-button label="serviceFee">服务费</el-radio-button>
                <el-radio-button label="orderCount">订单量</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="chartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="notice-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>系统通知</h3>
              <el-button type="primary" size="small" plain @click="addNotice" v-if="isAdmin">
                <el-icon><Plus /></el-icon>添加通知
              </el-button>
            </div>
          </template>
          <div class="notice-content">
            <div v-for="(notice, index) in notices" :key="index" class="notice-item">
              <div class="notice-title">
                <el-icon><Bell /></el-icon>
                <span>{{ notice.title }}</span>
                <el-button
                  v-if="isAdmin"
                  type="danger"
                  size="small"
                  circle
                  @click="deleteNotice(notice.id)"
                  class="delete-btn"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
              <div class="notice-text">{{ notice.content }}</div>
              <div class="notice-time">{{ notice.time }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加通知对话框 -->
    <el-dialog v-model="noticeDialogVisible" title="添加系统通知" width="500px">
      <el-form :model="noticeForm" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="noticeForm.title" placeholder="请输入通知标题"></el-input>
        </el-form-item>
        <el-form-item label="内容">
          <el-input
            v-model="noticeForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入通知内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="noticeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitNotice" :loading="submittingNotice">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import * as echarts from 'echarts'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Box, Document, Bell, Plus, Delete } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 接口定义
interface NoticeItem {
  id: string
  title: string
  content: string
  time: string
}

// 用户信息
const userInfo = computed(() => {
  // 使用类型断言来解决 TypeScript 错误
  const user = (userStore.user as any) || {}
  return {
    name: user.name || '用户',
    avatar: localStorage.getItem('userAvatar') || '/assets/default-avatar.png',
    role: localStorage.getItem('userRole') || 'operation',
    isAdmin: user.is_admin || false,
  }
})

const isAdmin = computed(() => {
  const role = localStorage.getItem('userRole')
  return role === 'admin'
})

// 当前时间
const currentTime = ref(formatDate(new Date()))
function formatDate(date: Date) {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hours = date.getHours()
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const weekday = weekdays[date.getDay()]
  return `${year}年${month}月${day}日 ${hours}:${minutes} ${weekday}`
}

// 系统统计数据
const systemStats = reactive({
  orderCount: 0,
  totalGmv: '0.00',
  totalServiceFee: '0.00',
})

// 商品统计
const productStats = reactive({
  totalCount: 0,
  newCount: 0,
  activeCount: 0,
  avgServiceFeeRate: 0,
  avgMerchantRate: 0,
})

// 订单统计
const orderStats = reactive({
  paidCount: 0,
  shippedCount: 0,
  settledCount: 0,
  invalidCount: 0,
  avgOrderAmount: '0.00',
})

// 用户统计
const userStats = reactive({
  businessCount: 0,
  operationCount: 0,
  talentCount: 0,
  teamLeaderCount: 0,
  sampleCount: 0,
})

// 加载状态
const loading = reactive({
  product: true,
  order: true,
  user: true,
  chart: true,
})

// 图表相关
const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null
const chartType = ref('gmv')
const chartData = reactive({
  dates: [] as string[],
  gmv: [] as number[],
  serviceFee: [] as number[],
  orderCount: [] as number[],
})

// 公告数据
const notices = ref<NoticeItem[]>([
  {
    id: '1',
    title: '系统更新通知',
    content: '系统将于2025年7月31日进行版本更新，届时将新增更多功能，敬请期待。',
    time: '2025-07-23',
  },
  {
    id: '2',
    title: '业务流程变更',
    content: '自8月1日起，所有寄样申请需提前3个工作日提交，请各位商务注意安排时间。',
    time: '2025-07-22',
  },
])

// 添加通知相关
const noticeDialogVisible = ref(false)
const submittingNotice = ref(false)
const noticeForm = reactive({
  title: '',
  content: '',
})

// 页面跳转
const navigateTo = (path: string) => {
  router.push(path)
}

// 获取系统统计数据
const fetchSystemStats = async () => {
  try {
    const token = localStorage.getItem('token')
    const currentMonth = new Date().toISOString().slice(0, 7) // 格式: YYYY-MM

    const response = await axios.get('/api/home/<USER>', {
      params: { month: currentMonth },
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      const data = response.data.data
      systemStats.orderCount = data.orderCount || 0
      systemStats.totalGmv = data.totalGmv || '0.00'
      systemStats.totalServiceFee = data.totalServiceFee || '0.00'
    }
  } catch (error) {
    console.error('获取系统统计数据失败:', error)
    // 使用模拟数据
    systemStats.orderCount = 1256
    systemStats.totalGmv = '256800.00'
    systemStats.totalServiceFee = '25680.00'
  }
}

// 获取商品统计
const fetchProductStats = async () => {
  loading.product = true
  try {
    const token = localStorage.getItem('token')
    const currentMonth = new Date().toISOString().slice(0, 7) // 格式: YYYY-MM

    const response = await axios.get('/api/home/<USER>', {
      params: { month: currentMonth },
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      const data = response.data.data
      productStats.totalCount = data.totalCount || 0
      productStats.newCount = data.newCount || 0
      productStats.activeCount = data.activeCount || 0
      productStats.avgServiceFeeRate = data.avgServiceFeeRate || 0
      productStats.avgMerchantRate = data.avgMerchantRate || 0
    }
  } catch (error) {
    console.error('获取商品统计失败:', error)
    // 使用模拟数据
    productStats.totalCount = 256
    productStats.newCount = 32
    productStats.activeCount = 128
    productStats.avgServiceFeeRate = 10
    productStats.avgMerchantRate = 20
  } finally {
    loading.product = false
  }
}

// 获取订单统计
const fetchOrderStats = async () => {
  loading.order = true
  try {
    const token = localStorage.getItem('token')
    const currentMonth = new Date().toISOString().slice(0, 7) // 格式: YYYY-MM

    const response = await axios.get('/api/home/<USER>', {
      params: { month: currentMonth },
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      const data = response.data.data
      orderStats.paidCount = data.paidCount || 0
      orderStats.shippedCount = data.shippedCount || 0
      orderStats.settledCount = data.settledCount || 0
      orderStats.invalidCount = data.invalidCount || 0
      orderStats.avgOrderAmount = data.avgOrderAmount || '0.00'
    }
  } catch (error) {
    console.error('获取订单统计失败:', error)
    // 使用模拟数据
    orderStats.paidCount = 450
    orderStats.shippedCount = 380
    orderStats.settledCount = 320
    orderStats.invalidCount = 50
    orderStats.avgOrderAmount = '198.50'
  } finally {
    loading.order = false
  }
}

// 获取用户统计
const fetchUserStats = async () => {
  loading.user = true
  try {
    const token = localStorage.getItem('token')
    const currentMonth = new Date().toISOString().slice(0, 7) // 格式: YYYY-MM

    const response = await axios.get('/api/home/<USER>', {
      params: { month: currentMonth },
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      const data = response.data.data
      userStats.businessCount = data.businessCount || 0
      userStats.operationCount = data.operationCount || 0
      userStats.talentCount = data.talentCount || 0
      userStats.teamLeaderCount = data.teamLeaderCount || 0
      userStats.sampleCount = data.sampleCount || 0
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
    // 使用模拟数据
    userStats.businessCount = 15
    userStats.operationCount = 5
    userStats.talentCount = 120
    userStats.teamLeaderCount = 30
    userStats.sampleCount = 45
  } finally {
    loading.user = false
  }
}

// 获取图表数据
const fetchChartData = async () => {
  loading.chart = true
  try {
    const token = localStorage.getItem('token')
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 30)

    const response = await axios.get('/api/home/<USER>', {
      params: {
        startDate: startDate.toISOString().slice(0, 10),
        endDate: endDate.toISOString().slice(0, 10),
      },
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      const data = response.data.data || []
      chartData.dates = data.map((item: any) => item.date)
      chartData.gmv = data.map((item: any) => item.gmv)
      chartData.serviceFee = data.map((item: any) => item.serviceFee)
      chartData.orderCount = data.map((item: any) => item.orderCount)

      initChart()
    }
  } catch (error) {
    console.error('获取图表数据失败:', error)
    // 使用模拟数据
    const dates = []
    const gmv = []
    const serviceFee = []
    const orderCount = []

    const endDate = new Date()
    for (let i = 14; i >= 0; i--) {
      const date = new Date()
      date.setDate(endDate.getDate() - i)
      dates.push(date.toISOString().slice(5, 10))
      gmv.push(Math.floor(Math.random() * 10000 + 10000))
      serviceFee.push(Math.floor(Math.random() * 1000 + 1000))
      orderCount.push(Math.floor(Math.random() * 40 + 20))
    }

    chartData.dates = dates
    chartData.gmv = gmv
    chartData.serviceFee = serviceFee
    chartData.orderCount = orderCount

    initChart()
  } finally {
    loading.chart = false
  }
}

// 获取通知列表
const fetchNotices = async () => {
  try {
    const token = localStorage.getItem('token')

    const response = await axios.get('/api/home/<USER>', {
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      notices.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取通知列表失败:', error)
    // 使用默认通知数据
  }
}

// 添加通知
const addNotice = () => {
  noticeForm.title = ''
  noticeForm.content = ''
  noticeDialogVisible.value = true
}

// 提交通知
const submitNotice = async () => {
  if (!noticeForm.title || !noticeForm.content) {
    ElMessage.warning('请填写完整的通知信息')
    return
  }

  submittingNotice.value = true
  try {
    const token = localStorage.getItem('token')

    const response = await axios.post(
      '/api/home/<USER>',
      {
        title: noticeForm.title,
        content: noticeForm.content,
      },
      {
        headers: { Authorization: `Bearer ${token}` },
      },
    )

    if (response.data.code === 200) {
      ElMessage.success('添加通知成功')
      noticeDialogVisible.value = false
      fetchNotices()
    } else {
      ElMessage.error(response.data.message || '添加通知失败')
    }
  } catch (error) {
    console.error('添加通知失败:', error)
    ElMessage.error('添加通知失败')
  } finally {
    submittingNotice.value = false
  }
}

// 删除通知
const deleteNotice = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这条通知吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const token = localStorage.getItem('token')

    const response = await axios.delete(`/api/home/<USER>/${id}`, {
      headers: { Authorization: `Bearer ${token}` },
    })

    if (response.data.code === 200) {
      ElMessage.success('删除通知成功')
      fetchNotices()
    } else {
      ElMessage.error(response.data.message || '删除通知失败')
    }
  } catch (error) {
    console.error('删除通知失败:', error)
    // 用户取消操作，不做处理
  }
}

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
    updateChart()

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', handleResize)
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  let seriesData: number[] = []
  let yAxisName = ''

  switch (chartType.value) {
    case 'gmv':
      seriesData = chartData.gmv
      yAxisName = 'GMV (元)'
      break
    case 'serviceFee':
      seriesData = chartData.serviceFee
      yAxisName = '服务费 (元)'
      break
    case 'orderCount':
      seriesData = chartData.orderCount
      yAxisName = '订单量'
      break
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        const data = params[0]
        const value = chartType.value === 'orderCount' ? data.value : `¥${data.value.toFixed(2)}`
        return `${data.name}<br/>${data.seriesName}: ${value}`
      },
    },
    xAxis: {
      type: 'category',
      data: chartData.dates,
      axisLabel: {
        interval: Math.floor(chartData.dates.length / 7),
      },
    },
    yAxis: {
      type: 'value',
      name: yAxisName,
      axisLabel: {
        formatter: function (value: number) {
          return chartType.value === 'orderCount' ? value : `¥${value}`
        },
      },
    },
    series: [
      {
        name:
          chartType.value === 'gmv'
            ? 'GMV'
            : chartType.value === 'serviceFee'
              ? '服务费'
              : '订单量',
        type: 'line',
        data: seriesData,
        smooth: true,
        lineStyle: {
          width: 3,
          color:
            chartType.value === 'gmv'
              ? '#409EFF'
              : chartType.value === 'serviceFee'
                ? '#67C23A'
                : '#E6A23C',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color:
                chartType.value === 'gmv'
                  ? 'rgba(64, 158, 255, 0.7)'
                  : chartType.value === 'serviceFee'
                    ? 'rgba(103, 194, 58, 0.7)'
                    : 'rgba(230, 162, 60, 0.7)',
            },
            {
              offset: 1,
              color:
                chartType.value === 'gmv'
                  ? 'rgba(64, 158, 255, 0.1)'
                  : chartType.value === 'serviceFee'
                    ? 'rgba(103, 194, 58, 0.1)'
                    : 'rgba(230, 162, 60, 0.1)',
            },
          ]),
        },
      },
    ],
  }

  chartInstance.setOption(option)
}

// 处理窗口大小变化
const handleResize = () => {
  chartInstance?.resize()
}

// 监听图表类型变化
watch(chartType, () => {
  updateChart()
})

// 定时更新当前时间
let timeInterval: number | null = null

onMounted(() => {
  fetchSystemStats()
  fetchProductStats()
  fetchOrderStats()
  fetchUserStats()
  fetchChartData()
  fetchNotices()

  // 每分钟更新一次时间
  timeInterval = setInterval(() => {
    currentTime.value = formatDate(new Date())
  }, 60000) as unknown as number
})

onUnmounted(() => {
  // 清除定时器
  if (timeInterval !== null) {
    clearInterval(timeInterval)
  }

  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)

  // 销毁图表实例
  chartInstance?.dispose()
})
</script>

<style scoped>
.home-container {
  padding: 20px;
  height: 100%;
  width: 100%;
  overflow: auto;
  box-sizing: border-box;
}

.mt-20 {
  margin-top: 20px;
}

/* 欢迎卡片样式 */
.welcome-card {
  height: 100%;
}

.welcome-header {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.welcome-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.welcome-text h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.welcome-text p {
  margin: 8px 0 0;
  color: #909399;
  font-size: 14px;
}

.welcome-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.stat-item {
  text-align: center;
  padding: 10px 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 快捷操作卡片样式 */
.quick-actions {
  height: 100%;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-buttons .el-button {
  width: 100%;
  justify-content: flex-start;
  padding: 15px;
  font-size: 16px;
}

.action-buttons .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* 数据卡片样式 */
.data-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.loading-container,
.empty-data {
  padding: 20px 0;
  text-align: center;
  color: #909399;
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.data-item:hover {
  transform: translateX(5px);
  background-color: #ecf5ff;
}

.data-label {
  font-weight: 500;
  color: #606266;
}

.data-value {
  font-weight: bold;
  color: #409eff;
}

/* 图表卡片样式 */
.chart-card {
  height: 100%;
}

.chart-container {
  height: 300px;
  width: 100%;
}

/* 公告卡片样式 */
.notice-card {
  height: 100%;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.notice-item {
  padding: 10px;
  border-radius: 8px;
  background-color: #f5f7fa;
  transition: all 0.3s;
  position: relative;
}

.notice-item:hover {
  background-color: #ecf5ff;
}

.notice-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #303133;
}

.notice-text {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 8px;
}

.notice-time {
  text-align: right;
  color: #909399;
  font-size: 12px;
}

.delete-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  opacity: 0;
  transition: opacity 0.3s;
}

.notice-item:hover .delete-btn {
  opacity: 1;
}
</style>
