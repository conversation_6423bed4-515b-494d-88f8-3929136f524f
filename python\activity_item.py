import requests
import json
from datetime import datetime

def fetch_activity_items(cookie, activity_id, page_size=10, current_page=1):
    """
    爬取快手小店活动商品列表数据的函数
    
    参数:
    cookie: str - 请求的cookie字符串
    activity_id: int - 活动ID
    page_size: int - 每页数据条数
    current_page: int - 当前页码
    
    返回:
    dict - 处理后的活动商品数据
    """
    url = "https://cps.kwaixiaodian.com/gateway/distribute/platform/investment/activity/item/list"
    
    # 构建请求参数
    payload = {
        "pageSize": page_size,
        "current": current_page,
        "invalidTime": 0,
        "baseOrderStatus": 0,
        "offset": (current_page - 1) * page_size,
        "limit": page_size,
        "activityId": activity_id
    }
    
    headers = {
        "authority": "cps.kwaixiaodian.com",
        "accept": "application/json",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "cookie": cookie,
        "kpf": "PC_WEB",
        "kpn": "KWAIXIAODIAN",
        "referer": "https://cps.kwaixiaodian.com/pc/leader/base/order-manage",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"
    }
    
    try:
        response = requests.get(url, params=payload, headers=headers)
        response.raise_for_status()
        
        # 解析响应数据
        result = response.json()
        
        # 检查响应是否成功
        if result.get("result") != 1:
            return {"error": result.get("error_msg", "未知错误")}
        
        # 提取商品列表
        items = result.get("data", [])
        
        # 处理数据，添加可读的时间格式
        for item in items:
            if "invalidTime" in item and item["invalidTime"] > 0:
                timestamp = item["invalidTime"]
                item["invalidTimeFormatted"] = datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')
        
        return {
            "items": items,
            "total": result.get("total", 0),
            "current_page": current_page,
            "page_size": page_size
        }
        
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}

def get_all_activity_items(cookie, activity_id, page_size=10, max_pages=None):
    """
    获取所有活动商品数据（分页获取）
    
    参数:
    cookie: str - 请求的cookie字符串
    activity_id: int - 活动ID
    page_size: int - 每页数据条数
    max_pages: int - 最大获取页数，None表示获取所有页
    
    返回:
    list - 所有活动商品数据
    """
    all_items = []
    current_page = 1
    total = None
    
    while True:
        result = fetch_activity_items(
            cookie=cookie,
            activity_id=activity_id,
            page_size=page_size,
            current_page=current_page
        )
        
        if "error" in result:
            break
        
        items = result.get("items", [])
        all_items.extend(items)
        
        if total is None:
            total = result.get("total", 0)
        
        
        # 判断是否继续获取下一页
        if max_pages and current_page >= max_pages:
            break
        
        if total is not None and len(all_items) >= int(total):
            break
        
        current_page += 1
    
    return all_items

def save_items_to_file(items, filename="activity_items.json"):
    """
    将活动商品数据保存到文件
    
    参数:
    items: list - 活动商品数据列表
    filename: str - 保存的文件名
    """
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(items, f, ensure_ascii=False, indent=2)
    print(f"活动商品数据已保存至 {filename}")

# 使用示例
if __name__ == "__main__":
    # 示例cookie，实际使用时需要替换
    cookie = "_did=web_6109536701C516C8; did=web_m5p31mdccrdqpmzfrlb5k0pb18ya7tn1; sid=kuaishou.shop.b; bUserId=1000040627146; userId=2885180614; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAE3tBozHFVZOn8pljtRICePPkO5r0KudHnwAKYs6cKFeR4iZ2kuwe3aY_uTEPJciQgEY7R3cPMo-Zs_Z83AQerRWidPl8vDub4WQdp_YD1EFwv1BmSppwUKKxrfyFD1MAMI0oFHlLPVdpSQK-m5yfSBSPE5AvYJkQ1QKZyRMXgmVs1RE4_wHZLmfACJibZ6mUJZC8jixMJ0KY1-1xjH83blGhISqzP1A3OvUjEirVmZwcinEd4iIJfDUuUOMJQnmTnc3NWgIAQd3LEVHp0oaIZSZ-gNVIyEKAUwAQ; kuaishou.shop.b_ph=70bed43b4a2ba56eb32fc3df6bcdff0a7ba5"
    
    # 活动ID
    activity_id = 9369212614
    
    # 获取单页数据示例
    result = fetch_activity_items(cookie, activity_id)
    if "error" not in result:
        print(f"获取到 {len(result.get('items', []))} 条活动商品数据")
    
    # 获取所有数据示例
    all_items = get_all_activity_items(cookie, activity_id)
    save_items_to_file(all_items) 