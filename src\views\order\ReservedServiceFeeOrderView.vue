<template>
  <div class="order-search-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-button v-if="isOperationRole" type="primary" @click="openSyncDialog" size="small"
            >同步订单数据</el-button
          >
        </div>
      </template>

      <!-- 选项卡 -->
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="order-tabs">
        <el-tab-pane label="预留服务费收入订单" name="income">
          <!-- 搜索区域 -->
          <div class="search-area">
            <!-- 原有搜索表单 -->
            <el-form :inline="true" :model="searchForm" class="search-form">
              <el-row :gutter="16">
                <el-col :span="6">
                  <el-form-item label="订单ID">
                    <el-input v-model="searchForm.orderId" placeholder="请输入订单ID" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="活动ID">
                    <el-input
                      v-model="searchForm.activityId"
                      placeholder="请输入活动ID"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商家ID">
                    <el-input
                      v-model="searchForm.merchantId"
                      placeholder="请输入商家ID"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商品ID">
                    <el-input v-model="searchForm.productId" placeholder="请输入商品ID" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="16">
                <el-col :span="6">
                  <el-form-item label="订单状态">
                    <el-select v-model="searchForm.status" placeholder="请选择订单状态" clearable>
                      <el-option label="全部" value="" />
                      <el-option label="已付款" value="30" />
                      <el-option label="已收货" value="50" />
                      <el-option label="已结算" value="60" />
                      <el-option label="已失效" value="80" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="下单时间">
                    <el-date-picker
                      v-model="searchForm.dateRange"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      format="YYYY-MM-DD HH:mm:ss"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="是否发货">
                    <el-select
                      v-model="searchForm.isShipped"
                      placeholder="请选择发货状态"
                      clearable
                    >
                      <el-option label="全部" value="" />
                      <el-option label="已发货" :value="1" />
                      <el-option label="未发货" :value="0" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="推广者类型">
                    <el-select
                      v-model="searchForm.promoterType"
                      placeholder="请选择推广者类型"
                      clearable
                    >
                      <el-option label="全部" value="" />
                      <el-option label="达人" value="1" />
                      <el-option label="团长" value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="16">
                <el-col :span="6">
                  <el-form-item label="达人ID">
                    <el-input v-model="searchForm.talentId" placeholder="请输入达人ID" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="团长ID">
                    <el-input
                      v-model="searchForm.teamLeaderId"
                      placeholder="请输入团长ID"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12" style="text-align: right">
                  <el-button @click="resetSearch">重置</el-button>
                  <el-button type="primary" @click="handleSearch">查询</el-button>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <!-- 业绩统计区域 -->
          <div class="stats-area" v-if="orderStats">
            <h4>预留服务费收入订单业绩统计</h4>
            <div class="stats-grid">
              <div class="stats-item">
                <div class="stats-label">订单量</div>
                <div class="stats-value">{{ orderStats.order_count || 0 }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">支付金额</div>
                <div class="stats-value">¥{{ formatNumber(orderStats.total_payment) }}</div>
              </div>

              <div class="stats-item">
                <div class="stats-label">预估预留服务费</div>
                <div class="stats-value">
                  ¥{{ formatNumber(orderStats.estimated_reserved_fee) }}
                </div>
              </div>
              <div class="stats-item">
                <div class="stats-label">结算预留服务费</div>
                <div class="stats-value">¥{{ formatNumber(orderStats.settled_reserved_fee) }}</div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="预留服务费支出订单" name="expense">
          <!-- 搜索区域 -->
          <div class="search-area">
            <!-- 原有搜索表单 -->
            <el-form :inline="true" :model="searchForm" class="search-form">
              <el-row :gutter="16">
                <el-col :span="6">
                  <el-form-item label="订单ID">
                    <el-input v-model="searchForm.orderId" placeholder="请输入订单ID" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="活动ID">
                    <el-input
                      v-model="searchForm.activityId"
                      placeholder="请输入活动ID"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商家ID">
                    <el-input
                      v-model="searchForm.merchantId"
                      placeholder="请输入商家ID"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商品ID">
                    <el-input v-model="searchForm.productId" placeholder="请输入商品ID" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="16">
                <el-col :span="6">
                  <el-form-item label="订单状态">
                    <el-select v-model="searchForm.status" placeholder="请选择订单状态" clearable>
                      <el-option label="全部" value="" />
                      <el-option label="已付款" value="30" />
                      <el-option label="已收货" value="50" />
                      <el-option label="已结算" value="60" />
                      <el-option label="已失效" value="80" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="下单时间">
                    <el-date-picker
                      v-model="searchForm.dateRange"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      format="YYYY-MM-DD HH:mm:ss"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="是否发货">
                    <el-select
                      v-model="searchForm.isShipped"
                      placeholder="请选择发货状态"
                      clearable
                    >
                      <el-option label="全部" value="" />
                      <el-option label="已发货" :value="1" />
                      <el-option label="未发货" :value="0" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="团长ID">
                    <el-input
                      v-model="searchForm.teamLeaderId"
                      placeholder="请输入团长ID"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" style="text-align: right">
                  <el-button @click="resetSearch">重置</el-button>
                  <el-button type="primary" @click="handleSearch">查询</el-button>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <!-- 业绩统计区域 -->
          <div class="stats-area" v-if="orderStats">
            <h4>预留服务费支出订单业绩统计</h4>
            <div class="stats-grid">
              <div class="stats-item">
                <div class="stats-label">订单量</div>
                <div class="stats-value">{{ orderStats.order_count || 0 }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">支付金额</div>
                <div class="stats-value">¥{{ formatNumber(orderStats.total_payment) }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">预估服务费</div>
                <div class="stats-value">¥{{ formatNumber(orderStats.estimated_service_fee) }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">结算服务费</div>
                <div class="stats-value">¥{{ formatNumber(orderStats.settled_service_fee) }}</div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 批量操作区域 -->
      <div class="batch-operation-area" v-if="selectedOrders.length > 0">
        <div class="selected-info">已选择 {{ selectedOrders.length }} 条订单</div>
        <el-button type="primary" @click="openBatchEditDialog" size="small">
          批量编辑商务
        </el-button>
        <el-button @click="clearSelection" size="small">取消选择</el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        @selection-change="handleSelectionChange"
        ref="orderTable"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="订单信息" min-width="300">
          <template #default="scope">
            <div class="product-card">
              <div class="product-image">
                <el-image
                  v-if="scope.row.product_image_url"
                  :src="scope.row.product_image_url"
                  fit="cover"
                >
                  <template #error>
                    <div class="image-placeholder">
                      <i class="el-icon-picture"></i>
                    </div>
                  </template>
                </el-image>
                <div v-else class="image-placeholder">
                  <i class="el-icon-picture"></i>
                </div>
              </div>
              <div class="product-info">
                <div class="order-id">订单ID: {{ scope.row.order_id }}</div>
                <div class="product-name">{{ scope.row.product_name || '未知商品' }}</div>
                <div class="product-id">商品ID: {{ scope.row.product_id }}</div>
                <div class="product-price">售价: ¥{{ scope.row.product_price }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status_text" label="订单状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status_text)">{{
              scope.row.status_text
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否发货" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_shipped ? 'success' : 'warning'">
              {{ scope.row.is_shipped ? '已发货' : '未发货' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="order_time" label="下单时间" width="180" />
        <el-table-column prop="shop_id" label="商家ID" width="120" />
        <el-table-column label="推广者信息" min-width="200">
          <template #default="scope">
            <div class="promotion-card">
              <div class="promotion-item">
                <span class="promotion-label">类型:</span>
                <span class="promotion-value">{{ scope.row.promotion_type_text || '-' }}</span>
              </div>
              <div class="promotion-item">
                <span class="promotion-label">推广者:</span>
                <span class="promotion-value"
                  >{{ scope.row.promoter_name || '-' }} ({{ scope.row.promoter_id || '-' }})</span
                >
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="payment_amount" label="支付金额" width="120">
          <template #default="scope">
            <span class="price">¥{{ scope.row.payment_amount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="对接商务" width="120">
          <template #default="scope">
            <span>{{ scope.row.talent_business || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="招商运营" width="120">
          <template #default="scope">
            <span>{{ scope.row.business_operation || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="达人佣金" width="120">
          <template #default="scope">
            <span class="commission">¥{{ scope.row.talent_commission || '0' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="预留服务费" :min-width="activeTab === 'income' ? 250 : 200">
          <template #default="scope">
            <div class="fee-card">
              <div class="fee-item" v-if="activeTab === 'income'">
                <span class="fee-label">预留服务费率:</span>
                <span class="fee">{{ scope.row.reserved_service_fee_rate || '0' }}%</span>
              </div>
              <div class="fee-item" v-if="activeTab === 'expense'">
                <span class="fee-label">服务费率:</span>
                <span class="fee">{{ scope.row.service_fee_rate || '0' }}%</span>
              </div>
              <div class="fee-item" v-if="activeTab === 'income'">
                <span class="fee-label">预估预留服务费:</span>
                <span class="fee">¥{{ scope.row.estimated_reserved_service_fee || '0' }}</span>
              </div>
              <div class="fee-item" v-if="activeTab === 'income'">
                <span class="fee-label">结算预留服务费:</span>
                <span class="fee">¥{{ scope.row.settled_reserved_service_fee || '0' }}</span>
              </div>
              <div class="fee-item" v-if="activeTab === 'expense'">
                <span class="fee-label">预估服务费:</span>
                <span class="fee">¥{{ scope.row.estimated_service_fee }}</span>
              </div>
              <div class="fee-item" v-if="activeTab === 'expense'">
                <span class="fee-label">结算服务费:</span>
                <span class="fee">¥{{ scope.row.actual_service_fee }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 同步订单数据对话框 -->
    <el-dialog v-model="syncDialogVisible" title="同步订单数据" width="500px">
      <el-form :model="syncForm" label-width="100px">
        <el-form-item label="订单类型" required>
          <el-radio-group v-model="syncForm.fundType">
            <el-radio :label="1">服务费收入订单</el-radio>
            <el-radio :label="2">服务费支出订单</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间范围" required>
          <el-date-picker
            v-model="syncForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="syncDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSyncOrders" :loading="syncLoading">
            确认同步
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量编辑商务对话框 -->
    <el-dialog v-model="batchEditDialogVisible" title="批量编辑商务" width="400px">
      <el-form :model="batchEditForm" label-width="80px">
        <el-form-item label="选择商务" required>
          <el-select
            v-model="batchEditForm.businessName"
            placeholder="请选择商务人员"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="user in businessUsers"
              :key="user.id"
              :label="user.name"
              :value="user.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <div class="selected-orders-info">已选择 {{ selectedOrders.length }} 条订单</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeBatchEditDialog">取消</el-button>
          <el-button type="primary" @click="handleBatchEdit" :loading="batchEditLoading">
            确认编辑
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

// 当前激活的选项卡
const activeTab = ref('income')

// 当前用户信息
const currentUser = ref(null)

// 用户权限
const userPermissions = ref({
  order: {
    data: [],
    operations: [],
    modules: [],
  },
})

// 权限检查方法
const hasDataPermission = (permission) => {
  return userPermissions.value.order.data.includes(permission)
}

const hasOperationPermission = (permission) => {
  return userPermissions.value.order.operations.includes(permission)
}

const hasModulePermission = (permission) => {
  return userPermissions.value.order.modules.includes(permission)
}

// 便捷权限检查
const canViewOwnDataOnly = computed(() => hasDataPermission('own_only'))
const canViewAllData = computed(() => hasDataPermission('all_data'))
const canEditBusiness = computed(() => hasOperationPermission('edit_business'))
const canAccessReservedServiceFeeOrders = computed(() =>
  hasModulePermission('reserved_service_fee_orders'),
)

// 角色判断
const isOperationRole = computed(() => {
  return currentUser.value?.role === 'operation' || currentUser.value?.role === 'admin'
})

const isBusinessRole = computed(() => {
  return currentUser.value?.role === 'business'
})

// 获取当前登录用户信息
const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      currentUser.value = JSON.parse(userStr)

      // 解析权限信息
      if (currentUser.value.permissions) {
        try {
          const permissions =
            typeof currentUser.value.permissions === 'string'
              ? JSON.parse(currentUser.value.permissions)
              : currentUser.value.permissions
          userPermissions.value = permissions
        } catch (permError) {
          console.error('解析权限信息失败:', permError)
          // 使用默认权限
          userPermissions.value = {
            order: { data: [], operations: [], modules: [] },
          }
        }
      }

      console.log('当前登录用户:', currentUser.value)
      console.log('订单模块权限:', userPermissions.value.order)
    } else {
      console.error('未找到登录用户信息')
      ElMessage.warning('未找到登录用户信息，请重新登录')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请重新登录')
  }
}

// 获取默认时间范围（近一周）
const getDefaultDateRange = () => {
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - 6) // 6天前，包含今天共7天

  const formatStartDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day} 00:00:00`
  }

  const formatEndDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day} 23:59:59`
  }

  return [formatStartDate(start), formatEndDate(end)]
}

// 搜索表单
const searchForm = reactive({
  orderId: '',
  productId: '',
  merchantId: '',
  status: '',
  dateRange: getDefaultDateRange(), // 默认近一周
  talentId: '',
  teamLeaderId: '',
  promoterType: '',
  isShipped: '',
  activityId: '',
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 添加业绩统计数据
const orderStats = ref(null)

// 批量编辑相关数据
const selectedOrders = ref([])
const batchEditDialogVisible = ref(false)
const businessUsers = ref([])
const batchEditForm = reactive({
  businessName: '',
})
const batchEditLoading = ref(false)

// 格式化数字
const formatNumber = (num) => {
  if (num === undefined || num === null) return '0.00'
  return parseFloat(num).toFixed(2)
}

// 同步订单数据
const syncDialogVisible = ref(false)
const syncLoading = ref(false)
const syncForm = reactive({
  dateRange: [],
  fundType: 1, // 默认同步收入订单
})

// 获取状态标签类型
const getStatusType = (status) => {
  switch (status) {
    case '已付款':
      return 'info'
    case '已发货':
      return 'primary'
    case '已收货':
      return 'success'
    case '已结算':
      return 'success'
    case '已失效':
      return 'danger'
    default:
      return 'info'
  }
}

// 选项卡切换处理
const handleTabChange = (tabName) => {
  activeTab.value = tabName
  // 切换选项卡时只重置部分搜索条件，保持时间范围
  searchForm.orderId = ''
  searchForm.productId = ''
  searchForm.merchantId = ''
  searchForm.status = ''
  searchForm.talentId = ''
  searchForm.teamLeaderId = ''
  searchForm.promoterType = ''
  searchForm.isShipped = ''
  searchForm.activityId = ''
  currentPage.value = 1
  handleSearch()
}

// 打开同步对话框
const openSyncDialog = () => {
  syncDialogVisible.value = true
}

// 同步订单数据
const handleSyncOrders = async () => {
  if (!syncForm.dateRange || syncForm.dateRange.length !== 2) {
    ElMessage.warning('请选择时间范围')
    return
  }

  try {
    syncLoading.value = true
    const startTime = syncForm.dateRange[0]
    const endTime = syncForm.dateRange[1]

    const response = await axios.post('/api/order/sync', {
      startTime,
      endTime,
      fundType: syncForm.fundType,
    })

    if (response.data.code === 0) {
      ElMessage.success('同步成功')
      syncDialogVisible.value = false
      handleSearch()
    } else {
      ElMessage.error(response.data.message || '同步失败')
    }
  } catch (error) {
    ElMessage.error('同步订单数据失败，请稍后重试')
  } finally {
    syncLoading.value = false
  }
}

// 搜索
const handleSearch = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      fundType: activeTab.value === 'income' ? 1 : 2, // 根据选项卡设置资金类型
    }

    if (searchForm.orderId) params.orderId = searchForm.orderId
    if (searchForm.productId) params.productId = searchForm.productId
    if (searchForm.merchantId) params.merchantId = searchForm.merchantId
    if (searchForm.status) params.status = searchForm.status
    if (searchForm.talentId) params.talentId = searchForm.talentId
    if (searchForm.teamLeaderId) params.teamLeaderId = searchForm.teamLeaderId
    if (searchForm.promoterType) params.promoterType = searchForm.promoterType
    if (searchForm.isShipped !== '') params.isShipped = searchForm.isShipped
    if (searchForm.activityId) params.activityId = searchForm.activityId

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0].split(' ')[0]
      params.endDate = searchForm.dateRange[1].split(' ')[0]
    }

    // 商务角色需要添加商务参数
    if (isBusinessRole.value && currentUser.value?.username) {
      params.businessName = currentUser.value.username
    }

    // 统一使用普通接口
    const response = await axios.get('/api/order/reserved-service-fee-orders', { params })

    if (response.data.code === 0) {
      tableData.value = response.data.data.orders || []
      total.value = response.data.data.total || 0
      // 保存业绩统计数据
      orderStats.value = response.data.data.stats || null
    } else {
      ElMessage.error(response.data.message || '获取订单列表失败')
    }
  } catch (error) {
    ElMessage.error('获取订单列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.orderId = ''
  searchForm.productId = ''
  searchForm.merchantId = ''
  searchForm.status = ''
  searchForm.dateRange = getDefaultDateRange() // 重置为默认近一周
  searchForm.talentId = ''
  searchForm.teamLeaderId = ''
  searchForm.promoterType = ''
  searchForm.isShipped = ''
  searchForm.activityId = ''
  currentPage.value = 1
  handleSearch()
}

// 分页大小改变
const handleSizeChange = (val) => {
  pageSize.value = val
  handleSearch()
}

// 页码改变
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleSearch()
}

// 批量编辑相关方法
const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

const clearSelection = () => {
  selectedOrders.value = []
  if (orderTable.value) {
    orderTable.value.clearSelection()
  }
}

const openBatchEditDialog = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请先选择要编辑的订单')
    return
  }

  // 获取商务用户列表
  try {
    const response = await axios.get('/api/order/business-users')
    if (response.data.code === 0) {
      businessUsers.value = response.data.data
      batchEditDialogVisible.value = true
    } else {
      ElMessage.error(response.data.message || '获取商务用户列表失败')
    }
  } catch (error) {
    console.error('获取商务用户列表失败:', error)
    ElMessage.error('获取商务用户列表失败')
  }
}

const handleBatchEdit = async () => {
  if (!batchEditForm.businessName) {
    ElMessage.warning('请选择商务人员')
    return
  }

  batchEditLoading.value = true
  try {
    const orderIds = selectedOrders.value.map((order) => order.order_id)
    const response = await axios.post('/api/order/batch-edit-business', {
      orderIds,
      businessName: batchEditForm.businessName,
    })

    if (response.data.code === 0) {
      ElMessage.success(response.data.message)
      batchEditDialogVisible.value = false
      batchEditForm.businessName = ''
      clearSelection()
      handleSearch() // 刷新表格数据
    } else {
      ElMessage.error(response.data.message || '批量编辑失败')
    }
  } catch (error) {
    console.error('批量编辑失败:', error)
    ElMessage.error('批量编辑失败')
  } finally {
    batchEditLoading.value = false
  }
}

const closeBatchEditDialog = () => {
  batchEditDialogVisible.value = false
  batchEditForm.businessName = ''
}

// 添加表格引用
const orderTable = ref(null)

onMounted(() => {
  getCurrentUser() // 获取当前用户信息
  handleSearch()
})
</script>

<style scoped>
.order-search-container {
  padding: 15px;
}

.card-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.order-tabs {
  margin-top: -10px;
}

.search-area {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-form .el-form-item {
  margin-bottom: 10px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.product-card {
  display: flex;
  align-items: center;
}

.product-image {
  width: 60px;
  height: 60px;
  margin-right: 12px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ebeef5;
}

.product-image .el-image {
  width: 100%;
  height: 100%;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.order-id {
  font-weight: 600;
  margin-bottom: 4px;
  color: #303133;
}

.product-name {
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-id,
.product-price {
  font-size: 12px;
  color: #909399;
}

.price {
  color: #f56c6c;
  font-weight: 500;
}

.fee,
.commission {
  color: #409eff;
  font-weight: 500;
}

.fee-card {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
  transition: all 0.3s;
}

.fee-card:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fee-label {
  color: #606266;
  font-size: 13px;
}

.promotion-card {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border-left: 3px solid #67c23a;
  transition: all 0.3s;
}

.promotion-card:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.promotion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.promotion-label {
  color: #606266;
  font-size: 13px;
}

.promotion-value {
  color: #409eff;
  font-size: 13px;
  font-weight: 500;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table__row:hover td) {
  background-color: #f5f7fa !important;
}

:deep(.el-pagination) {
  padding: 10px 0;
}

/* 对话框样式优化 */
:deep(.el-dialog__header) {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 15px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

/* 添加业绩统计样式 */
.stats-area {
  margin: 15px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.stats-area h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
}

.stats-item {
  background-color: #fff;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.stats-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-label {
  font-size: 13px;
  color: #606266;
  margin-bottom: 5px;
}

.stats-value {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
}

/* 支出订单统计项样式调整 */
.el-tab-pane[label='服务费支出订单'] .stats-grid {
  grid-template-columns: repeat(4, 1fr);
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 批量操作区域样式 */
.batch-operation-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 16px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
}

.selected-info {
  color: #1890ff;
  font-weight: 500;
}

.selected-orders-info {
  color: #606266;
  font-size: 14px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: center;
}
</style>
