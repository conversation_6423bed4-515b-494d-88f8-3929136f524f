from flask import Blueprint, request, jsonify
import jwt
import os
from app.utils.db_utils import get_connection

# JWT密钥
SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')

tags_bp = Blueprint('tags', __name__, url_prefix='/api/tags')

def check_tag_permission(token):
    """检查标签管理权限"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
        user_role = payload.get('role', 'business')
        user_id = payload.get('user_id')
        
        # 管理员有所有权限
        if user_role == 'admin':
            return True, user_id, user_role
        
        # 检查运营用户是否有标签管理权限
        if user_role == 'operation':
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)
            try:
                perm_query = f"SELECT perm_manage_tags FROM business_user WHERE id = {user_id}"
                cursor.execute(perm_query)
                perm_result = cursor.fetchone()

                if perm_result and perm_result['perm_manage_tags'] == 1:
                    return True, user_id, user_role
            finally:
                cursor.close()
                connection.close()
        
        return False, None, None
        
    except jwt.ExpiredSignatureError:
        return False, None, None
    except jwt.InvalidTokenError:
        return False, None, None

@tags_bp.route('/list', methods=['GET'])
def get_tags():
    """获取标签列表"""
    connection = None
    cursor = None
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        has_permission, user_id, user_role = check_tag_permission(token)
        
        if not has_permission:
            return jsonify({
                'code': 403,
                'message': '权限不足，无法查看标签',
                'data': None
            }), 403
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 获取标签列表
        query = """
        SELECT id, name, color, description, created_by,
               DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
               DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time
        FROM product_tags
        ORDER BY create_time DESC
        """
        cursor.execute(query)
        tags = cursor.fetchall()
        
        return jsonify({
            'code': 0,
            'message': '获取标签列表成功',
            'data': tags
        })
        
    except Exception as e:
        print(f"获取标签列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取标签列表失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@tags_bp.route('/create', methods=['POST'])
def create_tag():
    """创建标签"""
    connection = None
    cursor = None
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        has_permission, user_id, user_role = check_tag_permission(token)

        if not has_permission:
            return jsonify({
                'code': 403,
                'message': '权限不足，无法创建标签',
                'data': None
            }), 403

        # 获取请求数据
        data = request.json
        if not data or 'name' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供标签名称',
                'data': None
            }), 400
        
        name = data['name'].strip()
        color = data.get('color', '#409EFF')
        description = data.get('description', '')

        if not name:
            return jsonify({
                'code': 400,
                'message': '标签名称不能为空',
                'data': None
            }), 400

        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 转义单引号
        safe_name = name.replace("'", "''")
        safe_color = color.replace("'", "''")
        safe_description = description.replace("'", "''")

        # 检查标签名是否已存在
        check_query = f"SELECT id FROM product_tags WHERE name = '{safe_name}'"
        cursor.execute(check_query)
        existing_tag = cursor.fetchone()

        if existing_tag:
            return jsonify({
                'code': 1,
                'message': '标签名称已存在',
                'data': None
            })

        # 创建标签
        insert_query = f"""
        INSERT INTO product_tags (name, color, description, created_by, create_time, update_time)
        VALUES ('{safe_name}', '{safe_color}', '{safe_description}', {user_id}, NOW(), NOW())
        """
        cursor.execute(insert_query)
        tag_id = cursor.lastrowid
        connection.commit()

        # 获取新创建的标签信息
        query = f"""
        SELECT id, name, color, description, created_by,
               DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
               DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time
        FROM product_tags WHERE id = {tag_id}
        """
        cursor.execute(query)
        new_tag = cursor.fetchone()
        
        return jsonify({
            'code': 0,
            'message': '创建标签成功',
            'data': new_tag
        })
        
    except Exception as e:
        print(f"创建标签失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'创建标签失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@tags_bp.route('/update/<int:tag_id>', methods=['PUT'])
def update_tag(tag_id):
    """更新标签"""
    connection = None
    cursor = None
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        has_permission, user_id, user_role = check_tag_permission(token)
        
        if not has_permission:
            return jsonify({
                'code': 403,
                'message': '权限不足，无法更新标签',
                'data': None
            }), 403
        
        # 获取请求数据
        data = request.json
        if not data:
            return jsonify({
                'code': 400,
                'message': '请提供更新数据',
                'data': None
            }), 400
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查标签是否存在
        check_query = f"SELECT id FROM product_tags WHERE id = {tag_id}"
        cursor.execute(check_query)
        existing_tag = cursor.fetchone()

        if not existing_tag:
            return jsonify({
                'code': 404,
                'message': '标签不存在',
                'data': None
            }), 404

        # 构建更新查询
        update_fields = []

        if 'name' in data:
            name = data['name'].strip()
            if not name:
                return jsonify({
                    'code': 400,
                    'message': '标签名称不能为空',
                    'data': None
                }), 400

            # 转义单引号
            safe_name = name.replace("'", "''")

            # 检查名称是否与其他标签重复
            name_check_query = f"SELECT id FROM product_tags WHERE name = '{safe_name}' AND id != {tag_id}"
            cursor.execute(name_check_query)
            if cursor.fetchone():
                return jsonify({
                    'code': 1,
                    'message': '标签名称已存在',
                    'data': None
                })

            update_fields.append(f"name = '{safe_name}'")

        if 'color' in data:
            safe_color = data['color'].replace("'", "''")
            update_fields.append(f"color = '{safe_color}'")

        if 'description' in data:
            safe_description = data['description'].replace("'", "''")
            update_fields.append(f"description = '{safe_description}'")

        if not update_fields:
            return jsonify({
                'code': 400,
                'message': '没有提供需要更新的字段',
                'data': None
            }), 400

        # 执行更新
        update_fields.append("update_time = NOW()")

        update_query = f"UPDATE product_tags SET {', '.join(update_fields)} WHERE id = {tag_id}"
        cursor.execute(update_query)
        connection.commit()

        # 获取更新后的标签信息
        query = f"""
        SELECT id, name, color, description, created_by,
               DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
               DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time
        FROM product_tags WHERE id = {tag_id}
        """
        cursor.execute(query)
        updated_tag = cursor.fetchone()
        
        return jsonify({
            'code': 0,
            'message': '更新标签成功',
            'data': updated_tag
        })
        
    except Exception as e:
        print(f"更新标签失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'更新标签失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


@tags_bp.route('/product/<product_id>', methods=['GET'])
def get_product_tags(product_id):
    """获取商品的标签"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 获取商品标签
        query = f"""
        SELECT t.id, t.name, t.color, t.description
        FROM product_tags t
        INNER JOIN product_tag_relations r ON t.id = r.tag_id
        WHERE r.product_id = '{product_id}'
        ORDER BY t.name
        """
        cursor.execute(query)
        tags = cursor.fetchall()

        return jsonify({
            'code': 0,
            'message': '获取商品标签成功',
            'data': tags
        })

    except Exception as e:
        print(f"获取商品标签失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取商品标签失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@tags_bp.route('/product/<product_id>/add', methods=['POST'])
def add_product_tag(product_id):
    """为商品添加标签"""
    connection = None
    cursor = None
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]
        has_permission, user_id, user_role = check_tag_permission(token)

        if not has_permission:
            return jsonify({
                'code': 403,
                'message': '权限不足，无法为商品添加标签',
                'data': None
            }), 403

        # 获取请求数据
        data = request.json
        if not data or 'tag_id' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供标签ID',
                'data': None
            }), 400

        tag_id = data['tag_id']

        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 检查标签是否存在
        tag_check_query = f"SELECT id FROM product_tags WHERE id = {tag_id}"
        cursor.execute(tag_check_query)
        if not cursor.fetchone():
            return jsonify({
                'code': 404,
                'message': '标签不存在',
                'data': None
            }), 404

        # 检查商品是否存在
        product_check_query = f"SELECT COUNT(*) as count FROM product WHERE product_id = '{product_id}'"
        cursor.execute(product_check_query)
        result = cursor.fetchone()
        if not result or result['count'] == 0:
            return jsonify({
                'code': 404,
                'message': '商品不存在',
                'data': None
            }), 404

        # 检查关联是否已存在
        relation_check_query = f"SELECT id FROM product_tag_relations WHERE product_id = '{product_id}' AND tag_id = {tag_id}"
        cursor.execute(relation_check_query)
        if cursor.fetchone():
            return jsonify({
                'code': 400,
                'message': '商品已有此标签',
                'data': None
            }), 400

        # 添加关联
        insert_query = f"""
        INSERT INTO product_tag_relations (product_id, tag_id, created_by, create_time)
        VALUES ('{product_id}', {tag_id}, {user_id}, NOW())
        """
        cursor.execute(insert_query)
        connection.commit()

        return jsonify({
            'code': 0,
            'message': '添加标签成功',
            'data': None
        })

    except Exception as e:
        print(f"添加商品标签失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'添加商品标签失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@tags_bp.route('/product/<product_id>/remove/<int:tag_id>', methods=['DELETE'])
def remove_product_tag(product_id, tag_id):
    """移除商品标签"""
    connection = None
    cursor = None
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]
        has_permission, user_id, user_role = check_tag_permission(token)

        if not has_permission:
            return jsonify({
                'code': 403,
                'message': '权限不足，无法移除商品标签',
                'data': None
            }), 403

        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 检查关联是否存在
        check_query = f"SELECT id FROM product_tag_relations WHERE product_id = '{product_id}' AND tag_id = {tag_id}"
        cursor.execute(check_query)
        if not cursor.fetchone():
            return jsonify({
                'code': 404,
                'message': '商品标签关联不存在',
                'data': None
            }), 404

        # 删除关联
        delete_query = f"DELETE FROM product_tag_relations WHERE product_id = '{product_id}' AND tag_id = {tag_id}"
        cursor.execute(delete_query)
        connection.commit()

        return jsonify({
            'code': 0,
            'message': '移除标签成功',
            'data': None
        })

    except Exception as e:
        print(f"移除商品标签失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'移除商品标签失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@tags_bp.route('/delete/<int:tag_id>', methods=['DELETE'])
def delete_tag(tag_id):
    """删除标签"""
    connection = None
    cursor = None
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]
        has_permission, user_id, user_role = check_tag_permission(token)

        if not has_permission:
            return jsonify({
                'code': 403,
                'message': '权限不足，无法删除标签',
                'data': None
            })

        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 检查标签是否存在
        check_query = "SELECT id, name FROM product_tags WHERE id = %s"
        cursor.execute(check_query, [tag_id])
        tag = cursor.fetchone()

        if not tag:
            return jsonify({
                'code': 404,
                'message': '标签不存在',
                'data': None
            })

        # 检查是否有商品使用了这个标签
        relation_query = "SELECT COUNT(*) as count FROM product_tag_relations WHERE tag_id = %s"
        cursor.execute(relation_query, [tag_id])
        relation_count = cursor.fetchone()['count']

        if relation_count > 0:
            return jsonify({
                'code': 400,
                'message': f'无法删除标签，还有 {relation_count} 个商品正在使用此标签',
                'data': None
            })

        # 删除标签
        delete_query = "DELETE FROM product_tags WHERE id = %s"
        cursor.execute(delete_query, [tag_id])
        connection.commit()

        return jsonify({
            'code': 0,
            'message': f'标签 "{tag["name"]}" 删除成功',
            'data': None
        })

    except Exception as e:
        print(f"删除标签失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'删除标签失败: {str(e)}',
            'data': None
        })
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
