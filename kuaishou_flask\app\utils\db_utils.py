import mysql.connector
from mysql.connector import Error, pooling
from app.config.config import DB_CONFIG
import time
import threading
import logging
from contextlib import contextmanager

# 创建连接池
try:
    connection_pool = mysql.connector.pooling.MySQLConnectionPool(**DB_CONFIG)
    print(f"数据库连接池创建成功，池大小: {DB_CONFIG['pool_size']}")
    logging.info(f"数据库连接池创建成功，池大小: {DB_CONFIG['pool_size']}")
except Error as e:
    print(f"数据库连接池创建失败: {e}")
    logging.error(f"数据库连接池创建失败: {e}")
    raise

# 连接池监控
_pool_stats = {
    'total_requests': 0,
    'failed_requests': 0
}
_stats_lock = threading.Lock()

def get_connection():
    """获取数据库连接"""
    global _pool_stats

    with _stats_lock:
        _pool_stats['total_requests'] += 1

    max_retries = 3
    retry_delay = 0.1

    for attempt in range(max_retries):
        try:
            connection = connection_pool.get_connection()

            # 测试连接是否有效
            if connection.is_connected():
                return connection
            else:
                connection.close()
                raise Error("连接无效")

        except Error as e:
            if attempt < max_retries - 1:
                print(f"获取数据库连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                time.sleep(retry_delay * (2 ** attempt))  # 指数退避
                continue
            else:
                with _stats_lock:
                    _pool_stats['failed_requests'] += 1
                print(f"获取数据库连接失败，已重试{max_retries}次: {e}")
                logging.error(f"获取数据库连接失败，已重试{max_retries}次: {e}")
                raise

def close_connection(connection):
    """关闭数据库连接"""
    if connection and connection.is_connected():
        try:
            connection.close()
        except Error as e:
            logging.warning(f"关闭数据库连接时出错: {e}")

def get_actual_pool_stats():
    """获取真实的连接池状态"""
    try:
        # 尝试获取连接池的内部状态
        pool_size = connection_pool._pool_size

        # 计算当前可用连接数（队列中的连接数）
        available_connections = connection_pool._cnx_queue.qsize()

        # 活跃连接数 = 总连接数 - 可用连接数
        active_connections = pool_size - available_connections

        return {
            'pool_size': pool_size,
            'active_connections': max(0, active_connections),
            'available_connections': max(0, available_connections)
        }
    except Exception as e:
        logging.warning(f"获取连接池状态失败: {e}")
        # 返回默认值
        return {
            'pool_size': 32,
            'active_connections': 0,
            'available_connections': 32
        }

@contextmanager
def get_db_connection():
    """上下文管理器，自动管理数据库连接"""
    connection = None
    try:
        connection = get_connection()
        yield connection
    finally:
        if connection:
            close_connection(connection)

def get_pool_stats():
    """获取连接池统计信息"""
    # 获取真实的连接池状态
    pool_stats = get_actual_pool_stats()

    # 获取请求统计
    with _stats_lock:
        request_stats = _pool_stats.copy()

    # 合并统计信息
    return {
        **pool_stats,
        'total_requests': request_stats['total_requests'],
        'failed_requests': request_stats['failed_requests']
    }

def log_pool_stats():
    """记录连接池统计信息"""
    stats = get_pool_stats()
    logging.info(f"连接池统计 - 池大小: {stats['pool_size']}, "
                f"活跃连接: {stats['active_connections']}, "
                f"可用连接: {stats['available_connections']}, "
                f"总请求: {stats['total_requests']}, "
                f"失败请求: {stats['failed_requests']}")
    print(f"连接池统计 - 池大小: {stats['pool_size']}, "
          f"活跃连接: {stats['active_connections']}, "
          f"可用连接: {stats['available_connections']}, "
          f"总请求: {stats['total_requests']}, "
          f"失败请求: {stats['failed_requests']}")

# 定期记录连接池状态
def _periodic_stats_logging():
    """定期记录连接池状态"""
    import threading
    import time

    def log_stats():
        while True:
            time.sleep(300)  # 每5分钟记录一次
            log_pool_stats()

    stats_thread = threading.Thread(target=log_stats, daemon=True)
    stats_thread.start()

# 启动定期统计
_periodic_stats_logging()