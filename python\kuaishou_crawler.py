#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import time
import logging
from typing import Dict, List, Optional, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('kuaishou_crawler')

class KuaishouCrawler:
    """快手达人爬虫类，用于从快手后台获取达人数据"""
    
    def __init__(self):
        self.base_url = "https://cps.kwaixiaodian.com"
        self.headers = {
            "accept": "application/json",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "kpf": "PC_WEB",
            "kpn": "KWAIXIAODIAN",
            "referer": "https://cps.kwaixiaodian.com/pc/leader/zone/daren-match/daren-square",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"
        }
        # 注意：实际使用时需要更新cookie和businesskey
        self.cookies = {}
        self.business_key = ""
        
    def update_auth(self, cookies: str, business_key: str):
        """更新认证信息
        
        Args:
            cookies: 快手小店的cookie字符串
            business_key: 快手小店的businesskey
        """
        # 解析cookie字符串为字典
        if cookies:
            cookie_dict = {}
            for item in cookies.split(';'):
                if '=' in item:
                    key, value = item.strip().split('=', 1)
                    cookie_dict[key] = value
            self.cookies = cookie_dict
            
        if business_key:
            self.business_key = business_key
            self.headers["businesskey"] = business_key
    
    def search_talents(self, keyword: str) -> List[Dict]:
        """搜索快手达人
        
        Args:
            keyword: 搜索关键词，可以是达人名称或ID
            
        Returns:
            list: 达人列表
        """
        try:
            url = f"{self.base_url}/distribute/pc/seller/promoter/search"
            params = {"keyWord": keyword}
            
            response = requests.get(
                url, 
                params=params, 
                headers=self.headers, 
                cookies=self.cookies
            )
            
            if response.status_code != 200:
                logger.error(f"请求失败，状态码: {response.status_code}")
                return []
            
            data = response.json()
            if data.get("result") != 1:
                logger.error(f"请求失败，错误信息: {data.get('error_msg', '未知错误')}")
                return []
            
            promoter_list = data.get("data", {}).get("promoterList", [])
            
            # 格式化为我们需要的数据结构
            result = []
            for promoter in promoter_list:
                result.append({
                    "talent_id": str(promoter.get("userId", "")),
                    "talent_name": promoter.get("name", ""),
                    "fans_count": promoter.get("fans", 0),
                    "avatar_url": promoter.get("userHead", ""),
                    "kwai_id": promoter.get("kwaiId", ""),
                    # 其他可能需要的字段可以在这里添加
                    "talent_category": "",  # 这个字段API中没有，需要另外获取或手动设置
                })
            
            return result
        except Exception as e:
            logger.error(f"搜索达人出错: {str(e)}")
            return []
    
    def get_talent_detail(self, talent_id: str) -> Optional[Dict]:
        """获取达人详细信息
        
        Args:
            talent_id: 达人ID
            
        Returns:
            dict: 达人详细信息
        """
        # 注意：这个API接口您没有提供，这里只是一个示例
        # 实际使用时需要根据真实的API接口进行调整
        try:
            # 这里应该调用获取达人详情的API
            # 由于没有提供详细API，这里先返回基本信息
            talents = self.search_talents(talent_id)
            if talents:
                return talents[0]
            return None
        except Exception as e:
            logger.error(f"获取达人详情出错: {str(e)}")
            return None

def test_crawler():
    """测试爬虫功能"""
    # 创建爬虫实例
    crawler = KuaishouCrawler()
    
    # 设置cookie和businesskey
    # 注意：实际使用时需要替换为真实的cookie和businesskey
    cookie_str = "_did=web_6109536701C516C8; did=web_m5p31mdccrdqpmzfrlb5k0pb18ya7tn1; sid=kuaishou.shop.b; userId=2885180614; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAE3tBozHFVZOn8pljtRICePPkO5r0KudHnwAKYs6cKFeR4iZ2kuwe3aY_uTEPJciQgEY7R3cPMo-Zs_Z83AQerRWidPl8vDub4WQdp_YD1EFwv1BmSppwUKKxrfyFD1MAMI0oFHlLPVdpSQK-m5yfSBSPE5AvYJkQ1QKZyRMXgmVs1RE4_wHZLmfACJibZ6mUJZC8jixMJ0KY1-1xjH83blGhISqzP1A3OvUjEirVmZwcinEd4iIJfDUuUOMJQnmTnc3NWgIAQd3LEVHp0oaIZSZ-gNVIyEKAUwAQ; kuaishou.shop.b_ph=70bed43b4a2ba56eb32fc3df6bcdff0a7ba5"
    business_key = ""
    crawler.update_auth(cookie_str, business_key)
    
    # 测试搜索达人
    keyword = "151"
    logger.info(f"搜索关键词: {keyword}")
    talents = crawler.search_talents(keyword)
    
    # 打印结果
    logger.info(f"找到 {len(talents)} 个达人")
    for talent in talents:
        logger.info(f"达人ID: {talent['talent_id']}, 名称: {talent['talent_name']}, 粉丝数: {talent['fans_count']}")
    
    # 将结果保存到文件
    with open("kuaishou_talents.json", "w", encoding="utf-8") as f:
        json.dump(talents, f, ensure_ascii=False, indent=2)
    
    logger.info("结果已保存到 kuaishou_talents.json")

if __name__ == "__main__":
    test_crawler() 