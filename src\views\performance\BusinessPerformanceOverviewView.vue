<template>
  <div class="performance-overview">
    <div class="page-header">
      <h1>商务业绩概览</h1>
      <div class="date-picker">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateChange"
        />
      </div>
    </div>

    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-title">总GMV</div>
            <div class="statistics-value">¥{{ statistics.totalGmv }}</div>
            <div
              class="statistics-compare"
              :class="{ up: statistics.totalGmvCompare > 0, down: statistics.totalGmvCompare < 0 }"
            >
              较上期 {{ statistics.totalGmvCompare > 0 ? '+' : ''
              }}{{ statistics.totalGmvCompare }}%
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-title">收入GMV</div>
            <div class="statistics-value">¥{{ statistics.incomeGmv }}</div>
            <div
              class="statistics-compare"
              :class="{
                up: statistics.incomeGmvCompare > 0,
                down: statistics.incomeGmvCompare < 0,
              }"
            >
              较上期 {{ statistics.incomeGmvCompare > 0 ? '+' : ''
              }}{{ statistics.incomeGmvCompare }}%
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-title">支出GMV</div>
            <div class="statistics-value">¥{{ statistics.expenseGmv }}</div>
            <div
              class="statistics-compare"
              :class="{
                up: statistics.expenseGmvCompare > 0,
                down: statistics.expenseGmvCompare < 0,
              }"
            >
              较上期 {{ statistics.expenseGmvCompare > 0 ? '+' : ''
              }}{{ statistics.expenseGmvCompare }}%
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-title">净服务费</div>
            <div class="statistics-value">¥{{ statistics.netServiceFee }}</div>
            <div
              class="statistics-compare"
              :class="{
                up: statistics.netServiceFeeCompare > 0,
                down: statistics.netServiceFeeCompare < 0,
              }"
            >
              较上期 {{ statistics.netServiceFeeCompare > 0 ? '+' : ''
              }}{{ statistics.netServiceFeeCompare }}%
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-title">总订单量</div>
            <div class="statistics-value">{{ statistics.totalOrders }}</div>
            <div
              class="statistics-compare"
              :class="{
                up: statistics.totalOrdersCompare > 0,
                down: statistics.totalOrdersCompare < 0,
              }"
            >
              较上期 {{ statistics.totalOrdersCompare > 0 ? '+' : ''
              }}{{ statistics.totalOrdersCompare }}%
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-title">收入订单量</div>
            <div class="statistics-value">{{ statistics.incomeOrders }}</div>
            <div
              class="statistics-compare"
              :class="{
                up: statistics.incomeOrdersCompare > 0,
                down: statistics.incomeOrdersCompare < 0,
              }"
            >
              较上期 {{ statistics.incomeOrdersCompare > 0 ? '+' : ''
              }}{{ statistics.incomeOrdersCompare }}%
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-title">支出订单量</div>
            <div class="statistics-value">{{ statistics.expenseOrders }}</div>
            <div
              class="statistics-compare"
              :class="{
                up: statistics.expenseOrdersCompare > 0,
                down: statistics.expenseOrdersCompare < 0,
              }"
            >
              较上期 {{ statistics.expenseOrdersCompare > 0 ? '+' : ''
              }}{{ statistics.expenseOrdersCompare }}%
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-title">平均客单价</div>
            <div class="statistics-value">¥{{ avgOrderValue }}</div>
            <div class="statistics-compare neutral">基于总GMV/总订单量</div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>GMV趋势</span>
              </div>
            </template>
            <div ref="gmvChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>订单趋势</span>
              </div>
            </template>
            <div ref="orderChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 每日数据表格 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>每日业绩数据</span>
          </div>
        </template>
        <el-table :data="paginatedData" style="width: 100%" border v-loading="loading">
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="total_gmv" label="总GMV" width="120">
            <template #default="scope"> ¥{{ scope.row.total_gmv.toFixed(2) }} </template>
          </el-table-column>
          <el-table-column prop="income_gmv" label="收入GMV" width="120">
            <template #default="scope"> ¥{{ scope.row.income_gmv.toFixed(2) }} </template>
          </el-table-column>
          <el-table-column prop="expense_gmv" label="支出GMV" width="120">
            <template #default="scope"> ¥{{ scope.row.expense_gmv.toFixed(2) }} </template>
          </el-table-column>
          <el-table-column prop="net_service_fee" label="净服务费" width="120">
            <template #default="scope"> ¥{{ scope.row.net_service_fee.toFixed(2) }} </template>
          </el-table-column>
          <el-table-column prop="total_orders" label="总订单量" width="100" />
          <el-table-column prop="income_orders" label="收入订单" width="100" />
          <el-table-column prop="expense_orders" label="支出订单" width="100" />
        </el-table>

        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import * as echarts from 'echarts'
import axios from 'axios'
import { ElMessage } from 'element-plus'

// 定义数据接口
interface DailyData {
  date: string
  total_gmv: number
  income_gmv: number
  expense_gmv: number
  income_service_fee: number
  expense_service_fee: number
  net_service_fee: number
  total_orders: number
  income_orders: number
  expense_orders: number
}

// 日期范围
const dateRange = ref<[string, string]>([
  new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  new Date().toISOString().split('T')[0],
])

// 统计数据
const statistics = reactive({
  totalGmv: '0.00',
  totalGmvCompare: 0,
  incomeGmv: '0.00',
  incomeGmvCompare: 0,
  expenseGmv: '0.00',
  expenseGmvCompare: 0,
  netServiceFee: '0.00',
  netServiceFeeCompare: 0,
  totalOrders: 0,
  totalOrdersCompare: 0,
  incomeOrders: 0,
  incomeOrdersCompare: 0,
  expenseOrders: 0,
  expenseOrdersCompare: 0,
})

// 表格数据
const tableData = ref<DailyData[]>([])
const loading = ref(false)

// 当前用户信息
const currentUser = ref<any>(null)

// 用户权限
const userPermissions = ref<any>({
  performance: {
    data: [],
    operations: [],
    modules: [],
  },
})

// 权限检查方法
const hasDataPermission = (permission: string) => {
  return userPermissions.value.performance.data.includes(permission)
}

const hasModulePermission = (permission: string) => {
  return userPermissions.value.performance.modules.includes(permission)
}

// 便捷权限检查
const canViewOwnDataOnly = computed(() => hasDataPermission('own_only'))
const canViewAllData = computed(() => hasDataPermission('all_data'))
const canAccessPerformanceOverview = computed(() => hasModulePermission('performance_overview'))

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => tableData.value.length)

// 图表实例
let gmvChartInstance: echarts.ECharts | null = null
let orderChartInstance: echarts.ECharts | null = null
const gmvChartRef = ref<HTMLElement>()
const orderChartRef = ref<HTMLElement>()

// 计算平均客单价
const avgOrderValue = computed(() => {
  const totalGmv = parseFloat(statistics.totalGmv)
  const totalOrders = statistics.totalOrders
  if (totalOrders === 0) return '0.00'
  return (totalGmv / totalOrders).toFixed(2)
})

// 分页数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return tableData.value.slice(start, end)
})

// 格式化日期
const formatDate = (date: Date) => {
  return date.toISOString().split('T')[0]
}

// 获取当前登录用户信息
const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      currentUser.value = JSON.parse(userStr)

      // 解析权限信息
      if (currentUser.value.permissions) {
        try {
          const permissions =
            typeof currentUser.value.permissions === 'string'
              ? JSON.parse(currentUser.value.permissions)
              : currentUser.value.permissions
          userPermissions.value = permissions
        } catch (permError) {
          console.error('解析权限信息失败:', permError)
          // 使用默认权限
          userPermissions.value = {
            performance: { data: [], operations: [], modules: [] },
          }
        }
      }

      console.log('当前登录用户:', currentUser.value)
      console.log('业绩模块权限:', userPermissions.value.performance)
    } else {
      console.error('未找到登录用户信息')
      ElMessage.warning('未找到登录用户信息，请重新登录')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请重新登录')
  }
}

// 日期变化处理
const handleDateChange = () => {
  fetchData()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params: any = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const token = localStorage.getItem('token')
    const response = await axios.get('/api/performance/overview', {
      params,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (response.data.code === 200) {
      const data = response.data.data
      Object.assign(statistics, data.statistics)
      tableData.value = data.dailyData || []
      updateCharts()
    } else {
      ElMessage.error(response.data.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取业绩概览数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  if (gmvChartRef.value) {
    gmvChartInstance = echarts.init(gmvChartRef.value)
  }
  if (orderChartRef.value) {
    orderChartInstance = echarts.init(orderChartRef.value)
  }
}

// 更新图表
const updateCharts = () => {
  if (gmvChartInstance) {
    const gmvOption = {
      title: { text: 'GMV趋势' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['总GMV', '收入GMV', '支出GMV'] },
      xAxis: {
        type: 'category',
        data: tableData.value.map((item) => item.date),
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '总GMV',
          type: 'line',
          data: tableData.value.map((item) => item.total_gmv),
          smooth: true,
        },
        {
          name: '收入GMV',
          type: 'line',
          data: tableData.value.map((item) => item.income_gmv),
          smooth: true,
        },
        {
          name: '支出GMV',
          type: 'line',
          data: tableData.value.map((item) => item.expense_gmv),
          smooth: true,
        },
      ],
    }
    gmvChartInstance.setOption(gmvOption)
  }

  if (orderChartInstance) {
    const orderOption = {
      title: { text: '订单趋势' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['总订单', '收入订单', '支出订单'] },
      xAxis: {
        type: 'category',
        data: tableData.value.map((item) => item.date),
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '总订单',
          type: 'bar',
          data: tableData.value.map((item) => item.total_orders),
        },
        {
          name: '收入订单',
          type: 'bar',
          data: tableData.value.map((item) => item.income_orders),
        },
        {
          name: '支出订单',
          type: 'bar',
          data: tableData.value.map((item) => item.expense_orders),
        },
      ],
    }
    orderChartInstance.setOption(orderOption)
  }
}

// 窗口大小变化处理
const handleResize = () => {
  gmvChartInstance?.resize()
  orderChartInstance?.resize()
}

// 生命周期
onMounted(() => {
  getCurrentUser()
  initCharts()
  fetchData()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  gmvChartInstance?.dispose()
  orderChartInstance?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.performance-overview {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.statistics-cards {
  margin-bottom: 20px;
}

.statistics-card {
  text-align: center;
}

.statistics-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.statistics-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.statistics-compare {
  font-size: 12px;
}

.statistics-compare.up {
  color: #67c23a;
}

.statistics-compare.down {
  color: #f56c6c;
}

.statistics-compare.neutral {
  color: #909399;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.table-section {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
