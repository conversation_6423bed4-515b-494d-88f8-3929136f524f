<template>
  <div class="exclusive-team-leader-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>专属团长</h3>
        </div>
      </template>
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="团长名称">
            <el-input v-model="searchForm.leader_name" placeholder="请输入团长名称" />
          </el-form-item>
          <el-form-item label="团长ID">
            <el-input v-model="searchForm.leader_id" placeholder="请输入团长ID" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-table :data="teamLeaderList" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column label="团长信息" min-width="300">
          <template #default="scope">
            <div class="leader-info-card">
              <div class="leader-avatar">
                <el-avatar :size="50" :src="scope.row.avatar_url" fit="cover">
                  <el-icon><Picture /></el-icon>
                </el-avatar>
              </div>
              <div class="leader-details">
                <div class="leader-name-row">
                  <span class="leader-name">{{ scope.row.leader_name }}</span>
                  <el-tag size="small" type="success" class="leader-type-tag">专属团长</el-tag>
                </div>
                <div class="leader-id-row">
                  <span class="leader-id-label">ID:</span>
                  <span class="leader-id">{{ scope.row.leader_id }}</span>
                </div>
                <div class="leader-contact-row" v-if="scope.row.contact_name || scope.row.wechat">
                  <span v-if="scope.row.contact_name" class="leader-contact"
                    >联系人: {{ scope.row.contact_name }}</span
                  >
                  <span v-if="scope.row.wechat" class="leader-contact"
                    >微信: {{ scope.row.wechat }}</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="数据统计" min-width="250">
          <template #default="scope">
            <div class="leader-stats">
              <div class="stat-item">
                <span class="stat-label">近30天带货达人:</span>
                <span class="stat-value">{{ scope.row.recent_30d_talent_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">近30天销售额:</span>
                <span class="stat-value">{{ scope.row.recent_30d_sales_amount || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="历史数据" min-width="200">
          <template #default="scope">
            <div class="leader-stats">
              <div class="stat-item">
                <span class="stat-label">历史招商活动数:</span>
                <span class="stat-value">{{ scope.row.historical_talent_activities || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">历史推广成交商家数:</span>
                <span class="stat-value">{{ scope.row.historical_promoted_merchants || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">历史推广成交商品数:</span>
                <span class="stat-value">{{ scope.row.historical_promoted_products || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="标签" min-width="150">
          <template #default="scope">
            <div class="leader-tags">
              <template v-if="scope.row.tags">
                <el-tag
                  v-for="(tag, index) in scope.row.tags.split(',')"
                  :key="index"
                  size="small"
                  class="tag-item"
                >
                  {{ tag.trim() }}
                </el-tag>
              </template>
              <span v-else class="no-tags">无标签</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注" min-width="150">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.remarks"
              :content="scope.row.remarks"
              placement="top"
              :show-after="500"
            >
              <div class="remarks-text">{{ scope.row.remarks }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 对接商务列 - 只对管理员和运营显示 -->
        <el-table-column v-if="showBusinessColumn" label="对接商务" width="120" align="center">
          <template #default="scope">
            <span
              v-if="scope.row.business_contact"
              class="business-contact clickable"
              @click="handleEditBusiness(scope.row)"
            >
              {{ scope.row.business_contact }}
            </span>
            <span v-else class="no-business clickable" @click="handleEditBusiness(scope.row)">
              点击设置
            </span>
          </template>
        </el-table-column>

        <el-table-column label="更新时间" min-width="120">
          <template #default="scope">
            {{ formatDateTime(scope.row.update_time, 'short') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button :disabled="true" size="small" type="info">专属</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 编辑团长对话框 -->
    <el-dialog v-model="editLeaderDialogVisible" title="编辑团长信息" width="50%">
      <el-form :model="editForm" label-width="100px" :rules="editRules" ref="editFormRef">
        <div class="edit-leader-header">
          <el-avatar :size="60" :src="editForm.avatar_url" fit="cover">
            <el-icon><Picture /></el-icon>
          </el-avatar>
          <div class="leader-basic-info">
            <h3>{{ editForm.leader_name }}</h3>
            <p>ID: {{ editForm.leader_id }}</p>
          </div>
        </div>

        <el-divider content-position="left">详细信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="contact_name">
              <el-input v-model="editForm.contact_name" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="contact_phone">
              <el-input v-model="editForm.contact_phone" placeholder="请输入联系人电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合作微信" prop="wechat">
              <el-input v-model="editForm.wechat" placeholder="请输入合作微信" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签" prop="tags">
              <el-input v-model="editForm.tags" placeholder="请输入标签，多个标签用逗号分隔" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="寄样地址" prop="shipping_address">
          <el-input v-model="editForm.shipping_address" placeholder="请输入寄样地址" />
        </el-form-item>

        <!-- 对接商务字段 - 只有管理员和运营可编辑 -->
        <el-form-item v-if="canEditBusiness" label="对接商务" prop="business_contact">
          <BusinessUserSelector
            v-model="editForm.business_contact"
            :multiple="false"
            placeholder="请选择对接商务"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input v-model="editForm.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editLeaderDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEditForm" :loading="submitting">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑商务对话框 -->
    <el-dialog v-model="editBusinessDialogVisible" title="编辑商务信息" width="450px">
      <el-form :model="businessForm" label-width="100px" ref="businessFormRef">
        <div class="business-edit-header">
          <el-avatar :size="50" :src="businessForm.avatar_url" fit="cover">
            <el-icon><Picture /></el-icon>
          </el-avatar>
          <div class="leader-basic-info">
            <h4>{{ businessForm.leader_name }}</h4>
            <p>ID: {{ businessForm.leader_id }}</p>
          </div>
        </div>

        <el-form-item label="团长类型" prop="leader_type">
          <el-select
            v-model="businessForm.leader_type"
            placeholder="请选择团长类型"
            style="width: 100%"
          >
            <el-option label="公海" value="public" />
            <el-option label="专属" value="exclusive" />
            <el-option label="专享" value="special" />
            <el-option label="共享" value="shared" />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="businessForm.leader_type === 'shared'"
          label="共享商务"
          prop="shared_businesses"
        >
          <BusinessUserSelector
            v-model="businessForm.shared_businesses_array"
            :multiple="true"
            :multiple-limit="10"
            placeholder="请选择共享商务（最多10个）"
          />
        </el-form-item>

        <el-form-item
          v-else-if="businessForm.leader_type !== 'public'"
          label="对接商务"
          prop="business_contact"
        >
          <BusinessUserSelector
            v-model="businessForm.business_contact"
            :multiple="false"
            placeholder="请选择对接商务"
          />
        </el-form-item>

        <el-form-item v-else label="商务信息">
          <el-text type="info">公海团长无需设置商务信息</el-text>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editBusinessDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitBusinessForm" :loading="businessSubmitting"
            >保存</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import axios from 'axios'
import { Picture } from '@element-plus/icons-vue'
import BusinessUserSelector from '@/components/BusinessUserSelector.vue'

const teamLeaderList = ref<any[]>([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchForm = reactive({
  leader_name: '',
  leader_id: '',
})

// 当前用户
const currentUser = ref(JSON.parse(localStorage.getItem('user') || '{}'))
const currentBusiness = ref('')

// 用户权限
const userPermissions = ref<any>({
  team_leader: {
    data: [],
    operations: [],
  },
})

// 权限检查方法
const hasDataPermission = (permission: string) => {
  return userPermissions.value.team_leader.data.includes(permission)
}

const hasOperationPermission = (permission: string) => {
  return userPermissions.value.team_leader.operations.includes(permission)
}

// 便捷权限检查
const canViewOwnDataOnly = computed(() => hasDataPermission('own_only'))
const canViewAllData = computed(() => hasDataPermission('all_data'))
const canAddTeamLeader = computed(() => hasOperationPermission('add_team_leader'))
const canEditTeamLeaderInfo = computed(() => hasOperationPermission('edit_team_leader_info'))
const canEditTeamLeaderType = computed(() => hasOperationPermission('edit_team_leader_type'))

// 计算属性：是否显示对接商务列
const showBusinessColumn = computed(() => {
  return (
    currentUser.value &&
    (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
  )
})

// 计算属性：是否可以编辑商务信息
const canEditBusiness = computed(() => {
  return (
    currentUser.value &&
    (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
  )
})

// 编辑团长对话框
const editLeaderDialogVisible = ref(false)
const editFormRef = ref<FormInstance>()
const editForm = reactive({
  id: 0,
  leader_name: '',
  leader_id: '',
  avatar_url: '',
  contact_name: '',
  contact_phone: '',
  wechat: '',
  shipping_address: '',
  tags: '',
  business_contact: '',
  remarks: '',
  update_time: '',
})
const submitting = ref(false)

// 商务编辑对话框
const editBusinessDialogVisible = ref(false)
const businessFormRef = ref<FormInstance>()
const businessForm = reactive({
  id: 0,
  leader_name: '',
  leader_id: '',
  avatar_url: '',
  leader_type: '',
  business_contact: '',
  shared_businesses_array: [] as string[],
})
const businessSubmitting = ref(false)

// 表单验证规则
const editRules = {
  contact_name: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  contact_phone: [{ required: true, message: '请输入联系人电话', trigger: 'blur' }],
}

// 获取当前登录用户信息
const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      currentUser.value = JSON.parse(userStr)
      currentBusiness.value = currentUser.value.name || currentUser.value.username || ''

      // 解析权限信息
      if (currentUser.value.permissions) {
        try {
          const permissions =
            typeof currentUser.value.permissions === 'string'
              ? JSON.parse(currentUser.value.permissions)
              : currentUser.value.permissions
          userPermissions.value = permissions
        } catch (permError) {
          console.error('解析权限信息失败:', permError)
          // 使用默认权限
          userPermissions.value = {
            team_leader: { data: [], operations: [] },
          }
        }
      }
    } else {
      console.error('未找到登录用户信息')
    }
  } catch (error) {
    console.error('解析用户信息失败:', error)
  }
}

const fetchExclusiveTeamLeaders = async () => {
  loading.value = true
  try {
    // 根据用户角色选择不同的接口
    const isAdminOrOperation =
      currentUser.value &&
      (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
    const apiUrl = isAdminOrOperation ? '/api/team-leader/all-list' : '/api/team-leader/list'

    const params: any = {
      leader_type: 'exclusive',
      leader_name: searchForm.leader_name,
      leader_id: searchForm.leader_id,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    // 商务用户需要传递business_contact参数
    if (!isAdminOrOperation) {
      params.business_contact = currentBusiness.value
    }

    const { data } = await axios.get(apiUrl, { params })

    if (data.code === 0) {
      teamLeaderList.value = data.data.list || []
      total.value = data.data.total || 0
    } else {
      ElMessage.error(data.message || '获取专属团长失败')
    }
  } catch (e) {
    console.error('获取专属团长失败:', e)
    ElMessage.error('获取专属团长失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchExclusiveTeamLeaders()
}
const resetSearch = () => {
  searchForm.leader_name = ''
  searchForm.leader_id = ''
  handleSearch()
}
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchExclusiveTeamLeaders()
}
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchExclusiveTeamLeaders()
}

function formatDateTime(dateTimeStr: string, format: 'full' | 'short' = 'full') {
  if (!dateTimeStr) return ''
  try {
    const date = new Date(dateTimeStr)
    if (format === 'short') {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return dateTimeStr
  }
}

// 编辑团长
const handleEdit = (row: any) => {
  // 复制数据到编辑表单
  Object.assign(editForm, row)
  editLeaderDialogVisible.value = true
}

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const updateData: any = {
          leader_id: editForm.leader_id,
          contact_name: editForm.contact_name,
          contact_phone: editForm.contact_phone,
          wechat: editForm.wechat,
          shipping_address: editForm.shipping_address,
          tags: editForm.tags,
          remarks: editForm.remarks,
        }

        // 只有管理员和运营可以更新商务信息
        if (canEditBusiness.value) {
          updateData.business_contact = editForm.business_contact
        }

        const response = await axios.post('/api/team-leader/update', updateData)

        if (response.data.code === 0) {
          ElMessage.success('更新团长信息成功')
          editLeaderDialogVisible.value = false
          fetchExclusiveTeamLeaders() // 刷新列表
        } else {
          ElMessage.error(response.data.message || '更新团长信息失败')
        }
      } catch (error) {
        console.error('更新团长信息失败:', error)
        ElMessage.error('更新团长信息失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 编辑商务
const handleEditBusiness = (row: any) => {
  // 复制数据到商务编辑表单
  Object.assign(businessForm, {
    id: row.id,
    leader_name: row.leader_name,
    leader_id: row.leader_id,
    avatar_url: row.avatar_url,
    leader_type: row.leader_type || 'exclusive',
    business_contact: row.business_contact || '',
  })

  // 处理共享商务数组
  if (row.shared_businesses) {
    businessForm.shared_businesses_array = row.shared_businesses
      .split(',')
      .map((b: string) => b.trim())
  } else {
    businessForm.shared_businesses_array = []
  }

  editBusinessDialogVisible.value = true
}

// 提交商务编辑表单
const submitBusinessForm = async () => {
  businessSubmitting.value = true
  try {
    const updateData: any = {
      leader_id: businessForm.leader_id,
      leader_type: businessForm.leader_type,
    }

    // 根据团长类型设置不同的商务字段
    if (businessForm.leader_type === 'shared') {
      updateData.shared_businesses = businessForm.shared_businesses_array.join(',')
    } else if (businessForm.leader_type === 'public') {
      // 公海团长清空商务字段
      updateData.business_contact = ''
      updateData.shared_businesses = ''
    } else {
      updateData.business_contact = businessForm.business_contact
    }

    const response = await axios.post('/api/team-leader/update-business', updateData)

    if (response.data.code === 0) {
      ElMessage.success('更新商务信息成功')
      editBusinessDialogVisible.value = false
      fetchExclusiveTeamLeaders() // 刷新列表
    } else {
      ElMessage.error(response.data.message || '更新商务信息失败')
    }
  } catch (error) {
    console.error('更新商务信息失败:', error)
    ElMessage.error('更新商务信息失败')
  } finally {
    businessSubmitting.value = false
  }
}

onMounted(() => {
  getCurrentUser()
  fetchExclusiveTeamLeaders()
})
</script>

<style scoped>
.exclusive-team-leader-container {
  padding: 0px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search-area {
  margin-bottom: 16px;
}
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
.leader-info-card {
  display: flex;
  align-items: center;
}
.leader-avatar {
  margin-right: 10px;
}
.leader-details {
  flex: 1;
  overflow: hidden;
}
.leader-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.leader-name {
  font-weight: bold;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.leader-type-tag {
  margin-left: 8px;
}
.leader-id-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  color: #909399;
  font-size: 12px;
}
.leader-id-label {
  margin-right: 4px;
}
.leader-id {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.leader-contact-row {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
}
.leader-contact {
  margin-right: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.leader-stats {
  display: flex;
  flex-direction: column;
}
.stat-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.stat-label {
  color: #909399;
  font-size: 12px;
  margin-right: 4px;
  flex-shrink: 0;
}
.stat-value {
  font-weight: bold;
  font-size: 12px;
}
.leader-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
.tag-item {
  margin-right: 4px;
  margin-bottom: 4px;
}
.no-tags {
  color: #909399;
  font-size: 12px;
}
.remarks-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

/* 对接商务列样式 */
.business-contact {
  color: #409eff;
  font-weight: 500;
}

.no-business {
  color: #c0c4cc;
  font-style: italic;
}

/* 编辑对话框样式 */
.edit-leader-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.leader-basic-info h3 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 18px;
}

.leader-basic-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

/* 商务编辑对话框样式 */
.business-edit-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}
</style>
