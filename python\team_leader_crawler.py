#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import time
import mysql.connector
import re
from datetime import datetime

class TeamLeaderCrawler:
    def __init__(self, cookie, db_config=None):
        """
        初始化团长数据爬虫
        :param cookie: 快手小店的cookie
        :param db_config: 数据库配置，如果为None则只保存JSON文件不存入数据库
        """
        self.cookie = cookie
        self.db_config = db_config
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Cookie': self.cookie,
            'Content-Type': 'application/json',
            'Origin': 'https://cps.kwaixiaodian.com',
            'Referer': 'https://cps.kwaixiaodian.com/'
        }
        self.search_url = 'https://cps.kwaixiaodian.com/gateway/distribute/platform/seller/activity/user/list'
        self.detail_url = 'https://cps.kwaixiaodian.com/gateway/distribute/platform/seller/activity/user/get'
        self.db_conn = None
        self.cursor = None
        
        if db_config:
            try:
                self.db_conn = mysql.connector.connect(**db_config)
                self.cursor = self.db_conn.cursor()
                print("数据库连接成功")
            except Exception as e:
                print(f"数据库连接失败: {e}")
                self.db_config = None
    
    def __del__(self):
        """析构函数，确保关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.db_conn:
            self.db_conn.close()
    
    def search_team_leaders(self, keyword=None, limit=10, offset=0):
        """
        搜索团长列表
        :param keyword: 搜索关键词，可以是团长ID或名称
        :param limit: 每页数量
        :param offset: 偏移量
        :return: 团长列表
        """
        payload = {
            "activityUserLevelList": [],
            "bizCode": 2,
            "limit": limit,
            "offset": offset,
            "orderFiled": 1,
            "orderType": 1
        }
        
        if keyword:
            payload["keyWord"] = keyword
        
        try:
            response = requests.post(self.search_url, headers=self.headers, json=payload)
            response.raise_for_status()
            data = response.json()
            
            if data['result'] == 1 and 'data' in data and 'activityUserList' in data['data']:
                return data['data']['activityUserList'], data['data']['total']
            else:
                print(f"搜索团长失败: {data.get('error_msg', '未知错误')}")
                return [], 0
        except Exception as e:
            print(f"搜索团长请求异常: {e}")
            return [], 0
    
    def get_team_leader_detail(self, activity_user_id):
        """
        获取团长详细信息
        :param activity_user_id: 团长ID
        :return: 团长详细信息
        """
        params = {
            "activityUserId": activity_user_id,
            "bizCode": 2
        }
        
        try:
            response = requests.get(self.detail_url, headers=self.headers, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data['result'] == 1 and 'data' in data:
                return data['data']
            else:
                print(f"获取团长详情失败: {data.get('error_msg', '未知错误')}")
                return None
        except Exception as e:
            print(f"获取团长详情请求异常: {e}")
            return None
    
    def extract_number_from_fuzzy(self, fuzzy_value):
        """
        从模糊值中提取数字
        :param fuzzy_value: 模糊值，如"1000万以上"
        :return: 提取的数字，如果是"万以上"则乘以10000
        """
        if not fuzzy_value or not isinstance(fuzzy_value, str):
            return 0
        
        # 处理百分比
        if '%' in fuzzy_value:
            try:
                return float(fuzzy_value.replace('%', ''))
            except:
                return 0
        
        # 处理"万以上"格式
        if '万以上' in fuzzy_value:
            match = re.search(r'(\d+)万以上', fuzzy_value)
            if match:
                return int(match.group(1)) * 10000
        
        # 尝试直接提取数字
        match = re.search(r'(\d+)', fuzzy_value)
        if match:
            return int(match.group(1))
        
        return 0
    
    def save_to_database(self, leader_data, detail_data):
        """
        将团长数据保存到数据库
        :param leader_data: 团长基本数据
        :param detail_data: 团长详细数据
        :return: 是否保存成功
        """
        if not self.db_conn or not self.cursor:
            print("数据库未连接，无法保存数据")
            return False
        
        try:
            # 提取需要的字段
            leader_id = leader_data.get('activityUserId', 0)
            leader_name = leader_data.get('nickName', '')
            avatar_url = leader_data.get('headUrl', '')
            leader_type = leader_data.get('activityUserLevel', 0)
            
            # 近30日数据
            recent_30d_talent_count = self.extract_number_from_fuzzy(leader_data.get('promotionPromoterCount30DayFuzzy', '0'))
            recent_30d_promoted_products = self.extract_number_from_fuzzy(leader_data.get('promotionItemCount30DayFuzzy', '0'))
            recent_30d_sold_products = self.extract_number_from_fuzzy(leader_data.get('promotionVolume30DayFuzzy', '0'))
            recent_30d_avg_commission_rate = self.extract_number_from_fuzzy(leader_data.get('avgCommissionRate30DayFuzzy', '0'))
            recent_30d_sales_amount = self.extract_number_from_fuzzy(leader_data.get('promotionGmv30DayFuzzy', '0'))
            
            # 历史数据
            historical_talent_activities = detail_data.get('activityCount', 0) if detail_data else 0
            historical_promoted_merchants = detail_data.get('distributeTradeSellerCount', 0) if detail_data else 0
            historical_promoted_products = detail_data.get('distributeTradeItemCount', 0) if detail_data else 0
            historical_avg_transaction_value = detail_data.get('distributeItemOrderCount', 0) if detail_data else 0
            historical_unit_price = detail_data.get('customerPrice', 0) / 100 if detail_data and detail_data.get('customerPrice') else 0
            
            # 检查团长是否已存在
            self.cursor.execute("SELECT id FROM team_leader WHERE leader_id = %s", (str(leader_id),))
            result = self.cursor.fetchone()
            
            if result:
                # 更新现有记录
                update_query = """
                UPDATE team_leader SET
                    leader_name = %s,
                    avatar_url = %s,
                    leader_type = %s,
                    recent_30d_talent_count = %s,
                    recent_30d_promoted_products = %s,
                    recent_30d_sold_products = %s,
                    recent_30d_avg_commission_rate = %s,
                    recent_30d_sales_amount = %s,
                    historical_talent_activities = %s,
                    historical_promoted_merchants = %s,
                    historical_promoted_products = %s,
                    historical_avg_transaction_value = %s,
                    historical_unit_price = %s,
                    update_time = %s
                WHERE leader_id = %s
                """
                self.cursor.execute(update_query, (
                    leader_name,
                    avatar_url,
                    leader_type,
                    recent_30d_talent_count,
                    recent_30d_promoted_products,
                    recent_30d_sold_products,
                    recent_30d_avg_commission_rate,
                    recent_30d_sales_amount,
                    historical_talent_activities,
                    historical_promoted_merchants,
                    historical_promoted_products,
                    historical_avg_transaction_value,
                    historical_unit_price,
                    datetime.now(),
                    str(leader_id)
                ))
                print(f"更新团长数据: {leader_name} (ID: {leader_id})")
            else:
                # 插入新记录
                insert_query = """
                INSERT INTO team_leader (
                    leader_id, leader_name, avatar_url, leader_type,
                    recent_30d_talent_count, recent_30d_promoted_products, recent_30d_sold_products,
                    recent_30d_avg_commission_rate, recent_30d_sales_amount,
                    historical_talent_activities, historical_promoted_merchants, historical_promoted_products,
                    historical_avg_transaction_value, historical_unit_price,
                    create_time, update_time
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                current_time = datetime.now()
                self.cursor.execute(insert_query, (
                    str(leader_id),
                    leader_name,
                    avatar_url,
                    leader_type,
                    recent_30d_talent_count,
                    recent_30d_promoted_products,
                    recent_30d_sold_products,
                    recent_30d_avg_commission_rate,
                    recent_30d_sales_amount,
                    historical_talent_activities,
                    historical_promoted_merchants,
                    historical_promoted_products,
                    historical_avg_transaction_value,
                    historical_unit_price,
                    current_time,
                    current_time
                ))
                print(f"插入新团长数据: {leader_name} (ID: {leader_id})")
            
            self.db_conn.commit()
            return True
        except Exception as e:
            self.db_conn.rollback()
            print(f"保存团长数据到数据库失败: {e}")
            return False
    
    def save_to_json(self, data, filename="team_leaders.json"):
        """
        将数据保存为JSON文件
        :param data: 要保存的数据
        :param filename: 文件名
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到 {filename}")
        except Exception as e:
            print(f"保存JSON文件失败: {e}")
    
    def crawl_team_leaders(self, keywords=None, save_json=True):
        """
        爬取团长数据
        :param keywords: 关键词列表，如果为None则爬取所有团长
        :param save_json: 是否保存JSON文件
        :return: 团长数据列表
        """
        all_team_leaders = []
        
        if keywords:
            # 使用关键词搜索
            for keyword in keywords:
                print(f"搜索关键词: {keyword}")
                offset = 0
                limit = 10
                
                while True:
                    team_leaders, total = self.search_team_leaders(keyword, limit, offset)
                    if not team_leaders:
                        break
                    
                    for leader in team_leaders:
                        leader_id = leader.get('activityUserId')
                        print(f"获取团长详情: {leader.get('nickName')} (ID: {leader_id})")
                        
                        detail = self.get_team_leader_detail(leader_id)
                        
                        # 合并基本数据和详细数据
                        leader_data = {
                            "basic": leader,
                            "detail": detail
                        }
                        all_team_leaders.append(leader_data)
                        
                        # 保存到数据库
                        if self.db_config:
                            self.save_to_database(leader, detail)
                        
                        # 避免请求过于频繁
                        time.sleep(1)
                    
                    offset += limit
                    if offset >= total:
                        break
                    
                    # 避免请求过于频繁
                    time.sleep(2)
        else:
            # 不使用关键词，获取所有团长
            offset = 0
            limit = 10
            
            while True:
                team_leaders, total = self.search_team_leaders(None, limit, offset)
                if not team_leaders:
                    break
                
                for leader in team_leaders:
                    leader_id = leader.get('activityUserId')
                    print(f"获取团长详情: {leader.get('nickName')} (ID: {leader_id})")
                    
                    detail = self.get_team_leader_detail(leader_id)
                    
                    # 合并基本数据和详细数据
                    leader_data = {
                        "basic": leader,
                        "detail": detail
                    }
                    all_team_leaders.append(leader_data)
                    
                    # 保存到数据库
                    if self.db_config:
                        self.save_to_database(leader, detail)
                    
                    # 避免请求过于频繁
                    time.sleep(1)
                
                offset += limit
                if offset >= total:
                    break
                
                # 避免请求过于频繁
                time.sleep(2)
        
        # 保存为JSON文件
        if save_json and all_team_leaders:
            self.save_to_json(all_team_leaders)
        
        return all_team_leaders

    def search_team_leaders_for_api(self, keyword=None, limit=10):
        """
        为API提供的团长搜索功能，返回处理后的团长数据
        :param keyword: 搜索关键词，可以是团长ID或名称
        :param limit: 返回结果数量限制
        :return: 处理后的团长数据列表
        """
        result = []
        
        try:
            # 搜索团长
            team_leaders, total = self.search_team_leaders(keyword, limit, 0)
            
            if not team_leaders:
                return result
            
            # 处理每个团长的数据
            for leader in team_leaders:
                leader_id = leader.get('activityUserId')
                
                # 获取详细信息
                detail = self.get_team_leader_detail(leader_id)
                
                # 提取需要的字段
                processed_data = {
                    "leader_id": str(leader_id),
                    "leader_name": leader.get('nickName', ''),
                    "avatar_url": leader.get('headUrl', ''),
                    "leader_type": leader.get('activityUserLevel', 0),
                    "fans_count": 0,  # 暂无粉丝数字段
                    
                    # 近30日数据
                    "recent_30d_talent_count": self.extract_number_from_fuzzy(leader.get('promotionPromoterCount30DayFuzzy', '0')),
                    "recent_30d_promoted_products": self.extract_number_from_fuzzy(leader.get('promotionItemCount30DayFuzzy', '0')),
                    "recent_30d_sold_products": self.extract_number_from_fuzzy(leader.get('promotionVolume30DayFuzzy', '0')),
                    "recent_30d_avg_commission_rate": leader.get('avgCommissionRate30DayFuzzy', '0%'),
                    "recent_30d_sales_amount": leader.get('promotionGmv30DayFuzzy', '0'),
                    
                    # 历史数据
                    "historical_talent_activities": detail.get('activityCount', 0) if detail else 0,
                    "historical_promoted_merchants": detail.get('distributeTradeSellerCount', 0) if detail else 0,
                    "historical_promoted_products": detail.get('distributeTradeItemCount', 0) if detail else 0,
                    "historical_avg_transaction_value": detail.get('distributeItemOrderCount', 0) if detail else 0,
                    "historical_unit_price": detail.get('customerPrice', 0) / 100 if detail and detail.get('customerPrice') else 0,
                    
                    # 主要渠道
                    "main_channels": leader.get('mainChannel', []),
                    
                    # 原始数据
                    "raw_data": {
                        "basic": leader,
                        "detail": detail
                    }
                }
                
                result.append(processed_data)
                
                # 避免请求过于频繁
                time.sleep(0.5)
            
            return result
        except Exception as e:
            print(f"API搜索团长失败: {e}")
            return []


if __name__ == "__main__":
    # 配置信息
    cookie = """kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAE3tBozHFVZOn8pljtRICePPkO5r0KudHnwAKYs6cKFeR4iZ2kuwe3aY_uTEPJciQgEY7R3cPMo-Zs_Z83AQerRWidPl8vDub4WQdp_YD1EFwv1BmSppwUKKxrfyFD1MAMI0oFHlLPVdpSQK-m5yfSBSPE5AvYJkQ1QKZyRMXgmVs1RE4_wHZLmfACJibZ6mUJZC8jixMJ0KY1-1xjH83blGhISqzP1A3OvUjEirVmZwcinEd4iIJfDUuUOMJQnmTnc3NWgIAQd3LEVHp0oaIZSZ-gNVIyEKAUwAQ; kuaishou.shop.b_ph=70bed43b4a2ba56eb32fc3df6bcdff0a7ba5; _did=web_6109536701C516C8; did=web_m5p31mdccrdqpmzfrlb5k0pb18ya7tn1; sid=kuaishou.shop.b; bUserId=1000040627146; userId=2885180614; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAH4DSb6EMpt9ekMzXvWCmYtQk5h9ngNcJuNYVv5fFkqlXBVH5L8S0WjAO-KdAishVJ7PElN3_Qjay6vwLxG9_gdSPEK4b4Kcr_ur7ZFQj9KnCQUigDvpAuI5yc-CxhQxPIgvbLYUCGZ3Z3cM0rq1Tx4Go62m3BxFeGW5sjB1PS1fz6Hc6R6ZH7d-yOz4Y5z0BABnZGH7RJi5ud3asGSyuhVGhJHLt8Pt7EFLsNXpPl1vIIF_g4iIJUluFgVDclb_UG2bNu4G5AbcZ-BVc2kKEkAPEm8KHszKAUwAQ; kuaishou.shop.b_ph=e5de4e6bf2fc2e028415fced5111a185da16"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': '123456',
        'database': 'kwaixiaodian'
    }
    
    # 创建爬虫实例
    crawler = TeamLeaderCrawler(cookie, db_config)
    
    # 示例1: 搜索特定团长
    keywords = ["2513200446", "惠麦文化科技"]
    crawler.crawl_team_leaders(keywords)
    
    # 示例2: 获取所有团长
    # crawler.crawl_team_leaders() 

    # 测试API搜索功能
    result = crawler.search_team_leaders_for_api("惠麦文化科技")
    print(f"找到 {len(result)} 个团长")
    
    # 保存结果到JSON文件
    if result:
        crawler.save_to_json(result, "api_search_result.json") 