<template>
  <div class="my-promotion-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>我的推广商品</h3>
        </div>
      </template>

      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="商品名称">
            <el-input v-model="searchForm.product_name" placeholder="请输入商品名称" clearable />
          </el-form-item>
          <el-form-item label="商品ID">
            <el-input v-model="searchForm.product_id" placeholder="请输入商品ID" clearable />
          </el-form-item>
          <el-form-item label="达人ID">
            <el-input v-model="searchForm.talent_id" placeholder="请输入达人ID" clearable />
          </el-form-item>
          <el-form-item label="达人名称">
            <el-input v-model="searchForm.talent_name" placeholder="请输入达人名称" clearable />
          </el-form-item>
          <el-form-item label="价格区间">
            <el-input
              v-model="searchForm.min_price"
              placeholder="最低价"
              style="width: 100px"
              clearable
            />
            <span class="price-separator">-</span>
            <el-input
              v-model="searchForm.max_price"
              placeholder="最高价"
              style="width: 100px"
              clearable
            />
          </el-form-item>
          <el-form-item label="分类">
            <el-input v-model="searchForm.category" placeholder="请输入分类" clearable />
          </el-form-item>
          <el-form-item label="标签">
            <el-select v-model="searchForm.tag" placeholder="请选择标签" clearable>
              <el-option label="非独家品" value="非独家品" />
              <el-option label="独家品" value="独家品" />
            </el-select>
          </el-form-item>
          <el-form-item label="排序">
            <el-select v-model="searchForm.sort_by" placeholder="排序字段" style="width: 120px">
              <el-option label="推广时间" value="promotion_time" />
              <el-option label="商品ID" value="p.product_id" />
              <el-option label="商品名称" value="p.product_name" />
              <el-option label="价格" value="p.price" />
              <el-option label="达人ID" value="pp.talent_id" />
              <el-option label="达人名称" value="pp.talent_name" />
            </el-select>
            <el-select
              v-model="searchForm.sort_order"
              placeholder="排序方向"
              style="width: 100px; margin-left: 5px"
            >
              <el-option label="降序" value="desc" />
              <el-option label="升序" value="asc" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 卡片式商品列表 -->
      <div v-loading="loading" class="product-cards-container">
        <el-empty v-if="productList.length === 0" description="暂无推广商品数据" />
        <div v-else class="product-cards">
          <el-card
            v-for="product in productList"
            :key="`${product.id}-${product.talent_id}`"
            class="product-card"
            shadow="hover"
          >
            <div class="product-card-content">
              <div class="product-image">
                <el-image
                  :src="product.image_url"
                  fit="cover"
                  :preview-src-list="[product.image_url]"
                >
                  <template #error>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              <div class="product-info">
                <div class="product-title" :title="product.name">{{ product.name }}</div>
                <div class="product-price">¥{{ product.price?.toFixed(2) }}</div>
                <div class="product-meta">
                  <div class="meta-item">
                    <span class="meta-label">商品ID:</span>
                    <span class="meta-value">{{ product.id }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">库存:</span>
                    <span class="meta-value">{{ product.stock }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">销量:</span>
                    <span class="meta-value">{{ product.sales || 0 }}</span>
                  </div>
                </div>
                <div class="product-meta">
                  <div class="meta-item">
                    <span class="meta-label">商家:</span>
                    <span class="meta-value">{{ product.merchant_name }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">评分:</span>
                    <span class="meta-value">{{ product.store_score || '暂无' }}</span>
                  </div>
                </div>
                <!-- 佣金信息 -->
                <div class="commission-info">
                  <div class="commission-item">
                    <span class="commission-label">商家佣金率:</span>
                    <span class="commission-value">{{ product.merchant_commission || '0%' }}</span>
                  </div>
                  <div class="commission-item">
                    <span class="commission-label">服务费率:</span>
                    <span class="commission-value">{{
                      product.investment_commission || '0%'
                    }}</span>
                  </div>
                </div>
                <!-- 推广信息 -->
                <div class="promotion-info">
                  <div class="promotion-item">
                    <span class="promotion-label">达人ID:</span>
                    <span class="promotion-value">{{ product.talent_id }}</span>
                  </div>
                  <div class="promotion-item">
                    <span class="promotion-label">达人名称:</span>
                    <span class="promotion-value">{{ product.talent_name }}</span>
                  </div>
                  <div class="promotion-item">
                    <span class="promotion-label">推广时间:</span>
                    <span class="promotion-value">{{ formatTime(product.promotion_time) }}</span>
                  </div>
                </div>
                <!-- 评分信息 -->
                <div v-if="hasAnyScore(product)" class="product-scores">
                  <div class="score-item" v-if="product.control_commission_score">
                    <span class="score-label">控佣:</span>
                    <el-rate
                      :model-value="product.control_commission_score"
                      :max="10"
                      disabled
                      size="small"
                    />
                  </div>
                  <div class="score-item" v-if="product.cooperation_score">
                    <span class="score-label">配合:</span>
                    <el-rate
                      :model-value="product.cooperation_score"
                      :max="10"
                      disabled
                      size="small"
                    />
                  </div>
                  <div class="score-item" v-if="product.trust_score">
                    <span class="score-label">信任:</span>
                    <el-rate :model-value="product.trust_score" :max="10" disabled size="small" />
                  </div>
                </div>

                <div class="product-category" :title="product.category">
                  <el-tag size="small" type="info">{{ product.category }}</el-tag>
                </div>
                <div v-if="product.tag" class="product-tag-display">
                  <el-tag :type="product.tag === '独家品' ? 'danger' : 'warning'" size="small">
                    {{ product.tag }}
                  </el-tag>
                </div>
              </div>
            </div>
            <div class="product-actions">
              <el-button type="danger" size="small" @click="handleRemovePromotion(product)"
                >移除推广</el-button
              >
              <el-button type="primary" size="small" @click="handleApplyBoost(product)"
                >申请助播</el-button
              >
            </div>
          </el-card>
        </div>
      </div>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 36, 48]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 申请助播对话框 -->
    <el-dialog
      v-model="boostDialogVisible"
      title="申请助播"
      width="500px"
      :before-close="handleCloseBoostDialog"
    >
      <el-form :model="boostForm" :rules="boostRules" ref="boostFormRef" label-width="120px">
        <el-form-item label="选择助播:" prop="talent_id">
          <el-select
            v-model="boostForm.talent_id"
            placeholder="请选择助播"
            style="width: 100%"
            @change="handleTalentChange"
            popper-class="talent-select-dropdown"
          >
            <el-option
              v-for="talent in talentList"
              :key="talent.talent_id"
              :label="`${talent.talent_name} (${talent.fans_count || '未知'}粉丝)`"
              :value="talent.talent_id"
              class="talent-option"
            >
              <div class="talent-option-content">
                <el-avatar :src="talent.talent_avatar" :size="32" class="talent-avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <div class="talent-info">
                  <div class="talent-name">{{ talent.talent_name }}</div>
                  <div class="talent-fans">粉丝: {{ talent.fans_count || '未知' }}</div>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="按照GMV返点费率:">
          <div style="display: flex; align-items: center; flex-wrap: wrap; gap: 10px">
            <div style="display: flex; align-items: center">
              <el-form-item prop="target_gmv_rate" style="margin-bottom: 0">
                <el-input-number
                  v-model="boostForm.target_gmv_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  style="width: 150px"
                  placeholder="请输入费率"
                />
              </el-form-item>
              <span style="margin-left: 10px">%</span>
            </div>
            <span>+</span>
            <div style="display: flex; align-items: center">
              <span style="margin-right: 10px">固定金额:</span>
              <el-form-item prop="fixed_amount" style="margin-bottom: 0">
                <el-input-number
                  v-model="boostForm.fixed_amount"
                  :min="0"
                  :precision="2"
                  style="width: 150px"
                  placeholder="请输入金额"
                />
              </el-form-item>
              <span style="margin-left: 10px">元</span>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="开始时间:" prop="start_time">
          <el-date-picker
            v-model="boostForm.start_time"
            type="datetime"
            placeholder="选择开始时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="结束时间:" prop="end_time">
          <el-date-picker
            v-model="boostForm.end_time"
            type="datetime"
            placeholder="选择结束时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="对接群名:" prop="live_content">
          <el-input v-model="boostForm.live_content" placeholder="请输入对接群名" />
        </el-form-item>

        <el-form-item label="峰值在线:" prop="live_address">
          <el-input v-model="boostForm.live_address" placeholder="请输入峰值在线人数" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseBoostDialog">取消</el-button>
          <el-button type="primary" @click="handleSubmitBoost">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture, User } from '@element-plus/icons-vue'
import axios from 'axios'

// 商品接口
interface Product {
  id: string
  name: string
  price: number
  merchant_commission: string
  talent_commission: string
  investment_commission: string
  merchant_id: string
  merchant_name: string
  create_time: string
  image_url: string
  category: string
  stock: number
  sales: number
  status: number
  store_score: string
  activity_id: string
  control_commission_score?: number
  cooperation_score?: number
  trust_score?: number
  cost_performance_score?: number
  routine_score?: number
  assist_broadcast_score?: number
  remark?: string
  tag?: string
  talent_id: string
  talent_name: string
  promotion_time: string
}

// 达人接口
interface Talent {
  talent_id: string
  talent_name: string
  talent_avatar?: string
  fans_count?: string
}

// 助播申请表单接口
interface BoostForm {
  talent_id: string
  target_gmv_rate: number | null
  fixed_amount: number | null
  start_time: Date | null
  end_time: Date | null
  live_content: string
  live_address: string
}

// 搜索表单
const searchForm = reactive({
  product_name: '',
  product_id: '',
  talent_id: '',
  talent_name: '',
  min_price: '',
  max_price: '',
  category: '',
  tag: '',
  sort_by: 'promotion_time',
  sort_order: 'desc',
})

// 商品列表
const productList = ref<Product[]>([])

// 分页
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)
const loading = ref(false)

// 助播相关数据
const boostDialogVisible = ref(false)
const boostFormRef = ref()
const currentProduct = ref<Product | null>(null)
const talentList = ref<Talent[]>([])

// 助播申请表单
const boostForm = reactive<BoostForm>({
  talent_id: '',
  target_gmv_rate: null,
  fixed_amount: null,
  start_time: null,
  end_time: null,
  live_content: '',
  live_address: '',
})

// 助播表单验证规则
const boostRules = {
  talent_id: [{ required: true, message: '请选择助播', trigger: 'change' }],
  target_gmv_rate: [{ required: true, message: '请输入GMV返点费率', trigger: 'blur' }],
  fixed_amount: [{ required: true, message: '请输入固定金额', trigger: 'blur' }],
  start_time: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  end_time: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  live_content: [{ required: true, message: '请输入对接群名', trigger: 'blur' }],
  live_address: [{ required: true, message: '请输入峰值在线', trigger: 'blur' }],
}

// 搜索
const handleSearch = async () => {
  try {
    loading.value = true

    // 获取token
    const token = localStorage.getItem('token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      product_name: searchForm.product_name,
      product_id: searchForm.product_id,
      talent_id: searchForm.talent_id,
      talent_name: searchForm.talent_name,
      min_price: searchForm.min_price,
      max_price: searchForm.max_price,
      category: searchForm.category,
      tag: searchForm.tag,
      sort_by: searchForm.sort_by,
      sort_order: searchForm.sort_order,
    }

    const response = await axios.get('/api/product/my-promotions', {
      params,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (response.data.code === 0) {
      productList.value = response.data.data.list
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.message || '获取推广商品列表失败')
    }
  } catch (error: any) {
    console.error('获取推广商品列表失败:', error)
    ElMessage.error('获取推广商品列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.product_name = ''
  searchForm.product_id = ''
  searchForm.talent_id = ''
  searchForm.talent_name = ''
  searchForm.min_price = ''
  searchForm.max_price = ''
  searchForm.category = ''
  searchForm.tag = ''
  searchForm.sort_by = 'promotion_time'
  searchForm.sort_order = 'desc'
  currentPage.value = 1
  handleSearch()
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

// 检查商品是否有评分
const hasAnyScore = (product: Product): boolean => {
  return !!(
    product.control_commission_score ||
    product.cooperation_score ||
    product.trust_score ||
    product.cost_performance_score ||
    product.routine_score ||
    product.assist_broadcast_score
  )
}

// 编辑评分
const handleEditScores = (product: Product) => {
  console.log('编辑评分', product)
  // 这里可以跳转到商品管理页面进行评分编辑
}

// 移除推广
const handleRemovePromotion = async (product: Product) => {
  try {
    await ElMessageBox.confirm(`确定要移除商品"${product.name}"的推广吗？`, '确认移除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 获取token
    const token = localStorage.getItem('token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    const response = await axios.post(
      '/api/product/remove-promotion',
      {
        product_id: product.id,
        talent_id: product.talent_id,
        talent_name: product.talent_name,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )

    if (response.data.code === 0) {
      ElMessage.success('移除推广成功')
      handleSearch()
    } else {
      ElMessage.error(response.data.message || '移除推广失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('当前活动状态不允许修改达人信息')
    }
  }
}

// 查看详情
const handleViewDetail = (product: Product) => {
  console.log('查看详情', product)
  // 这里可以跳转到商品详情页面
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  handleSearch()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}

// 申请助播
const handleApplyBoost = async (product: Product) => {
  currentProduct.value = product
  boostDialogVisible.value = true

  // 加载达人列表
  await loadTalentList()
}

// 加载达人列表（只加载当前商务负责的达人）
const loadTalentList = async () => {
  try {
    const token = localStorage.getItem('token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    // 获取当前用户信息
    const userInfo = localStorage.getItem('user')
    if (!userInfo) {
      ElMessage.error('用户信息不存在')
      return
    }

    const user = JSON.parse(userInfo)
    const businessContact = user.name

    // 获取当前商务负责的达人列表（包括专享、专属和共享达人）
    const responses = await Promise.all([
      // 专享达人
      axios.get('/api/talent/list', {
        headers: { Authorization: `Bearer ${token}` },
        params: { talent_type: 'special', business_contact: businessContact, page_size: 1000 },
      }),
      // 专属达人
      axios.get('/api/talent/list', {
        headers: { Authorization: `Bearer ${token}` },
        params: { talent_type: 'exclusive', business_contact: businessContact, page_size: 1000 },
      }),
      // 共享达人
      axios.get('/api/talent/list', {
        headers: { Authorization: `Bearer ${token}` },
        params: { talent_type: 'shared', business_contact: businessContact, page_size: 1000 },
      }),
    ])

    const allTalents: any[] = []
    responses.forEach((response) => {
      if (response.data.code === 0 && response.data.data.list) {
        allTalents.push(...response.data.data.list)
      }
    })

    talentList.value = allTalents.map((talent) => ({
      talent_id: talent.talent_id,
      talent_name: talent.talent_name,
      talent_avatar: talent.avatar_url,
      fans_count: talent.fans_count,
    }))
  } catch (error) {
    console.error('加载达人列表失败:', error)
    ElMessage.error('加载达人列表失败')
  }
}

// 达人选择变化
const handleTalentChange = (talentId: string) => {
  // 可以在这里根据选择的达人更新其他信息
  console.log('选择的达人ID:', talentId)
}

// 关闭助播对话框
const handleCloseBoostDialog = () => {
  boostDialogVisible.value = false
  // 重置表单
  Object.assign(boostForm, {
    talent_id: '',
    target_gmv_rate: null,
    fixed_amount: null,
    start_time: null,
    end_time: null,
    live_content: '',
    live_address: '',
  })
  boostFormRef.value?.clearValidate()
}

// 提交助播申请
const handleSubmitBoost = async () => {
  try {
    // 表单验证
    await boostFormRef.value?.validate()

    if (!currentProduct.value) {
      ElMessage.error('商品信息丢失')
      return
    }

    const token = localStorage.getItem('token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    // 构建提交数据
    const submitData = {
      product_id: currentProduct.value.id,
      product_name: currentProduct.value.name,
      talent_id: boostForm.talent_id,
      target_gmv_rate: boostForm.target_gmv_rate,
      fixed_amount: boostForm.fixed_amount,
      start_time: boostForm.start_time,
      end_time: boostForm.end_time,
      live_content: boostForm.live_content,
      live_address: boostForm.live_address,
    }

    const response = await axios.post('/api/boost/apply', submitData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })

    if (response.data.code === 0) {
      ElMessage.success('助播申请提交成功')
      handleCloseBoostDialog()
    } else {
      ElMessage.error(response.data.message || '助播申请提交失败')
    }
  } catch (error: any) {
    if (error.message) {
      ElMessage.error(error.message)
    } else {
      console.error('提交助播申请失败:', error)
      ElMessage.error('助播申请提交失败')
    }
  }
}

onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.my-promotion-container {
  padding: 20px;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
}

:deep(.el-card) {
  width: 100%;
  height: 100%;
  max-width: 100%;
}

:deep(.el-card__body) {
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
}

.price-separator {
  margin: 0 5px;
  color: #909399;
}

.product-cards-container {
  min-height: 400px;
  flex: 1;
}

.product-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.product-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-card-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-image {
  height: 200px;
  width: 100%;
  overflow: hidden;
}

.product-image .el-image {
  width: 100%;
  height: 100%;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
}

.image-placeholder .el-icon {
  font-size: 40px;
  color: #c0c4cc;
}

.product-info {
  padding: 15px 0;
  flex: 1;
}

.product-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  height: 48px;
}

.product-price {
  font-size: 18px;
  color: #ff6b6b;
  font-weight: bold;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.meta-item {
  display: flex;
}

.meta-label {
  color: #909399;
  margin-right: 4px;
}

.meta-value {
  color: #606266;
}

.commission-info {
  margin-top: 10px;
  padding: 8px;
  background-color: #fff7e6;
  border-radius: 4px;
  border-left: 3px solid #ff9900;
}

.commission-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.commission-item:last-child {
  margin-bottom: 0;
}

.commission-label {
  color: #ff9900;
  font-weight: bold;
}

.commission-value {
  color: #606266;
  font-weight: bold;
}

.promotion-info {
  margin-top: 10px;
  padding: 8px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.promotion-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.promotion-item:last-child {
  margin-bottom: 0;
}

.promotion-label {
  color: #409eff;
  font-weight: bold;
}

.promotion-value {
  color: #606266;
}

.product-scores {
  margin-top: 10px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.score-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 12px;
}

.score-item:last-child {
  margin-bottom: 0;
}

.score-label {
  width: 40px;
  color: #606266;
  margin-right: 8px;
}

.product-category {
  margin-top: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-tag-display {
  margin-top: 8px;
}

.product-actions {
  display: flex;
  justify-content: space-around;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

/* 助播选择下拉框样式 */
:deep(.talent-select-dropdown) {
  min-width: 300px !important;
}

:deep(.talent-option) {
  height: auto !important;
  padding: 8px 12px !important;
  line-height: normal !important;
}

.talent-option-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.talent-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.talent-info {
  flex: 1;
  min-width: 0;
}

.talent-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.talent-fans {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  width: 100%;
  display: flex;
  justify-content: center;
}
</style>
