from functools import wraps
from flask import request, jsonify
import jwt
import os

# JWT密钥
SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')

def token_required(f):
    """验证JWT token的装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            
            # 构建用户信息
            current_user = {
                'user_id': payload.get('user_id'),
                'username': payload.get('username'),
                'name': payload.get('name'),
                'role': payload.get('role', 'business'),
                'is_admin': payload.get('is_admin', False),
                'avatar': payload.get('avatar'),
                'phone': payload.get('phone')
            }
            
            # 将用户信息作为参数传递给被装饰的函数
            return f(current_user, *args, **kwargs)
            
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
    
    return decorated

def admin_required(f):
    """验证管理员权限的装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            role = payload.get('role', 'business')
            
            if role != 'admin':
                return jsonify({
                    'code': 403,
                    'message': '权限不足',
                    'data': None
                }), 403
            
            # 构建用户信息
            current_user = {
                'user_id': payload.get('user_id'),
                'username': payload.get('username'),
                'name': payload.get('name'),
                'role': role,
                'is_admin': True,
                'avatar': payload.get('avatar'),
                'phone': payload.get('phone')
            }
            
            # 将用户信息作为参数传递给被装饰的函数
            return f(current_user, *args, **kwargs)
                
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
    
    return decorated

def operation_required(f):
    """验证运营权限的装饰器（运营和管理员都可以访问）"""
    @wraps(f)
    def decorated(*args, **kwargs):
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            role = payload.get('role', 'business')
            
            # 管理员和运营都可以访问运营功能
            if role not in ['operation', 'admin']:
                return jsonify({
                    'code': 403,
                    'message': '权限不足',
                    'data': None
                }), 403
            
            # 构建用户信息
            current_user = {
                'user_id': payload.get('user_id'),
                'username': payload.get('username'),
                'name': payload.get('name'),
                'role': role,
                'is_admin': (role == 'admin'),
                'avatar': payload.get('avatar'),
                'phone': payload.get('phone')
            }
            
            # 将用户信息作为参数传递给被装饰的函数
            return f(current_user, *args, **kwargs)
                
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
    
    return decorated
