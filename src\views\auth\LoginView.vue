<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>苍穹严选管理系统</h2>
      </div>
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入账号"
            prefix-icon="el-icon-user"
            clearable
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="el-icon-lock"
            show-password
            clearable
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="captcha">
          <div class="captcha-container">
            <el-input v-model="loginForm.captcha" placeholder="请输入验证码" class="captcha-input">
              <template #prefix>
                <el-icon><Key /></el-icon>
              </template>
            </el-input>
            <div class="captcha-image" @click="refreshCaptcha">
              <img :src="captchaUrl" alt="验证码" />
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button :loading="loading" type="primary" class="login-button" @click="handleLogin">
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance } from 'element-plus'
import { User, Lock, Key } from '@element-plus/icons-vue'
import axios from 'axios'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const loginFormRef = ref<FormInstance>()
const loading = ref(false)
const captchaUrl = ref('')
const captchaKey = ref('')

// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
  captcha: '',
})

// 表单验证规则
const loginRules = reactive({
  username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
})

// 获取验证码
const getCaptcha = async () => {
  try {
    const response = await axios.get('/api/auth/captcha')
    if (response.data.code === 0) {
      captchaUrl.value = response.data.data.captcha_image
      captchaKey.value = response.data.data.captcha_key
    } else {
      ElMessage.error('获取验证码失败')
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error('获取验证码失败，请检查网络连接')
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  loginForm.captcha = ''
  getCaptcha()
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const response = await axios.post('/api/auth/login', {
          username: loginForm.username,
          password: loginForm.password,
          captcha: loginForm.captcha,
          captcha_key: captchaKey.value,
        })

        if (response.data.code === 0) {
          // 保存令牌到本地存储
          localStorage.setItem('token', response.data.data.token)

          // 保存完整的用户信息
          const user = response.data.data.user
          localStorage.setItem(
            'user',
            JSON.stringify({
              id: user.id,
              username: user.username,
              name: user.name,
              role: user.role,
              is_admin: user.is_admin,
              avatar: user.avatar || '/assets/default-avatar.png',
              phone: user.phone || '',
              permissions: user.permissions || null,
            }),
          )

          ElMessage.success('登录成功')

          // 根据用户角色跳转到不同页面
          if (response.data.data.user.role === 'admin') {
            // 如果是管理员，跳转到管理员后台
            router.push('/admin/dashboard')
          } else {
            // 商务和运营用户都跳转到首页
            router.push('/')
          }
        } else {
          ElMessage.error(response.data.message || '登录失败')
          // 如果验证码错误，刷新验证码
          if (response.data.message.includes('验证码')) {
            refreshCaptcha()
          }
        }
      } catch (error: any) {
        if (error.response && error.response.data) {
          ElMessage.error(error.response.data.message || '登录失败')
          // 如果验证码错误，刷新验证码
          if (error.response.data.message && error.response.data.message.includes('验证码')) {
            refreshCaptcha()
          }
        } else {
          ElMessage.error('登录失败，请检查网络连接')
        }
      } finally {
        loading.value = false
      }
    }
  })
}

onMounted(() => {
  getCaptcha()
})
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.login-box {
  width: 400px;
  padding: 40px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #409eff;
}

.login-header p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.login-form {
  margin-top: 30px;
}

.login-button {
  width: 100%;
}

.captcha-container {
  display: flex;
  align-items: center;
}

.captcha-input {
  flex: 1;
  margin-right: 10px;
}

.captcha-image {
  width: 120px;
  height: 40px;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.captcha-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
