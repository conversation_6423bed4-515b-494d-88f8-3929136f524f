"""
达人商务关联同步服务
定期同步商品推广关联表和订单表，更新订单中的talent_business字段
"""

import threading
import time
import logging
from datetime import datetime
from app.utils.db_utils import get_connection

class TalentBusinessSyncService:
    def __init__(self):
        self.sync_running = False
        self.sync_thread = None
        self.stop_event = threading.Event()
        self.sync_interval = 10  # 10秒同步一次

        logging.info("🔗 达人商务关联同步服务初始化完成")
        print("🔗 达人商务关联同步服务初始化完成")

    def start(self):
        """启动同步服务"""
        # 重置停止事件，确保可以重新启动
        self.stop_event.clear()

        if not self.sync_thread or not self.sync_thread.is_alive():
            self.sync_thread = threading.Thread(target=self._sync_loop, daemon=True)
            self.sync_thread.start()
            logging.info("🔗 达人商务关联同步线程已启动")
            print("🔗 达人商务关联同步线程已启动")

    def stop(self):
        """停止同步服务"""
        self.stop_event.set()
        logging.info("🔗 达人商务关联同步服务停止信号已发送")
        print("🔗 达人商务关联同步服务停止信号已发送")

        # 等待线程结束（最多等待10秒）
        if self.sync_thread and self.sync_thread.is_alive():
            self.sync_thread.join(timeout=10)
            if self.sync_thread.is_alive():
                logging.warning("达人商务关联同步线程未能在10秒内停止")
            else:
                logging.info("🔗 达人商务关联同步线程已停止")
                print("🔗 达人商务关联同步线程已停止")
    
    def _sync_loop(self):
        """同步循环"""
        while not self.stop_event.is_set():
            try:
                if not self.sync_running:
                    self._sync_talent_business()
                else:
                    logging.info("达人商务关联同步正在进行中，跳过本次同步")
                
                # 等待下次同步
                self.stop_event.wait(self.sync_interval)
                
            except Exception as e:
                logging.error(f"达人商务关联同步循环出错: {str(e)}")
                print(f"❌ 达人商务关联同步循环出错: {str(e)}")
                self.stop_event.wait(60)  # 出错后等待1分钟再重试
    
    def _sync_talent_business(self):
        """同步达人商务关联数据"""
        self.sync_running = True
        start_time = datetime.now()
        
        connection = None
        cursor = None
        
        try:
            
            # 获取数据库连接
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # 1. 获取商品推广关联表中的所有数据
            promotion_query = """
                SELECT DISTINCT talent_id, product_id, business_name 
                FROM product_promotion 
                WHERE talent_id IS NOT NULL 
                AND product_id IS NOT NULL 
                AND business_name IS NOT NULL
            """
            cursor.execute(promotion_query)
            promotions = cursor.fetchall()
            
            if not promotions:
                print("📝 商品推广关联表中没有数据，跳过同步")
                return
            
            
            # 2. 使用JOIN方式进行批量更新，更高效
            update_sql = """
                UPDATE `order` o
                INNER JOIN product_promotion pp ON o.promoter_id = pp.talent_id AND o.product_id = pp.product_id
                SET o.talent_business = pp.business_name
                WHERE (o.talent_business IS NULL OR o.talent_business = '' OR o.talent_business != pp.business_name)
                AND pp.talent_id IS NOT NULL
                AND pp.product_id IS NOT NULL
                AND pp.business_name IS NOT NULL
            """

            # 执行更新
            cursor.execute(update_sql)

            # 提交事务
            connection.commit()


        except Exception as e:
            print(f"❌ 达人商务关联同步失败: {str(e)}")
            logging.error(f"达人商务关联同步失败: {str(e)}")
            
            # 回滚事务
            if connection:
                try:
                    connection.rollback()
                except:
                    pass
                    
        finally:
            self.sync_running = False
            
            # 关闭数据库连接
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    


# 全局达人商务关联同步服务实例
talent_business_sync_service = TalentBusinessSyncService()
