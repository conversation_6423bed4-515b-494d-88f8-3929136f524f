from flask import Blueprint, request, jsonify
from app.utils.db_utils import get_connection
from app.utils.auth import token_required
from datetime import datetime
import logging
import re

# 创建蓝图
boost_bp = Blueprint('boost', __name__)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def parse_datetime(datetime_str):
    """解析前端传来的日期时间字符串"""
    if not datetime_str:
        return None

    try:
        # 处理ISO格式的日期时间字符串
        if isinstance(datetime_str, str):
            # 移除毫秒和时区信息，转换为MySQL可接受的格式
            # 2025-07-08T16:00:00.000Z -> 2025-07-08 16:00:00
            datetime_str = re.sub(r'T', ' ', datetime_str)
            datetime_str = re.sub(r'\.\d{3}Z?$', '', datetime_str)
            datetime_str = re.sub(r'Z$', '', datetime_str)

            # 尝试解析日期时间
            return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')

        return datetime_str
    except Exception as e:
        logger.error(f"日期时间解析失败: {datetime_str}, 错误: {str(e)}")
        return None

@boost_bp.route('/apply', methods=['POST'])
@token_required
def apply_boost(current_user):
    """申请助播"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['product_id', 'product_name', 'talent_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'code': 1, 'message': f'{field} 不能为空'})
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 获取商品信息
        product_query = """
        SELECT product_name, product_image_url, price
        FROM product
        WHERE product_id = %s
        """
        cursor.execute(product_query, (data['product_id'],))
        product_info = cursor.fetchone()
        
        # 获取达人信息
        talent_query = """
        SELECT talent_name, avatar_url, fans_count
        FROM talent
        WHERE talent_id = %s
        """
        cursor.execute(talent_query, (data['talent_id'],))
        talent_info = cursor.fetchone()
        
        if not talent_info:
            return jsonify({'code': 1, 'message': '达人信息不存在'})

        # 转换日期时间格式
        start_time = parse_datetime(data.get('start_time'))
        end_time = parse_datetime(data.get('end_time'))

        # 插入助播申请记录
        insert_query = """
        INSERT INTO boost_application (
            product_id, product_name, product_image, product_price,
            talent_id, talent_name, talent_avatar, talent_fans,
            business_contact, target_gmv_rate, fixed_amount,
            start_time, end_time, live_content, live_address,
            status, create_time
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NOW()
        )
        """
        
        cursor.execute(insert_query, (
            data['product_id'],
            product_info['product_name'] if product_info else data['product_name'],
            product_info['product_image_url'] if product_info else None,
            product_info['price'] if product_info else None,
            data['talent_id'],
            talent_info['talent_name'],
            talent_info.get('avatar_url'),
            talent_info.get('fans_count'),
            current_user['name'],  # 申请商务
            data.get('target_gmv_rate'),
            data.get('fixed_amount'),
            start_time,
            end_time,
            data.get('live_content', ''),
            data.get('live_address', '')
        ))
        
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '助播申请提交成功',
            'data': {'id': cursor.lastrowid}
        })
        
    except Exception as e:
        logger.error(f"申请助播失败: {str(e)}")
        return jsonify({'code': 1, 'message': '申请助播失败'})
    finally:
        if 'connection' in locals():
            connection.close()

@boost_bp.route('/list', methods=['GET'])
@token_required
def get_boost_list(current_user):
    """获取助播申请列表"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        status = request.args.get('status')
        business_search = request.args.get('business_search', '')
        talent_search = request.args.get('talent_search', '')
        product_search = request.args.get('product_search', '')
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 构建查询条件
        conditions = []
        params = []
        
        # 权限控制：商务只能查看自己的申请
        user_role = current_user.get('role', 'business')
        if user_role == 'business':
            conditions.append("business_contact = %s")
            params.append(current_user['name'])
        
        # 状态筛选
        if status is not None and status != '':
            conditions.append("status = %s")
            params.append(int(status))
        
        # 商务搜索
        if business_search:
            conditions.append("business_contact LIKE %s")
            params.append(f"%{business_search}%")
        
        # 达人搜索
        if talent_search:
            conditions.append("talent_name LIKE %s")
            params.append(f"%{talent_search}%")
        
        # 商品搜索
        if product_search:
            conditions.append("product_name LIKE %s")
            params.append(f"%{product_search}%")
        
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        # 查询总数
        count_query = f"SELECT COUNT(*) as total FROM boost_application WHERE {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 查询列表数据
        offset = (page - 1) * page_size
        list_query = f"""
        SELECT * FROM boost_application 
        WHERE {where_clause}
        ORDER BY create_time DESC
        LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        cursor.execute(list_query, params)
        boost_list = cursor.fetchall()
        
        # 格式化数据
        for item in boost_list:
            if item['create_time']:
                item['create_time'] = item['create_time'].strftime('%Y-%m-%d %H:%M:%S')
            if item['start_time']:
                item['start_time'] = item['start_time'].strftime('%Y-%m-%d %H:%M:%S')
            if item['end_time']:
                item['end_time'] = item['end_time'].strftime('%Y-%m-%d %H:%M:%S')
            if item['approve_time']:
                item['approve_time'] = item['approve_time'].strftime('%Y-%m-%d %H:%M:%S')
            if item['update_time']:
                item['update_time'] = item['update_time'].strftime('%Y-%m-%d %H:%M:%S')
        
        return jsonify({
            'code': 0,
            'message': '获取成功',
            'data': {
                'list': boost_list,
                'total': total,
                'page': page,
                'page_size': page_size
            }
        })
        
    except Exception as e:
        logger.error(f"获取助播列表失败: {str(e)}")
        return jsonify({'code': 1, 'message': '获取助播列表失败'})
    finally:
        if 'connection' in locals():
            connection.close()

@boost_bp.route('/update/<int:boost_id>', methods=['PUT'])
@token_required
def update_boost(current_user, boost_id):
    """更新助播申请（仅运营和管理员可操作）"""
    try:
        # 权限检查
        user_role = current_user.get('role', 'business')
        if user_role == 'business':
            return jsonify({'code': 1, 'message': '无权限操作'})
        
        data = request.get_json()
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 构建更新字段
        update_fields = []
        params = []
        
        # 可更新的字段
        updatable_fields = [
            'status', 'operation_notes', 'actual_gmv', 'actual_fee', 
            'settlement_status', 'target_gmv_rate', 'fixed_amount',
            'start_time', 'end_time', 'live_content', 'live_address'
        ]
        
        for field in updatable_fields:
            if field in data:
                update_fields.append(f"{field} = %s")
                # 对日期时间字段进行特殊处理
                if field in ['start_time', 'end_time']:
                    params.append(parse_datetime(data[field]))
                else:
                    params.append(data[field])
        
        if not update_fields:
            return jsonify({'code': 1, 'message': '没有可更新的字段'})
        
        # 添加更新时间和审核人
        update_fields.append("update_time = NOW()")
        if data.get('status') in [1, 2]:  # 通过或拒绝
            update_fields.append("approve_time = NOW()")
            update_fields.append("approve_user = %s")
            params.append(current_user['name'])
        
        params.append(boost_id)
        
        # 执行更新
        update_query = f"""
        UPDATE boost_application 
        SET {', '.join(update_fields)}
        WHERE id = %s
        """
        
        cursor.execute(update_query, params)
        connection.commit()
        
        if cursor.rowcount == 0:
            return jsonify({'code': 1, 'message': '助播申请不存在'})
        
        return jsonify({
            'code': 0,
            'message': '更新成功'
        })
        
    except Exception as e:
        logger.error(f"更新助播申请失败: {str(e)}")
        return jsonify({'code': 1, 'message': '更新助播申请失败'})
    finally:
        if 'connection' in locals():
            connection.close()

@boost_bp.route('/delete/<int:boost_id>', methods=['DELETE'])
@token_required
def delete_boost(current_user, boost_id):
    """删除助播申请（仅管理员可操作）"""
    try:
        # 权限检查
        user_role = current_user.get('role', 'business')
        if user_role != 'admin':
            return jsonify({'code': 1, 'message': '无权限操作'})
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor()
        
        # 删除记录
        delete_query = "DELETE FROM boost_application WHERE id = %s"
        cursor.execute(delete_query, (boost_id,))
        connection.commit()
        
        if cursor.rowcount == 0:
            return jsonify({'code': 1, 'message': '助播申请不存在'})
        
        return jsonify({
            'code': 0,
            'message': '删除成功'
        })
        
    except Exception as e:
        logger.error(f"删除助播申请失败: {str(e)}")
        return jsonify({'code': 1, 'message': '删除助播申请失败'})
    finally:
        if 'connection' in locals():
            connection.close()
