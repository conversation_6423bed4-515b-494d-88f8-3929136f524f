import { ref } from 'vue'
import { defineStore } from 'pinia'
import axios from 'axios'

export interface UserPermissions {
  perm_talent: number
  perm_team_leader: number
  perm_product: number
  perm_order: number
  perm_boost: number
  perm_performance_overview: number
  perm_performance_statistics: number
  perm_sample_management: number
  perm_hide_product: number
  perm_manage_tags: number
  perm_merchant_commission_orders: number
  perm_reserved_service_fee_orders: number
  perm_team_leader_commission_orders: number
  perm_violation_fee_orders: number
  permissions: {
    talent: { data: string[]; operations: string[] }
    team_leader: { data: string[]; operations: string[] }
    product: { data: string[]; operations: string[] }
    order: { data: string[]; operations: string[]; modules: string[] }
    boost: { data: string[]; operations: string[] }
    performance: { data: string[]; operations: string[]; modules: string[] }
    sample: { data: string[]; operations: string[] }
  }
}

export const usePermissionStore = defineStore('permission', () => {
  const permissions = ref<UserPermissions | null>(null)
  const loading = ref(false)

  // 设置权限信息
  function setPermissions(perms: UserPermissions) {
    permissions.value = perms
  }

  // 获取用户权限
  async function fetchUserPermissions() {
    loading.value = true
    try {
      const response = await axios.get('/api/auth/user-permissions')
      if (response.data.code === 0) {
        setPermissions(response.data.data)
        return response.data.data
      } else {
        console.error('获取权限失败:', response.data.message)
        return null
      }
    } catch (error) {
      console.error('获取权限失败:', error)
      return null
    } finally {
      loading.value = false
    }
  }

  // 检查是否有某个模块的权限
  function hasPermission(permissionKey: keyof UserPermissions): boolean {
    if (!permissions.value) return false
    return permissions.value[permissionKey] === 1
  }

  // 清除权限信息
  function clearPermissions() {
    permissions.value = null
  }

  // 获取所有模块权限状态
  const modulePermissions = {
    talent: () => hasPermission('perm_talent'),
    teamLeader: () => hasPermission('perm_team_leader'),
    product: () => hasPermission('perm_product'),
    order: () => hasPermission('perm_order'),
    boost: () => hasPermission('perm_boost'),
    performanceOverview: () => hasPermission('perm_performance_overview'),
    performanceStatistics: () => hasPermission('perm_performance_statistics'),
    sampleManagement: () => hasPermission('perm_sample_management'),
    hideProduct: () => hasPermission('perm_hide_product'),
    manageTags: () => hasPermission('perm_manage_tags'),
    merchantCommissionOrders: () => hasPermission('perm_merchant_commission_orders'),
    reservedServiceFeeOrders: () => hasPermission('perm_reserved_service_fee_orders'),
    teamLeaderCommissionOrders: () => hasPermission('perm_team_leader_commission_orders'),
    violationFeeOrders: () => hasPermission('perm_violation_fee_orders'),
  }

  return {
    permissions,
    loading,
    setPermissions,
    fetchUserPermissions,
    hasPermission,
    clearPermissions,
    modulePermissions,
  }
})
