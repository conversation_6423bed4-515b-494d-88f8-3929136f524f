#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手小店达人列表获取脚本
用于调用快手小店API获取活动达人详情信息
"""

import requests
import json
import time
from typing import Optional, Dict, Any, List


def get_talent_activity_detail(
    cookie: str,
    activity_id: int,
    investment_source: int = 2,
    proxy: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取快手小店活动达人详情
    
    Args:
        cookie: 用户认证cookie
        activity_id: 活动ID
        investment_source: 投资来源，默认2
        proxy: 代理地址，如 "http://127.0.0.1:7890"
    
    Returns:
        API响应结果字典
    """
    
    # API URL
    url = "https://cps.kwaixiaodian.com/gateway/distribute/platform/investment/activity/get"
    
    # 请求参数
    params = {
        "activityId": activity_id,
        "investmentSource": investment_source
    }
    
    # 请求头
    headers = {
        "accept": "application/json",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "businesskey": "454b9798-15ae-4ba6-9d39-1fd6c08e7357",
        "cookie": cookie,
        "kpf": "PC_WEB",
        "kpn": "KWAIXIAODIAN",
        "priority": "u=1, i",
        "referer": f"https://cps.kwaixiaodian.com/pc/leader/base/activity-detail?activityId={activity_id}&source=copy",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "trace-id": f"1.0.0.{int(time.time() * 1000)}.1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    }
    
    # 设置代理
    proxies = None
    if proxy:
        proxies = {
            "http": proxy,
            "https": proxy
        }
    
    try:
        # 发送GET请求
        response = requests.get(
            url=url,
            params=params,
            headers=headers,
            proxies=proxies,
            timeout=30
        )
        
        # 检查响应状态
        response.raise_for_status()
        
        # 解析JSON响应
        result = response.json()
        
        print(f"请求成功！")
        print(f"响应状态码: {response.status_code}")
        print(f"活动ID: {activity_id}")
        
        # 提取关键信息
        if result.get("result") == 1 and "data" in result:
            data = result["data"]
            print(f"活动标题: {data.get('activityTitle', 'N/A')}")
            print(f"活动状态: {data.get('activityStatus', 'N/A')}")
            print(f"达人昵称: {data.get('activityUserNickName', 'N/A')}")
            print(f"达人ID: {data.get('activityUserId', 'N/A')}")
            
            # 专属用户列表
            exclusive_users = data.get('activityExclusiveUserList', [])
            if exclusive_users:
                print(f"专属用户数量: {len(exclusive_users)}")
                for i, user in enumerate(exclusive_users[:5], 1):  # 只显示前5个
                    print(f"  {i}. {user.get('name', 'N/A')} (ID: {user.get('userId', 'N/A')})")
                if len(exclusive_users) > 5:
                    print(f"  ... 还有 {len(exclusive_users) - 5} 个用户")
            
            # 达人统计信息
            user_info = data.get('activityUserInfo', {})
            if user_info:
                print(f"达人等级: {user_info.get('activityUserLevel', 'N/A')}")
                print(f"30天平均佣金率: {user_info.get('avgCommissionRate30dFuzzy', 'N/A')}")
                print(f"30天推广GMV: {user_info.get('promotionGmv30dFuzzy', 'N/A')}")
                print(f"30天推广商品数: {user_info.get('promotionItemCount30dFuzzy', 'N/A')}")
        
        return result
        
    except requests.exceptions.RequestException as e:
        error_result = {
            "error": True,
            "error_type": "RequestException",
            "error_message": str(e),
            "status_code": getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None
        }
        print(f"请求失败: {error_result}")
        return error_result
        
    except json.JSONDecodeError as e:
        error_result = {
            "error": True,
            "error_type": "JSONDecodeError", 
            "error_message": str(e),
            "raw_response": response.text if 'response' in locals() else None
        }
        print(f"JSON解析失败: {error_result}")
        return error_result
        
    except Exception as e:
        error_result = {
            "error": True,
            "error_type": "UnknownError",
            "error_message": str(e)
        }
        print(f"未知错误: {error_result}")
        return error_result


def extract_talent_info(api_response: Dict[str, Any]) -> Dict[str, Any]:
    """
    从API响应中提取达人关键信息
    
    Args:
        api_response: API响应结果
    
    Returns:
        提取的达人信息字典
    """
    if api_response.get("error") or api_response.get("result") != 1:
        return {"error": "API响应异常"}
    
    data = api_response.get("data", {})
    user_info = data.get("activityUserInfo", {})
    
    talent_info = {
        "activity_id": data.get("activityId"),
        "activity_title": data.get("activityTitle"),
        "activity_status": data.get("activityStatus"),
        "talent_id": data.get("activityUserId"),
        "talent_nickname": data.get("activityUserNickName"),
        "talent_level": user_info.get("activityUserLevel"),
        "head_url": user_info.get("headUrl"),
        "main_channels": user_info.get("mainChannel", []),
        "contact_info": user_info.get("contactInfo", []),
        "stats_30d": {
            "avg_commission_rate": user_info.get("avgCommissionRate30dFuzzy"),
            "promotion_gmv": user_info.get("promotionGmv30dFuzzy"),
            "promotion_item_count": user_info.get("promotionItemCount30dFuzzy"),
            "promotion_volume": user_info.get("promotionVolume30dFuzzy"),
            "promotion_seller_count": user_info.get("promotionSellerCount30dFuzzy"),
            "item_avg_price": user_info.get("itemAvgPrice30d")
        },
        "exclusive_users": [
            {
                "user_id": user.get("userId"),
                "name": user.get("name")
            }
            for user in data.get("activityExclusiveUserList", [])
        ],
        "activity_times": {
            "begin_time": data.get("activityBeginTime"),
            "end_time": data.get("activityEndTime"),
            "create_time": data.get("createTime")
        }
    }
    
    return talent_info


def timestamp_to_readable(timestamp_ms: int) -> str:
    """将毫秒时间戳转换为可读格式"""
    if not timestamp_ms:
        return "N/A"
    return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp_ms / 1000))


if __name__ == "__main__":
    # 示例用法
    
    # 示例cookie（需要替换为实际的cookie）
    sample_cookie = "_did=web_6109536701C516C8; did=web_m5p31mdccrdqpmzfrlb5k0pb18ya7tn1; sid=kuaishou.shop.b; bUserId=1000040627146; userId=2885180614; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAFmZiuRCsCOXm7yHry9XEtJXmQJZq3-SYt9gj7LI1T0GaFXLiNLRWUILYGfqoqMh2TEtn9i-u3SH0Ow-0Os29OIrky7V2-D5XWLTxZm0OnE7nU-EwPQVnXmI6AP63DPN1w3TJX0fvKXKgPxI3fRNuchgqSt1_SodD8xlnFNB7ePs0fW12y7t9J_yPYHZSnHZ6R606_rRZWLVNOLx1UhyZWTGhJEwYN8fze1y97CKEMszZ1sXx4iIFWeIdhRDEmOwvpz8viMRkJmfabOleDPC3jM9pgvcWr5KAUwAQ; kuaishou.shop.b_ph=dc7ff5faa44a972a014870c3b0abb62e7ba2"
    
    # 示例活动ID
    sample_activity_id = 9173858085
    
    print(f"获取活动 {sample_activity_id} 的达人信息...")
    print("-" * 50)
    
    # 调用API获取达人信息
    result = get_talent_activity_detail(
        cookie=sample_cookie,
        activity_id=sample_activity_id,
        proxy="http://127.0.0.1:7890"  # 如果需要代理
    )
    
    print("-" * 50)
    
    # 提取并显示关键信息
    if not result.get("error"):
        talent_info = extract_talent_info(result)
        
        if not talent_info.get("error"):
            print("📊 达人详细信息:")
            print(f"活动标题: {talent_info['activity_title']}")
            print(f"达人昵称: {talent_info['talent_nickname']}")
            print(f"达人ID: {talent_info['talent_id']}")
            print(f"达人等级: {talent_info['talent_level']}")
            print(f"主要频道: {', '.join(talent_info['main_channels'])}")
            
            print(f"\n📈 30天数据:")
            stats = talent_info['stats_30d']
            for key, value in stats.items():
                if value:
                    print(f"  {key}: {value}")
            
            print(f"\n👥 专属用户 ({len(talent_info['exclusive_users'])} 个):")
            for user in talent_info['exclusive_users'][:10]:  # 显示前10个
                print(f"  - {user['name']} (ID: {user['user_id']})")
            
            print(f"\n⏰ 时间信息:")
            times = talent_info['activity_times']
            print(f"  活动开始: {timestamp_to_readable(times['begin_time'])}")
            print(f"  活动结束: {timestamp_to_readable(times['end_time'])}")
            print(f"  创建时间: {timestamp_to_readable(times['create_time'])}")
        else:
            print(f"❌ 信息提取失败: {talent_info['error']}")
    else:
        print(f"❌ API调用失败")
