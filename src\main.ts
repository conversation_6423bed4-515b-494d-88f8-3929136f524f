import './assets/main.css'
import './assets/styles/global.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

import App from './App.vue'
import router from './router'
import { usePermissionStore } from '@/stores/permission'

// 配置axios
axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL || ''

// 添加请求拦截器
axios.interceptors.request.use(
  (config) => {
    // 从本地存储获取令牌
    const token = localStorage.getItem('token')
    if (token) {
      // 在请求头中添加令牌
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 添加响应拦截器
axios.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      // 如果响应状态码为401（未授权），清除令牌并重定向到登录页
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('userRole')
      localStorage.removeItem('userAvatar')
      ElMessage.error('登录已过期，请重新登录')
      router.push('/login')
    }
    return Promise.reject(error)
  },
)

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 应用启动时验证令牌
const validateTokenOnStartup = async () => {
  const token = localStorage.getItem('token')
  if (token) {
    try {
      await axios.get('/api/auth/check')
    } catch (error) {
      // 令牌无效，清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      router.push('/login')
    }
  }
}

// 在挂载应用前验证令牌
validateTokenOnStartup().then(() => {
  app.mount('#app')
})
