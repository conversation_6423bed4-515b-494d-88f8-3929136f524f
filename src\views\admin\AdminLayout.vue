<template>
  <div class="admin-layout">
    <el-container>
      <el-aside width="220px" class="admin-sidebar">
        <div class="admin-logo">
          <h2>苍穹严选商务管理系统</h2>
          <div class="admin-subtitle">{{ isAdmin ? '管理员后台' : '运营后台' }}</div>
        </div>
        <el-menu
          router
          :default-active="activeMenu"
          class="admin-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <template v-if="isAdmin">
            <el-menu-item index="/admin/dashboard">
              <el-icon><DataBoard /></el-icon>
              <span>控制面板</span>
            </el-menu-item>
            <el-menu-item index="/admin/business-users">
              <el-icon><User /></el-icon>
              <span>用户管理</span>
            </el-menu-item>

            <!-- 管理员可以查看所有模块 -->
            <el-divider content-position="left">业务模块</el-divider>

            <el-sub-menu index="talent">
              <template #title>
                <el-icon><User /></el-icon>
                <span>达人管理</span>
              </template>
              <el-menu-item index="/admin/talent/public">公海达人</el-menu-item>
              <el-menu-item index="/admin/talent/exclusive">专属达人</el-menu-item>
              <el-menu-item index="/admin/talent/special">专享达人</el-menu-item>
              <el-menu-item index="/admin/talent/shared">共享达人</el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="team">
              <template #title>
                <el-icon><UserFilled /></el-icon>
                <span>团长管理</span>
              </template>
              <el-menu-item index="/admin/team/public">公海团长</el-menu-item>
              <el-menu-item index="/admin/team/exclusive">专属团长</el-menu-item>
              <el-menu-item index="/admin/team/special">专享团长</el-menu-item>
              <el-menu-item index="/admin/team/shared">共享团长</el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="product">
              <template #title>
                <el-icon><Goods /></el-icon>
                <span>商品管理</span>
              </template>
              <el-menu-item index="/admin/product/list">商品管理</el-menu-item>
              <el-menu-item index="/admin/product/hot-ranking">爆品排行</el-menu-item>
              <el-menu-item index="/admin/product/hidden">隐藏商品</el-menu-item>
              <el-menu-item index="/admin/product/link">
                <span>商品链接</span>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="order">
              <template #title>
                <el-icon><List /></el-icon>
                <span>订单管理</span>
              </template>
              <el-menu-item index="/admin/order/search">商务服务费订单</el-menu-item>
              <el-menu-item index="/admin/order/merchant-commission">商家返佣订单</el-menu-item>
              <el-menu-item index="/admin/order/reserved-service-fee">预留服务费订单</el-menu-item>
              <el-menu-item index="/admin/order/team-leader-commission">团长返佣订单</el-menu-item>
              <el-menu-item index="/admin/order/violation-fee">违规承担订单</el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="boost">
              <template #title>
                <el-icon><Promotion /></el-icon>
                <span>助播管理</span>
              </template>
              <el-menu-item index="/admin/boost/management">助播管理</el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="/admin/performance">
              <template #title>
                <el-icon><DataLine /></el-icon>
                <span>业绩管理</span>
              </template>
              <el-menu-item index="/admin/performance-overview">商务业绩概览</el-menu-item>
              <el-menu-item index="/admin/performance-statistics">商务业绩统计</el-menu-item>
              <el-menu-item index="/admin/talent-statistics">达人业绩统计</el-menu-item>
            </el-sub-menu>

            <el-menu-item index="/admin/sample/management">
              <el-icon><Box /></el-icon>
              <span>寄样管理</span>
            </el-menu-item>

            <el-menu-item index="/admin/system/config">
              <el-icon><Tools /></el-icon>
              <span>系统配置</span>
            </el-menu-item>

            <el-menu-item index="/admin/database-monitor">
              <el-icon><Monitor /></el-icon>
              <span>数据库监控</span>
            </el-menu-item>

            <el-menu-item index="/admin/profile">
              <el-icon><Setting /></el-icon>
              <span>个人设置</span>
            </el-menu-item>
          </template>
          <template v-else>
            <el-menu-item index="/operation/dashboard">
              <el-icon><DataBoard /></el-icon>
              <span>控制面板</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-aside>
      <el-container>
        <el-header class="admin-header">
          <div class="header-left">
            <h3>{{ pageTitle }}</h3>
          </div>
          <div class="header-right">
            <el-dropdown trigger="click">
              <div class="user-info">
                <el-avatar size="small" :src="userAvatar" class="user-avatar"></el-avatar>
                <span>{{ userName }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="navigateToProfile">个人设置</el-dropdown-item>
                  <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        <el-main class="admin-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  DataBoard,
  User,
  Back,
  ArrowDown,
  Setting,
  Goods,
  List,
  Promotion,
  UserFilled,
  DataLine,
  PieChart,
  Box,
  Tools,
  Monitor,
  Link,
} from '@element-plus/icons-vue'
import axios from 'axios'

const route = useRoute()
const router = useRouter()

// 当前激活的菜单项
const activeMenu = computed(() => route.path)

// 页面标题
const pageTitle = computed(() => {
  switch (route.path) {
    case '/admin/dashboard':
      return '控制面板'
    case '/admin/business-users':
      return '用户管理'
    case '/admin/profile':
      return '个人设置'
    // 达人管理
    case '/admin/talent/public':
      return '公海达人'
    case '/admin/talent/exclusive':
      return '专属达人'
    case '/admin/talent/special':
      return '专享达人'
    case '/admin/talent/shared':
      return '共享达人'
    // 团长管理
    case '/admin/team/public':
      return '公海团长'
    case '/admin/team/exclusive':
      return '专属团长'
    case '/admin/team/special':
      return '专享团长'
    case '/admin/team/shared':
      return '共享团长'
    // 商品管理
    case '/admin/product/list':
      return '商品管理'
    case '/admin/product/hot-ranking':
      return '爆品排行'
    case '/admin/product/hidden':
      return '隐藏商品'
    case '/admin/product/link':
      return '商品链接'
    // 订单管理
    case '/admin/order/search':
      return '服务费订单'
    case '/admin/order/merchant-commission':
      return '商家返佣订单'
    case '/admin/order/reserved-service-fee':
      return '预留服务费订单'
    case '/admin/order/team-leader-commission':
      return '团长返佣订单'
    case '/admin/order/violation-fee':
      return '违规承担订单'
    // 助播管理
    case '/admin/boost/management':
      return '助播管理'
    case '/admin/boost/add':
      return '助播添加'
    // 业绩管理
    case '/admin/performance-overview':
      return '商务业绩概览'
    case '/admin/performance-statistics':
      return '商务业绩统计'
    case '/admin/talent-statistics':
      return '达人业绩统计'
    // 寄样管理
    case '/admin/sample/management':
      return '寄样管理'
    // 系统配置
    case '/admin/system/config':
      return '系统配置'
    // 数据库监控
    case '/admin/database-monitor':
      return '数据库监控'
    case '/operation/dashboard':
      return '控制面板'
    default:
      return isAdmin.value ? '管理员后台' : '运营后台'
  }
})

// 判断是否是管理员
const isAdmin = computed(() => {
  return route.path.startsWith('/admin')
})

// 用户名
const userName = ref('管理员')
// 用户头像
const userAvatar = ref('/assets/default-avatar.png')

// 获取用户信息
const getUserInfo = () => {
  const userStr = localStorage.getItem('user')
  if (userStr) {
    try {
      const user = JSON.parse(userStr)
      userName.value = user.name || '管理员'
      // 如果有头像信息则使用，否则使用默认头像
      userAvatar.value = user.avatar || '/assets/default-avatar.png'
    } catch (error) {
      console.error('解析用户信息失败:', error)
    }
  }
}

// 定期检查用户信息更新
const checkUserInfoInterval = setInterval(() => {
  getUserInfo()
}, 2000) // 每2秒检查一次

// 监听localStorage变化，实时更新头像
const handleStorageChange = (event: StorageEvent) => {
  if (event.key === 'user') {
    getUserInfo()
  }
}

onMounted(() => {
  getUserInfo()
  window.addEventListener('storage', handleStorageChange)
})

onUnmounted(() => {
  clearInterval(checkUserInfoInterval)
  window.removeEventListener('storage', handleStorageChange)
})

// 退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await axios.post('/api/auth/logout')
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        router.push('/login')
        ElMessage.success('已成功退出登录')
      } catch (error) {
        console.error('退出登录失败:', error)
        // 即使API调用失败，也清除本地存储并跳转到登录页
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        router.push('/login')
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 导航到个人设置页面
const navigateToProfile = () => {
  router.push('/admin/profile')
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
  width: 100vw;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.admin-sidebar {
  background-color: #304156;
  height: 100%;
  overflow-x: hidden;
}

.admin-logo {
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  border-bottom: 1px solid #1f2d3d;
  padding: 10px 0;
}

.admin-logo h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.admin-subtitle {
  font-size: 14px;
  color: #bfcbd9;
  margin-top: 5px;
}

.admin-menu {
  border-right: none;
}

/* 增强菜单文本颜色对比度 */
:deep(.el-menu-item) {
  color: #ffffff !important;
}

:deep(.el-menu-item.is-active) {
  color: #409eff !important;
  background-color: #263445 !important;
}

:deep(.el-menu-item:hover) {
  background-color: #263445 !important;
}

.admin-header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  width: 100%;
}

.header-left h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-info span {
  margin-right: 5px;
}

.user-avatar {
  margin-right: 8px;
}

.admin-main {
  background-color: #f0f2f5;
  padding: 20px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  width: 100%;
}

/* 确保容器占满整个空间 */
.el-container {
  width: 100%;
  height: 100%;
}

.main-container {
  flex: 1;
  width: calc(100vw - 220px);
  display: flex;
  flex-direction: column;
}
</style>
