from flask import Blueprint, request, jsonify
import jwt
import os
from app.utils.db_utils import get_connection
from app.services.complete_auto_sync_service import complete_auto_sync_service

# JWT密钥
SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')

system_bp = Blueprint('system', __name__, url_prefix='/api/system')

def check_admin_permission(token):
    """检查管理员权限"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
        user_role = payload.get('role', 'business')
        user_id = payload.get('user_id')
        
        # 只有管理员有权限
        if user_role == 'admin':
            return True, user_id, user_role
        
        return False, None, None
        
    except jwt.ExpiredSignatureError:
        return False, None, None
    except jwt.InvalidTokenError:
        return False, None, None

@system_bp.route('/config', methods=['GET'])
def get_system_config():
    """获取系统配置"""
    connection = None
    cursor = None
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        has_permission, user_id, user_role = check_admin_permission(token)
        
        if not has_permission:
            return jsonify({
                'code': 403,
                'message': '权限不足，只有管理员可以查看系统配置',
                'data': None
            }), 403
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 获取所有系统配置
        query = """
        SELECT config_key, config_value, config_desc,
               DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
               DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time
        FROM system_config
        ORDER BY config_key
        """
        cursor.execute(query)
        configs = cursor.fetchall()
        
        return jsonify({
            'code': 0,
            'message': '获取系统配置成功',
            'data': configs
        })
        
    except Exception as e:
        print(f"获取系统配置失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取系统配置失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@system_bp.route('/config/<config_key>', methods=['PUT'])
def update_system_config(config_key):
    """更新系统配置"""
    connection = None
    cursor = None
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        has_permission, user_id, user_role = check_admin_permission(token)
        
        if not has_permission:
            return jsonify({
                'code': 403,
                'message': '权限不足，只有管理员可以修改系统配置',
                'data': None
            }), 403
        
        # 获取请求数据
        data = request.json
        if not data or 'config_value' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供配置值',
                'data': None
            }), 400
        
        config_value = data['config_value']
        config_desc = data.get('config_desc', '')
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查配置是否存在
        check_query = f"SELECT id FROM system_config WHERE config_key = '{config_key}'"
        cursor.execute(check_query)
        existing_config = cursor.fetchone()
        
        if existing_config:
            # 更新现有配置
            safe_value = config_value.replace("'", "''")
            safe_desc = config_desc.replace("'", "''")
            update_query = f"""
            UPDATE system_config 
            SET config_value = '{safe_value}', 
                config_desc = '{safe_desc}',
                update_time = NOW()
            WHERE config_key = '{config_key}'
            """
            cursor.execute(update_query)
        else:
            # 创建新配置
            safe_key = config_key.replace("'", "''")
            safe_value = config_value.replace("'", "''")
            safe_desc = config_desc.replace("'", "''")
            insert_query = f"""
            INSERT INTO system_config (config_key, config_value, config_desc, created_by, create_time, update_time)
            VALUES ('{safe_key}', '{safe_value}', '{safe_desc}', {user_id}, NOW(), NOW())
            """
            cursor.execute(insert_query)
        
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '更新系统配置成功',
            'data': {
                'config_key': config_key,
                'config_value': config_value
            }
        })
        
    except Exception as e:
        print(f"更新系统配置失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新系统配置失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@system_bp.route('/config/cookie', methods=['GET'])
def get_cookie_config():
    """获取Cookie配置（简化接口）"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 获取cookie配置
        query = f"SELECT config_value FROM system_config WHERE config_key = 'kuaishou_cookie'"
        cursor.execute(query)
        result = cursor.fetchone()
        
        cookie_value = result['config_value'] if result else ''
        
        return jsonify({
            'code': 0,
            'message': '获取Cookie配置成功',
            'data': {
                'cookie': cookie_value
            }
        })
        
    except Exception as e:
        print(f"获取Cookie配置失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取Cookie配置失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@system_bp.route('/auto-sync/start', methods=['POST'])
def start_auto_sync():
    """启动自动同步服务"""
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]
        has_permission, user_id, user_role = check_admin_permission(token)

        if not has_permission:
            return jsonify({
                'code': 403,
                'message': '权限不足，只有管理员可以启动自动同步',
                'data': None
            }), 403

        # 启动自动同步服务
        complete_auto_sync_service.start()

        return jsonify({
            'code': 0,
            'message': '自动同步服务启动成功',
            'data': None
        })

    except Exception as e:
        print(f"启动自动同步服务失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'启动自动同步服务失败: {str(e)}',
            'data': None
        }), 500

@system_bp.route('/auto-sync/stop', methods=['POST'])
def stop_auto_sync():
    """停止自动同步服务"""
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]
        has_permission, user_id, user_role = check_admin_permission(token)

        if not has_permission:
            return jsonify({
                'code': 403,
                'message': '权限不足，只有管理员可以停止自动同步',
                'data': None
            }), 403

        # 停止自动同步服务
        complete_auto_sync_service.stop()

        return jsonify({
            'code': 0,
            'message': '自动同步服务停止成功',
            'data': None
        })

    except Exception as e:
        print(f"停止自动同步服务失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'停止自动同步服务失败: {str(e)}',
            'data': None
        }), 500

@system_bp.route('/auto-sync/status', methods=['GET'])
def get_auto_sync_status():
    """获取自动同步服务状态"""
    try:
        # 验证权限
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]
        has_permission, user_id, user_role = check_admin_permission(token)

        if not has_permission:
            return jsonify({
                'code': 403,
                'message': '权限不足，只有管理员可以查看自动同步状态',
                'data': None
            }), 403

        # 获取同步状态
        status = complete_auto_sync_service.get_sync_status()

        return jsonify({
            'code': 0,
            'message': '获取自动同步状态成功',
            'data': status
        })

    except Exception as e:
        print(f"获取自动同步状态失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取自动同步状态失败: {str(e)}',
            'data': None
        }), 500
