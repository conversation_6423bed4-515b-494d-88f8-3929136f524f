#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手小店活动创建脚本
用于调用快手小店API创建推广活动
"""

import requests
import json
import time
from typing import List, Optional, Dict, Any


def create_kuaishou_activity(
    cookie: str,
    activity_begin_time: int,
    activity_end_time: int,
    activity_exclusive_user_list: List[str],
    activity_title: str = "7.31测试",
    activity_type: int = 2,
    activity_status: int = 2,
    pre_exclusive_activity_sign_type: int = 2,
    global_min_commission_rate: int = 0,
    min_investment_promotion_rate: int = 0,
    proxy: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建快手小店推广活动
    
    Args:
        cookie: 用户认证cookie
        activity_begin_time: 活动开始时间戳(毫秒)
        activity_end_time: 活动结束时间戳(毫秒)
        activity_exclusive_user_list: 专属用户列表
        activity_title: 活动标题，默认"7.31测试"
        activity_type: 活动类型，默认2
        activity_status: 活动状态，默认2
        pre_exclusive_activity_sign_type: 预专属活动签约类型，默认2
        global_min_commission_rate: 全局最小佣金率，默认0
        min_investment_promotion_rate: 最小投资推广率，默认0
        proxy: 代理地址，如 "http://127.0.0.1:7890"
    
    Returns:
        API响应结果字典
    """
    
    # API URL
    url = "https://cps.kwaixiaodian.com/gateway/distribute/platform/investment/activity/create"
    
    # 请求头
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "businesskey": "454b9798-15ae-4ba6-9d39-1fd6c08e7357",
        "content-type": "application/json",
        "cookie": cookie,
        "kpf": "PC_WEB",
        "kpn": "ks-csrf-token",
        "ktrace-str": f"3|My40NTgzNjk4Mjg2NzM2NzY5LjU1OTg5Njk0LjE3NTM5MDEwMTM0NDQuMTAxMw==|My40NTgzNjk4Mjg2NzM2NzY5LjI4OTgyMjQzLjE3NTM5MDEwMTM0NDQuMTAxMg==|0|plateco-kfx-service|plateco|true|src:Js,seqn:4193,rsi:63e423e0-52a8-4606-9de7-0a8d66215564,path:/pc/leader/originLeader/activity/my_activity/create,rpi:64800675ec",
        "origin": "https://cps.kwaixiaodian.com",
        "priority": "u=1, i",
        "referer": "https://cps.kwaixiaodian.com/pc/leader/originLeader/activity/my_activity/create?fromNew=1",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "trace-id": f"1.8369767669828067.342175390658.69604.{int(time.time() * 1000)}.6",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"
    }
    
    # 请求负载
    payload = {
        "activityType": activity_type,
        "activityTitle": activity_title,
        "activityBeginTime": activity_begin_time,
        "activityEndTime": activity_end_time,
        "activityStatus": activity_status,
        "activityExclusiveUserList": activity_exclusive_user_list,
        "preExclusiveActivitySignType": pre_exclusive_activity_sign_type,
        "activityRuleSet": {
            "promotionActivityMarketingRule": {
                "ruleType": 10,
                "globalMinCommissionRate": global_min_commission_rate,
                "categoryCommissionRateList": [],
                "minInvestmentPromotionRate": min_investment_promotion_rate
            }
        }
    }
    
    # 设置代理
    proxies = None
    if proxy:
        proxies = {
            "http": proxy,
            "https": proxy
        }
    
    try:
        # 发送POST请求
        response = requests.post(
            url=url,
            headers=headers,
            json=payload,
            proxies=proxies,
            timeout=30
        )
        
        # 检查响应状态
        response.raise_for_status()
        
        # 解析JSON响应
        result = response.json()
        
        print(f"请求成功！")
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        return result
        
    except requests.exceptions.RequestException as e:
        error_result = {
            "error": True,
            "error_type": "RequestException",
            "error_message": str(e),
            "status_code": getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None
        }
        print(f"请求失败: {error_result}")
        return error_result
        
    except json.JSONDecodeError as e:
        error_result = {
            "error": True,
            "error_type": "JSONDecodeError", 
            "error_message": str(e),
            "raw_response": response.text if 'response' in locals() else None
        }
        print(f"JSON解析失败: {error_result}")
        return error_result
        
    except Exception as e:
        error_result = {
            "error": True,
            "error_type": "UnknownError",
            "error_message": str(e)
        }
        print(f"未知错误: {error_result}")
        return error_result


def timestamp_to_readable(timestamp_ms: int) -> str:
    """将毫秒时间戳转换为可读格式"""
    return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp_ms / 1000))


def readable_to_timestamp(date_str: str) -> int:
    """将日期字符串转换为毫秒时间戳
    
    Args:
        date_str: 日期字符串，格式如 "2025-01-30 00:00:00"
    
    Returns:
        毫秒时间戳
    """
    time_struct = time.strptime(date_str, '%Y-%m-%d %H:%M:%S')
    return int(time.mktime(time_struct) * 1000)


if __name__ == "__main__":
    # 示例用法
    
    # 示例cookie（需要替换为实际的cookie）
    sample_cookie = "_did=web_6109536701C516C8; did=web_m5p31mdccrdqpmzfrlb5k0pb18ya7tn1; sid=kuaishou.shop.b; bUserId=1000040627146; userId=2885180614; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAFmZiuRCsCOXm7yHry9XEtJXmQJZq3-SYt9gj7LI1T0GaFXLiNLRWUILYGfqoqMh2TEtn9i-u3SH0Ow-0Os29OIrky7V2-D5XWLTxZm0OnE7nU-EwPQVnXmI6AP63DPN1w3TJX0fvKXKgPxI3fRNuchgqSt1_SodD8xlnFNB7ePs0fW12y7t9J_yPYHZSnHZ6R606_rRZWLVNOLx1UhyZWTGhJEwYN8fze1y97CKEMszZ1sXx4iIFWeIdhRDEmOwvpz8viMRkJmfabOleDPC3jM9pgvcWr5KAUwAQ; kuaishou.shop.b_ph=dc7ff5faa44a972a014870c3b0abb62e7ba2"
    
    # 示例时间（需要替换为实际时间）
    begin_time = readable_to_timestamp("2025-01-30 00:00:00")  # 活动开始时间
    end_time = readable_to_timestamp("2025-02-28 23:59:59")    # 活动结束时间
    
    # 示例专属用户列表
    exclusive_users = ["1"]
    
    print(f"活动开始时间: {timestamp_to_readable(begin_time)}")
    print(f"活动结束时间: {timestamp_to_readable(end_time)}")
    print(f"专属用户列表: {exclusive_users}")
    print("-" * 50)
    
    # 调用API创建活动
    result = create_kuaishou_activity(
        cookie=sample_cookie,
        activity_begin_time=begin_time,
        activity_end_time=end_time,
        activity_exclusive_user_list=exclusive_users,
        activity_title="测试活动",
        proxy="http://127.0.0.1:7890"  # 如果需要代理
    )
    
    # 检查结果
    if not result.get("error") and result.get("result") == 1:
        activity_id = result.get("data", {}).get("activityId")
        print(f"\n✅ 活动创建成功！活动ID: {activity_id}")
    else:
        print(f"\n❌ 活动创建失败！")
