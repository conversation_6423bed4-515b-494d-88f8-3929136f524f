-- 达人状态转换配置表
CREATE TABLE `talent_conversion_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_type` varchar(50) NOT NULL COMMENT '规则类型：special_upgrade, special_downgrade, exclusive_downgrade, shared_conversion',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_description` text COMMENT '规则描述',
  `check_days` int NOT NULL COMMENT '检查天数',
  `gmv_threshold` decimal(15,2) NOT NULL COMMENT 'GMV阈值（元）',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用（0:禁用，1:启用）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_type` (`rule_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='达人状态转换配置表';

-- 插入默认配置数据
INSERT INTO `talent_conversion_config` (`rule_type`, `rule_name`, `rule_description`, `check_days`, `gmv_threshold`, `is_enabled`) VALUES
('special_upgrade', '专享达人升级规则', '专享达人在指定天数内GMV达到阈值升级为专属达人', 15, 10000.00, 1),
('special_downgrade', '专享达人降级规则', '专享达人在指定天数内GMV未达到阈值降至公海', 15, 10000.00, 1),
('exclusive_downgrade', '专属达人降级规则', '专属达人在指定天数内GMV未达到阈值降至公海', 30, 100000.00, 1),
('shared_conversion', '共享达人转换规则', '共享达人中商务在指定天数内GMV达到阈值获得专享权', 30, 50000.00, 1);
