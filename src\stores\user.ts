import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export interface User {
  id: number
  username: string
  name: string
  is_admin: boolean
}

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)

  // 设置用户信息
  function setUser(userInfo: User) {
    user.value = userInfo
  }

  // 设置token
  function setToken(tokenValue: string) {
    token.value = tokenValue
  }

  // 清除用户信息
  function clearUser() {
    user.value = null
    token.value = null
  }

  // 获取当前商务名称
  const businessName = computed(() => user.value?.name || '')

  // 检查是否已登录
  const isLoggedIn = computed(() => !!user.value && !!token.value)

  // 检查是否是管理员
  const isAdmin = computed(() => user.value?.is_admin || false)

  return {
    user,
    token,
    setUser,
    setToken,
    clearUser,
    businessName,
    isLoggedIn,
    isAdmin,
  }
})
