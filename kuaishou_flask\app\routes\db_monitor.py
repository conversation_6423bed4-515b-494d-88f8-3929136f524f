"""
数据库监控API路由
"""

from flask import Blueprint, jsonify
from app.utils.db_utils import get_pool_stats, log_pool_stats
import logging

db_monitor_bp = Blueprint('database_monitor', __name__, url_prefix='/api/db')

@db_monitor_bp.route('/status', methods=['GET'])
def get_database_status():
    """获取数据库连接池状态"""
    try:
        stats = get_pool_stats()

        # 计算额外统计信息
        active_connections = stats.get('active_connections', 0)
        total_requests = stats.get('total_requests', 0)
        failed_requests = stats.get('failed_requests', 0)
        failure_rate = (failed_requests / max(total_requests, 1)) * 100

        # 判断健康状态
        health_status = "healthy"
        if active_connections >= 30:
            health_status = "critical"
        elif active_connections >= 28:
            health_status = "warning"
        elif failure_rate > 5:
            health_status = "warning"

        # 构建完整的统计信息
        pool_stats = {
            'pool_size': 32,
            'active_connections': active_connections,
            'available_connections': 32 - active_connections,
            'total_requests': total_requests,
            'failed_requests': failed_requests,
            'failure_rate': round(failure_rate, 2)
        }

        return jsonify({
            'code': 0,
            'message': '获取数据库状态成功',
            'data': {
                'health_status': health_status,
                'pool_stats': pool_stats,
                'recommendations': _get_recommendations(pool_stats)
            }
        })

    except Exception as e:
        logging.error(f"获取数据库状态失败: {e}")
        return jsonify({
            'code': 500,
            'message': f'获取数据库状态失败: {str(e)}',
            'data': None
        }), 500

@db_monitor_bp.route('/cleanup', methods=['POST'])
def cleanup_database_connections():
    """清理数据库连接池（简化版）"""
    try:
        # 简单的清理操作：记录当前状态
        log_pool_stats()

        return jsonify({
            'code': 0,
            'message': '数据库连接池状态已记录，建议重启应用以完全清理连接池',
            'data': None
        })

    except Exception as e:
        logging.error(f"清理数据库连接池失败: {e}")
        return jsonify({
            'code': 500,
            'message': f'清理数据库连接池失败: {str(e)}',
            'data': None
        }), 500

@db_monitor_bp.route('/log-stats', methods=['POST'])
def log_database_stats():
    """手动记录数据库统计信息"""
    try:
        log_pool_stats()
        return jsonify({
            'code': 0,
            'message': '数据库统计信息已记录',
            'data': None
        })
        
    except Exception as e:
        logging.error(f"记录数据库统计信息失败: {e}")
        return jsonify({
            'code': 500,
            'message': f'记录数据库统计信息失败: {str(e)}',
            'data': None
        }), 500

def _get_recommendations(stats):
    """根据统计信息提供优化建议"""
    recommendations = []
    
    if stats['active_connections'] >= 30:
        recommendations.append("连接池几乎耗尽，建议立即检查是否有连接泄漏")
        recommendations.append("考虑重启应用程序以重置连接池")
        
    elif stats['active_connections'] >= 28:
        recommendations.append("连接池使用率过高，建议检查长时间运行的查询")
        recommendations.append("确保所有数据库连接都正确关闭")
        
    if stats['failure_rate'] > 10:
        recommendations.append("连接失败率过高，建议检查数据库服务器状态")
        recommendations.append("考虑增加连接重试机制")
        
    elif stats['failure_rate'] > 5:
        recommendations.append("连接失败率较高，建议监控数据库性能")
        
    if stats['total_requests'] > 10000 and stats['failed_requests'] == 0:
        recommendations.append("连接池运行良好，无需特殊处理")
        
    if not recommendations:
        recommendations.append("连接池状态正常")
        
    return recommendations
